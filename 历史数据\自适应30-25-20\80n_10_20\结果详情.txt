test.py                                                                                                                                    正在读取文件: results_A-n80-k2-d4.vrp_10runs.pkl...
成功读取了10次运行的结果。

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 739.69
最小总成本: 697.80 (运行 6)
最大总成本: 831.12 (运行 7)
总成本标准差: 46.85

========== 算法精度与稳定性分析 ==========
最大偏差: 133.32 (19.11%)
平均偏差: 36.46 (5.23%)
平均求解时间: 174.06秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |         11 | 0.001204 |   830.58 |        0 | 176.21秒   |
+----------+------------+----------+----------+----------+------------+
|        2 |         12 | 0.001377 |   726.17 |        0 | 161.51秒   |
+----------+------------+----------+----------+----------+------------+
|        3 |         13 | 0.001362 |   734.3  |        0 | 165.25秒   |
+----------+------------+----------+----------+----------+------------+
|        4 |         14 | 0.001379 |   725.42 |        0 | 175.85秒   |
+----------+------------+----------+----------+----------+------------+
|        5 |         15 | 0.001385 |   721.79 |        0 | 178.47秒   |
+----------+------------+----------+----------+----------+------------+
|        6 |         16 | 0.001433 |   697.8  |        0 | 185.19秒   |
+----------+------------+----------+----------+----------+------------+
|        7 |         17 | 0.001203 |   831.12 |        0 | 157.62秒   |
+----------+------------+----------+----------+----------+------------+
|        8 |         18 | 0.001425 |   701.92 |        0 | 180.02秒   |
+----------+------------+----------+----------+----------+------------+
|        9 |         19 | 0.001413 |   707.69 |        0 | 169.84秒   |
+----------+------------+----------+----------+----------+------------+
|       10 |         20 | 0.001389 |   720.12 |        0 | 190.63秒   |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 6) ==========
适应度: 0.001433
总成本: 697.80
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 42, 40, 21, 1, 62, 12, 44, 5, 63, 74, 11, 14, 28, 52, 68, 43, 16, 61, 20, 57, 75, 78, 19, 26, 47, 25, 33, 55, 46, 54, 76, 38,
 58, 53, 0, 35, 49, 73, 66, 67, 77, 13, 70, 32, 4, 45, 22, 50, 72, 39, 29, 64, 17, 27, 59, 6, 24, 30, 34, 79, 48, 18, 71, 10, 7, 0, 51, 60, 3, 31, 36, 15, 56, 9, 41, 65, 69, 37, 2, 8, 23, 0)                                                                                          服务方式层: (0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0)               
染色体解码结果:
  车辆 1 路径: [0, 42, 40, 21, 1, 62, 12, 44, 5, 11, 14, 28, 52, 68, 43, 16, 61, 20, 57, 19, 25, 33, 54, 76, 38, 58, 53, 0]
    无人机任务:
      发射点 5 → 任务: [63, 74]
      发射点 57 → 任务: [75, 78]
      发射点 19 → 任务: [26, 47]
      发射点 33 → 任务: [55, 46]
  车辆 2 路径: [0, 35, 49, 73, 66, 67, 70, 32, 4, 45, 22, 50, 72, 39, 17, 27, 59, 6, 34, 79, 48, 71, 10, 7, 0]
    无人机任务:
      发射点 67 → 任务: [77, 13]
      发射点 39 → 任务: [29, 64]
      发射点 6 → 任务: [24, 30]
      发射点 48 → 任务: [18]
  车辆 3 路径: [0, 51, 60, 3, 15, 41, 37, 2, 8, 23, 0]
    无人机任务:
      发射点 3 → 任务: [31, 36]
      发射点 15 → 任务: [56, 9]
      发射点 41 → 任务: [65, 69]

程序执行完毕。



