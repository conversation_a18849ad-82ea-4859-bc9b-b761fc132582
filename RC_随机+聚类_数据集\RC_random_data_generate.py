import random
import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from typing import List, Dict, <PERSON><PERSON>

def read_solomon_file(file_path: str) -> Dict:
    """读取Solomon格式的VRP文件"""
    data = {
        'nodes': {},
        'demands': {},
        'vehicle_capacity': 200
    }
    
    current_section = None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return data
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测当前处理的部分
        if 'VEHICLE' in line:
            current_section = 'vehicle'
            continue
        elif 'CUSTOMER' in line:
            current_section = 'customer'
            continue
        elif 'NUMBER' in line or 'CAPACITY' in line or 'CUST NO.' in line:
            continue
            
        # 处理车辆容量信息
        if current_section == 'vehicle':
            parts = line.split()
            if len(parts) >= 2 and parts[0].isdigit():
                data['vehicle_capacity'] = int(parts[1])
        
        # 处理Solomon格式的客户数据
        elif current_section == 'customer':
            try:
                parts = line.split()
                if len(parts) >= 7:
                    node_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    demand = float(parts[3])
                    
                    # 保存节点坐标
                    data['nodes'][node_id] = (x_coord, y_coord)
                    
                    # 保存需求信息
                    data['demands'][node_id] = demand
            except (ValueError, IndexError):
                continue
    
    print(f"成功读取 {len(data['nodes'])} 个节点")
    return data

def scale_coordinates(data: Dict, scale_factor: float) -> Dict:
    """缩放节点坐标"""
    scaled_data = data.copy()
    scaled_nodes = {}
    
    for node_id, (x, y) in data['nodes'].items():
        scaled_nodes[node_id] = (x * scale_factor, y * scale_factor)
    
    scaled_data['nodes'] = scaled_nodes
    return scaled_data

def analyze_distribution_with_dbscan(nodes_dict: Dict[int, Tuple[float, float]]) -> Dict:
    """使用DBSCAN分析数据分布，识别聚类和随机点"""
    # 排除配送中心(ID=0)
    customer_nodes = {k: v for k, v in nodes_dict.items() if k > 0}
    
    if not customer_nodes:
        return {'clusters': {}, 'random_nodes': [], 'n_clusters': 0}
    
    # 提取坐标
    node_ids = list(customer_nodes.keys())
    coords = np.array([customer_nodes[nid] for nid in node_ids])
    
    # 使用DBSCAN识别聚类
    total_points = len(coords)
    
    # 根据数据规模调整DBSCAN参数
    if total_points <= 30:
        eps = 8.0
        min_samples = 3
    elif total_points <= 60:
        eps = 10.0
        min_samples = 4
    else:
        eps = 12.0
        min_samples = 5
    
    dbscan = DBSCAN(eps=eps, min_samples=min_samples)
    labels = dbscan.fit_predict(coords)
    
    # 整理聚类结果
    clusters = {}
    random_nodes = []
    
    for i, label in enumerate(labels):
        if label == -1:
            # 噪声点（随机点）
            random_nodes.append(node_ids[i])
        else:
            # 聚类点
            if label not in clusters:
                clusters[label] = []
            clusters[label].append(node_ids[i])
    
    n_clusters = len(clusters)
    
    print(f"DBSCAN分析结果: {n_clusters}个聚类, {len(random_nodes)}个随机点")
    for cluster_id, nodes in clusters.items():
        print(f"  聚类 {cluster_id}: {len(nodes)} 个点")
    
    return {
        'clusters': clusters,
        'random_nodes': random_nodes,
        'n_clusters': n_clusters
    }

def select_mixed_distribution_points(distribution_analysis: Dict, 
                                   all_customer_ids: List[int],
                                   target_size: int) -> List[int]:
    """
    根据分布分析结果选择保持混合分布特性的客户点
    
    参数:
        distribution_analysis: 分布分析结果
        all_customer_ids: 所有客户点ID列表
        target_size: 目标客户点数量
    
    返回:
        选中的客户点ID列表
    """
    clusters = distribution_analysis.get('clusters', {})
    random_nodes = distribution_analysis.get('random_nodes', [])
    n_clusters = distribution_analysis.get('n_clusters', 0)
    
    # 根据目标规模确定聚类和随机点的比例
    if target_size <= 20:
        # 小规模：保持1-2个聚类，15-25%随机点
        target_clusters = min(2, n_clusters)
        random_ratio = 0.2
    elif target_size <= 50:
        # 中规模：保持2-3个聚类，20-30%随机点
        target_clusters = min(3, n_clusters)
        random_ratio = 0.25
    else:
        # 大规模：保持3-5个聚类，25-35%随机点
        target_clusters = min(5, n_clusters)
        random_ratio = 0.3
    
    # 计算各部分的点数
    target_random_count = max(1, int(target_size * random_ratio))
    target_cluster_count = target_size - target_random_count
    
    selected_nodes = []
    
    # 1. 选择随机点
    if random_nodes:
        if len(random_nodes) <= target_random_count:
            selected_nodes.extend(random_nodes)
        else:
            selected_nodes.extend(random.sample(random_nodes, target_random_count))
    
    # 2. 选择聚类点
    if clusters and target_cluster_count > 0:
        # 选择最大的几个聚类
        sorted_clusters = sorted(clusters.items(), key=lambda x: len(x[1]), reverse=True)
        selected_cluster_ids = [cid for cid, _ in sorted_clusters[:target_clusters]]
        
        # 从选中的聚类中按比例采样
        total_cluster_points = sum(len(clusters[cid]) for cid in selected_cluster_ids)
        
        for cluster_id in selected_cluster_ids:
            cluster_nodes = clusters[cluster_id]
            # 计算这个聚类应该选择的点数
            cluster_ratio = len(cluster_nodes) / total_cluster_points
            cluster_sample_count = max(1, round(target_cluster_count * cluster_ratio))
            cluster_sample_count = min(cluster_sample_count, len(cluster_nodes))
            
            if cluster_sample_count >= len(cluster_nodes):
                # 如果聚类很小，全部选择
                selected_nodes.extend(cluster_nodes)
            else:
                # 随机选择部分点
                selected_nodes.extend(random.sample(cluster_nodes, cluster_sample_count))
    
    # 3. 如果选择的点数不够，从剩余点中随机补充
    if len(selected_nodes) < target_size:
        remaining_nodes = [nid for nid in all_customer_ids if nid not in selected_nodes]
        additional_count = target_size - len(selected_nodes)
        if remaining_nodes and additional_count > 0:
            additional_nodes = random.sample(remaining_nodes, 
                                           min(additional_count, len(remaining_nodes)))
            selected_nodes.extend(additional_nodes)
    
    # 4. 如果选择的点数过多，随机移除一些
    if len(selected_nodes) > target_size:
        selected_nodes = random.sample(selected_nodes, target_size)
    
    return sorted(selected_nodes)

def generate_demand_data(customer_count: int) -> List[Tuple[float, float, str]]:
    """
    生成需求数据
    
    参数:
        customer_count: 客户点数量
    
    返回:
        需求数据列表，每个元素为(delivery, pickup, comment)
    """
    # 计算各类客户数量
    both_demands_count = int(customer_count * 0.25)  # 25%有送货和取货需求
    delivery_only_count = int(customer_count * 0.55)  # 55%仅有送货需求
    pickup_only_count = customer_count - both_demands_count - delivery_only_count  # 剩余为仅取货需求
    
    # 创建需求类型列表
    demand_types = (['both'] * both_demands_count + 
                   ['delivery'] * delivery_only_count + 
                   ['pickup'] * pickup_only_count)
    random.shuffle(demand_types)
    
    demands = []
    
    for i in range(customer_count):
        demand_type = demand_types[i]
        
        # 根据包裹重量分布生成重量
        weight_type = random.choices(
            ['light', 'medium', 'heavy'],
            weights=[0.8, 0.15, 0.05],
            k=1
        )[0]
        
        # 生成具体重量
        if weight_type == 'light':
            weight = round(random.uniform(1, 5), 1)
        elif weight_type == 'medium':
            weight = round(random.uniform(5.1, 10), 1)
        else:  # heavy
            weight = round(random.uniform(10.1, 20), 1)
        
        # 根据需求类型设置送货和取货重量
        if demand_type == 'both':
            delivery = weight
            pickup = round(random.uniform(1, weight), 1)
            comment = f"// 送货取货双需求客户 - {weight_type}包裹"
        elif demand_type == 'delivery':
            delivery = weight
            pickup = 0.0
            comment = f"// 仅送货需求客户 - {weight_type}包裹"
        else:  # pickup only
            delivery = 0.0
            pickup = weight
            comment = f"// 仅取货需求客户 - {weight_type}包裹"
        
        demands.append((delivery, pickup, comment))
    
    return demands

def write_vrp_file(data: Dict, output_path: str) -> None:
    """写入VRP文件"""
    content = []
    
    # 文件头部
    content.append(f"NAME : {data['name']}")
    content.append(f"COMMENT : {data['comment']}")
    content.append(f"TYPE : {data['type']}")
    content.append(f"DIMENSION : {data['dimension']}")
    content.append(f"EDGE_WEIGHT_TYPE : {data['edge_weight_type']}")
    content.append(f"NUM_CUSTOMERS : {data['num_customers']}")
    content.append("")
    
    # 节点坐标部分
    content.append("NODE_COORD_SECTION")
    sorted_nodes = sorted(data['nodes'].items())
    for node_id, (x, y) in sorted_nodes:
        content.append(f" {node_id} {x:.1f} {y:.1f}")
    content.append("")
    
    # 需求部分
    content.append("DEMAND_SECTION")
    content.append(" 0 0 0      ")  # 配送中心需求为0
    
    sorted_demands = sorted(data['demands'].items())
    for node_id, (delivery, pickup, comment) in sorted_demands:
        content.append(f" {node_id} {delivery} {pickup}      {comment}")
    
    content.append("")
    
    # 配送中心部分
    content.append("DEPOT_SECTION")
    content.append(" 0")
    content.append("-1")
    content.append("EOF")
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(content))

def visualize_distribution(original_nodes: Dict[int, Tuple[float, float]], 
                         selected_nodes: List[int],
                         distribution_analysis: Dict,
                         title: str) -> None:
    """可视化数据分布"""
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建输出目录
        output_dir = "distribution_plots"
        os.makedirs(output_dir, exist_ok=True)
        
        plt.figure(figsize=(12, 8))
        
        # 绘制所有原始客户点（淡色）
        customer_nodes = {k: v for k, v in original_nodes.items() if k > 0}
        if customer_nodes:
            x_all = [coord[0] for coord in customer_nodes.values()]
            y_all = [coord[1] for coord in customer_nodes.values()]
            plt.scatter(x_all, y_all, c='lightgray', s=20, alpha=0.4, label='原始客户点')
        
        # 绘制配送中心
        if 0 in original_nodes:
            depot_x, depot_y = original_nodes[0]
            plt.scatter(depot_x, depot_y, c='red', marker='*', s=200, 
                       edgecolor='black', label='配送中心')
        
        # 绘制选中的点，区分聚类点和随机点
        clusters = distribution_analysis.get('clusters', {})
        random_nodes = distribution_analysis.get('random_nodes', [])
        
        # 绘制选中的聚类点
        colors = ['blue', 'green', 'orange', 'purple', 'brown']
        cluster_count = 0
        for cluster_id, cluster_nodes in clusters.items():
            selected_in_cluster = [n for n in cluster_nodes if n in selected_nodes]
            if selected_in_cluster:
                x_cluster = [original_nodes[n][0] for n in selected_in_cluster]
                y_cluster = [original_nodes[n][1] for n in selected_in_cluster]
                color = colors[cluster_count % len(colors)]
                plt.scatter(x_cluster, y_cluster, c=color, s=80, alpha=0.8,
                           marker='o', edgecolor='black', 
                           label=f'聚类{cluster_id} ({len(selected_in_cluster)}个)')
                cluster_count += 1
        
        # 绘制选中的随机点
        selected_random = [n for n in random_nodes if n in selected_nodes]
        if selected_random:
            x_random = [original_nodes[n][0] for n in selected_random]
            y_random = [original_nodes[n][1] for n in selected_random]
            plt.scatter(x_random, y_random, c='red', s=60, alpha=0.8,
                       marker='^', edgecolor='black', 
                       label=f'随机点 ({len(selected_random)}个)')
        
        plt.title(f'混合分布数据集 - {title}')
        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 保存图像
        clean_title = title.replace(' ', '_').replace('-', '_')
        plt.savefig(f"{output_dir}/{clean_title}.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存分布图: {clean_title}.png")
        
    except Exception as e:
        print(f"生成分布图时出错: {e}")

def generate_rc_dataset(original_data: Dict, 
                       customer_count: int, 
                       instance_id: int, 
                       output_dir: str,
                       scale_factor: float = 0.3) -> str:
    """
    生成RC类型数据集
    
    参数:
        original_data: 原始数据
        customer_count: 目标客户点数量
        instance_id: 实例编号
        output_dir: 输出目录
        scale_factor: 坐标缩放因子
    
    返回:
        生成的文件路径
    """
    # 缩放坐标
    scaled_data = scale_coordinates(original_data, scale_factor)
    
    # 分析分布特征
    distribution_analysis = analyze_distribution_with_dbscan(scaled_data['nodes'])
    
    # 获取所有客户点ID
    all_customer_ids = [nid for nid in scaled_data['nodes'].keys() if nid > 0]
    
    # 选择客户点保持混合分布特性
    selected_customers = select_mixed_distribution_points(
        distribution_analysis, all_customer_ids, customer_count)
    
    print(f"选择了 {len(selected_customers)} 个客户点")
    
    # 生成需求数据
    demand_data = generate_demand_data(customer_count)
    
    # 创建新数据集
    new_data = {
        'name': f"RC101-{customer_count+1}-{instance_id}",
        'comment': "(Solomon dataset, Modified for VRPD with scaled coordinates)",
        'type': "VRPD",
        'dimension': customer_count + 1,
        'edge_weight_type': "EUC_2D",
        'num_customers': customer_count,
        'nodes': {0: scaled_data['nodes'][0]},  # 配送中心
        'demands': {}
    }
    
    # 添加选中的客户点（重新编号）
    for new_id, old_id in enumerate(selected_customers, 1):
        new_data['nodes'][new_id] = scaled_data['nodes'][old_id]
        new_data['demands'][new_id] = demand_data[new_id - 1]
    
    # 生成输出文件路径
    filename = f"RC101-{customer_count+1}-{instance_id}.vrp"
    output_path = os.path.join(output_dir, filename)
    
    # 写入文件
    write_vrp_file(new_data, output_path)
    
    # 可视化
    visualize_distribution(scaled_data['nodes'], selected_customers, 
                         distribution_analysis, new_data['name'])
    
    print(f"已生成数据集: {output_path}")
    return output_path

def main():
    """主函数"""
    # 设置参数
    original_file = os.path.join(os.path.dirname(__file__), "5_Solomom_RC101.vrp")
    output_dir = os.path.join(os.path.dirname(__file__), "RC_随机+聚类_数据集")
    os.makedirs(output_dir, exist_ok=True)
    
    # 可调整的参数
    scale_factor = 0.2  # 坐标缩放因子
    dataset_sizes = [20, 30, 50, 60, 80, 100]  # 要生成的数据集规模
    instances_per_size = 3  # 每个规模生成的实例数
    
    # 读取原始数据
    print(f"读取原始数据文件: {original_file}")
    original_data = read_solomon_file(original_file)
    
    if not original_data['nodes']:
        print("错误: 未能读取到节点数据")
        return
    
    print(f"原始数据包含 {len(original_data['nodes'])} 个节点")
    
    # 生成各规模的数据集
    for size in dataset_sizes:
        print(f"\n=== 生成规模为 {size} 的数据集 ===")
        
        for instance in range(1, instances_per_size + 1):
            print(f"\n生成实例 {instance}...")
            
            try:
                output_file = generate_rc_dataset(
                    original_data, size, instance, output_dir, scale_factor)
                print(f"成功生成: {os.path.basename(output_file)}")
            except Exception as e:
                print(f"生成实例 {instance} 时出错: {e}")
        
        print(f"完成规模 {size} 的所有实例")

if __name__ == "__main__":
    main()

