80n_1_10
========== 30\25\20 ==========
========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 747.74
最小总成本: 659.08 (运行 1)
最大总成本: 826.03 (运行 6)
总成本标准差: 50.51

========== 算法精度与稳定性分析 ==========
最大偏差: 166.95 (25.33%)
平均偏差: 42.02 (6.38%)
平均求解时间: 186.32秒

========== 30\28\25 ==========                        比对结果：30\28\25相对精度比较高，最大偏差约低14%，平均偏差约低3%。平均求解时间节约约9秒。
========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 763.49
最小总成本: 715.61 (运行 8)
最大总成本: 797.56 (运行 5)
总成本标准差: 26.87

========== 算法精度与稳定性分析 ==========
最大偏差: 81.95 (11.45%)
平均偏差: 23.16 (3.24%)
平均求解时间: 177.62秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 | 0.001352 |   739.53 |        0 | 184.84秒   |
+----------+------------+----------+----------+----------+------------+
|        2 |          2 | 0.00139  |   719.52 |        0 | 201.57秒   |
+----------+------------+----------+----------+----------+------------+
|        3 |          3 | 0.00128  |   781    |        0 | 198.42秒   |
+----------+------------+----------+----------+----------+------------+
|        4 |          4 | 0.001282 |   779.92 |        0 | 172.07秒   |
+----------+------------+----------+----------+----------+------------+
|        5 |          5 | 0.001254 |   797.56 |        0 | 170.61秒   |
+----------+------------+----------+----------+----------+------------+
|        6 |          6 | 0.001295 |   772.33 |        0 | 168.41秒   |
+----------+------------+----------+----------+----------+------------+
|        7 |          7 | 0.001288 |   776.42 |        0 | 168.29秒   |
+----------+------------+----------+----------+----------+------------+
|        8 |          8 | 0.001397 |   715.61 |        0 | 157.13秒   |
+----------+------------+----------+----------+----------+------------+
|        9 |          9 | 0.001276 |   783.41 |        0 | 167.82秒   |
+----------+------------+----------+----------+----------+------------+
|       10 |         10 | 0.001299 |   769.62 |        0 | 187.05秒   |
+----------+------------+----------+----------+----------+------------+

第二次各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 | 0.001352 |   739.53 |        0 | 167.23秒   |
+----------+------------+----------+----------+----------+------------+
|        2 |          2 | 0.00139  |   719.52 |        0 | 179.16秒   |
+----------+------------+----------+----------+----------+------------+
|        3 |          3 | 0.00128  |   781    |        0 | 183.31秒   |
+----------+------------+----------+----------+----------+------------+
|        4 |          4 | 0.001282 |   779.92 |        0 | 172.48秒   |
+----------+------------+----------+----------+----------+------------+
|        5 |          5 | 0.001254 |   797.56 |        0 | 171.21秒   |
+----------+------------+----------+----------+----------+------------+
|        6 |          6 | 0.001295 |   772.33 |        0 | 169.32秒   |
+----------+------------+----------+----------+----------+------------+
|        7 |          7 | 0.001288 |   776.42 |        0 | 171.25秒   |
+----------+------------+----------+----------+----------+------------+
|        8 |          8 | 0.001397 |   715.61 |        0 | 158.12秒   |
+----------+------------+----------+----------+----------+------------+
|        9 |          9 | 0.001276 |   783.41 |        0 | 169.13秒   |
+----------+------------+----------+----------+----------+------------+
|       10 |         10 | 0.001299 |   769.62 |        0 | 188.36秒   |
+----------+------------+----------+----------+----------+------------+


========== 最佳解详情 (运行 8) ==========
适应度: 0.001397
总成本: 715.61
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 42, 73, 49, 36, 67, 70, 53, 66, 58, 38, 32, 4, 50, 72, 76, 6, 2, 37, 8, 68, 16, 43, 61, 78, 23, 0, 51, 77, 3, 39, 60, 29, 1
2, 62, 63, 11, 52, 28, 79, 34, 24, 71, 14, 10, 7, 1, 21, 40, 0, 45, 22, 54, 55, 15, 33, 25, 41, 46, 64, 31, 17, 27, 59, 44, 74, 30, 48, 18, 9, 65, 26, 35, 47, 19, 69, 56, 75, 20, 57, 5, 13, 0)                                                                                    服务方式层: (0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0)           
染色体解码结果:
  车辆 1 路径: [0, 42, 73, 67, 66, 58, 32, 4, 50, 72, 6, 2, 37, 8, 68, 61, 78, 23, 0]
    无人机任务:
      发射点 73 → 任务: [49, 36]
      发射点 67 → 任务: [70, 53]
      发射点 58 → 任务: [38]
      发射点 72 → 任务: [76]
      发射点 68 → 任务: [16, 43]
  车辆 2 路径: [0, 51, 77, 3, 39, 60, 29, 12, 62, 63, 11, 52, 28, 79, 34, 24, 71, 10, 7, 1, 21, 40, 0]
    无人机任务:
      发射点 71 → 任务: [14]
  车辆 3 路径: [0, 45, 22, 54, 55, 15, 41, 46, 31, 17, 27, 59, 30, 48, 18, 9, 65, 47, 19, 75, 5, 13, 0]
    无人机任务:
      发射点 15 → 任务: [33, 25]
      发射点 46 → 任务: [64]
      发射点 59 → 任务: [44, 74]
      发射点 65 → 任务: [26, 35]
      发射点 19 → 任务: [69, 56]
      发射点 75 → 任务: [20, 57]

程序执行完毕。
