#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
drone_tasks 变量结构详细演示
展示在不同VRP-D场景下drone_tasks的完整数据结构
"""

from data_loader import DroneTask

def demonstrate_drone_tasks_structures():
    """演示各种drone_tasks数据结构"""
    print("=" * 80)
    print("drone_tasks 变量结构完整演示")
    print("=" * 80)
    
    print("drone_tasks是一个字典，键为发射点ID，值为该发射点的无人机任务信息")
    print("基本结构: {发射点ID: {'drone_tasks': [DroneTask对象列表], 'recovery_point': 回收点ID}}")
    print()

def scenario_1_simple_case():
    """场景1: 简单的VRP-D案例"""
    print("=" * 60)
    print("场景1: 简单VRP-D案例 (2辆卡车, 4架无人机)")
    print("=" * 60)
    
    # 卡车路径
    truck_routes = [
        [0, 5, 12, 18, 0],      # 卡车1
        [0, 8, 15, 22, 0]       # 卡车2
    ]
    
    print("卡车路径:")
    for i, route in enumerate(truck_routes, 1):
        print(f"  卡车{i}: {route}")
    print()
    
    # drone_tasks结构
    drone_tasks = {
        5: {  # 发射点5 (卡车1路径中)
            'drone_tasks': [
                DroneTask(
                    drone_id=1,
                    customer_sequence=[3],      # 无人机1服务客户3
                    launch_point=5,
                    recovery_point=12,
                    total_energy=45.6
                ),
                DroneTask(
                    drone_id=2,
                    customer_sequence=[7],      # 无人机2服务客户7
                    launch_point=5,
                    recovery_point=12,
                    total_energy=52.3
                )
            ],
            'recovery_point': 12  # 统一在节点12回收
        },
        15: {  # 发射点15 (卡车2路径中)
            'drone_tasks': [
                DroneTask(
                    drone_id=3,
                    customer_sequence=[11],     # 无人机3服务客户11
                    launch_point=15,
                    recovery_point=22,
                    total_energy=38.9
                ),
                DroneTask(
                    drone_id=4,
                    customer_sequence=[17],     # 无人机4服务客户17
                    launch_point=15,
                    recovery_point=22,
                    total_energy=41.2
                )
            ],
            'recovery_point': 22  # 统一在节点22回收
        }
    }
    
    print("drone_tasks 结构:")
    print("drone_tasks = {")
    for launch_point, task_info in drone_tasks.items():
        print(f"    {launch_point}: {{  # 发射点{launch_point}")
        print(f"        'drone_tasks': [")
        for task in task_info['drone_tasks']:
            route = task.get_route_sequence()
            print(f"            DroneTask(id={task.drone_id}, 客户={task.customer_sequence}, 路径={route}, 能耗={task.total_energy}),")
        print(f"        ],")
        print(f"        'recovery_point': {task_info['recovery_point']}  # 回收点")
        print(f"    }},")
    print("}")
    print()
    
    return drone_tasks

def scenario_2_multi_customer_chains():
    """场景2: 多客户服务链案例"""
    print("=" * 60)
    print("场景2: 多客户服务链 (高效配送模式)")
    print("=" * 60)
    
    # 更复杂的卡车路径
    truck_routes = [
        [0, 6, 14, 20, 28, 0],    # 卡车1 (长路径)
        [0, 4, 10, 16, 0],        # 卡车2 (短路径)
        [0, 8, 18, 25, 30, 0]     # 卡车3 (中等路径)
    ]
    
    print("卡车路径:")
    for i, route in enumerate(truck_routes, 1):
        print(f"  卡车{i}: {route}")
    print()
    
    # 多客户服务链的drone_tasks
    drone_tasks = {
        6: {  # 发射点6
            'drone_tasks': [
                DroneTask(
                    drone_id=1,
                    customer_sequence=[2, 5, 9, 12],    # 无人机1服务4个客户
                    launch_point=6,
                    recovery_point=14,
                    total_energy=156.8
                ),
                DroneTask(
                    drone_id=2,
                    customer_sequence=[3, 7],           # 无人机2服务2个客户
                    launch_point=6,
                    recovery_point=14,
                    total_energy=89.4
                )
            ],
            'recovery_point': 14
        },
        10: {  # 发射点10
            'drone_tasks': [
                DroneTask(
                    drone_id=3,
                    customer_sequence=[11, 13, 15, 17, 19],  # 无人机3服务5个客户！
                    launch_point=10,
                    recovery_point=16,
                    total_energy=234.7
                )
            ],
            'recovery_point': 16
        },
        18: {  # 发射点18
            'drone_tasks': [
                DroneTask(
                    drone_id=4,
                    customer_sequence=[21, 23],         # 无人机4服务2个客户
                    launch_point=18,
                    recovery_point=25,
                    total_energy=78.9
                ),
                DroneTask(
                    drone_id=5,
                    customer_sequence=[22, 24, 26, 27], # 无人机5服务4个客户
                    launch_point=18,
                    recovery_point=25,
                    total_energy=145.6
                )
            ],
            'recovery_point': 25
        }
    }
    
    print("多客户服务链 drone_tasks:")
    print("drone_tasks = {")
    total_customers = 0
    for launch_point, task_info in drone_tasks.items():
        print(f"    {launch_point}: {{  # 发射点{launch_point} → 回收点{task_info['recovery_point']}")
        print(f"        'drone_tasks': [")
        for task in task_info['drone_tasks']:
            route = task.get_route_sequence()
            customers_str = ' → '.join(map(str, task.customer_sequence))
            print(f"            # 无人机{task.drone_id}: {task.launch_point} → {customers_str} → {task.recovery_point}")
            print(f"            DroneTask({task.drone_id}, {task.customer_sequence}, {task.launch_point}, {task.recovery_point}, {task.total_energy}),")
            total_customers += task.num_customers
        print(f"        ],")
        print(f"        'recovery_point': {task_info['recovery_point']}")
        print(f"    }},")
    print("}")
    print(f"\n总计: {len([task for task_info in drone_tasks.values() for task in task_info['drone_tasks']])}架无人机服务{total_customers}个客户")
    print()
    
    return drone_tasks

def scenario_3_complex_mixed():
    """场景3: 复杂混合场景"""
    print("=" * 60)
    print("场景3: 复杂混合场景 (同步+异步+多客户)")
    print("=" * 60)
    
    drone_tasks = {
        3: {  # 同步模式：发射点=回收点
            'drone_tasks': [
                DroneTask(
                    drone_id=1,
                    customer_sequence=[1, 2],
                    launch_point=3,
                    recovery_point=3,           # 同步：同一点发射回收
                    total_energy=67.4
                )
            ],
            'recovery_point': 3
        },
        8: {  # 异步模式：发射点≠回收点
            'drone_tasks': [
                DroneTask(
                    drone_id=2,
                    customer_sequence=[5, 7, 9, 11, 13],  # 5个客户的长链
                    launch_point=8,
                    recovery_point=15,          # 异步：不同点回收
                    total_energy=198.7
                ),
                DroneTask(
                    drone_id=3,
                    customer_sequence=[6],      # 单客户
                    launch_point=8,
                    recovery_point=15,
                    total_energy=45.2
                )
            ],
            'recovery_point': 15
        },
        12: {  # 特殊情况：无回收点
            'drone_tasks': [
                DroneTask(
                    drone_id=4,
                    customer_sequence=[14, 16],
                    launch_point=12,
                    recovery_point=None,        # 特殊：无明确回收点
                    total_energy=89.3
                )
            ],
            'recovery_point': None
        },
        20: {  # 空任务（占位或错误处理）
            'drone_tasks': [
                DroneTask(
                    drone_id=5,
                    customer_sequence=[],       # 空客户序列
                    launch_point=20,
                    recovery_point=20,
                    total_energy=0.0
                )
            ],
            'recovery_point': 20
        }
    }
    
    print("复杂混合 drone_tasks:")
    print("drone_tasks = {")
    for launch_point, task_info in drone_tasks.items():
        print(f"    {launch_point}: {{")
        print(f"        'drone_tasks': [")
        for task in task_info['drone_tasks']:
            # 分析任务类型
            if task.launch_point == task.recovery_point:
                mode = "同步模式"
            elif task.recovery_point is None:
                mode = "无回收点"
            else:
                mode = "异步模式"
                
            if task.num_customers == 0:
                task_type = "空任务"
            elif task.num_customers == 1:
                task_type = "单客户"
            else:
                task_type = f"{task.num_customers}客户链"
            
            print(f"            # {mode}, {task_type}")
            print(f"            DroneTask({task.drone_id}, {task.customer_sequence}, {task.launch_point}, {task.recovery_point}, {task.total_energy}),")
        print(f"        ],")
        print(f"        'recovery_point': {task_info['recovery_point']}")
        print(f"    }},")
    print("}")
    print()
    
    return drone_tasks

def analyze_drone_tasks_structure(drone_tasks):
    """分析drone_tasks的结构特征"""
    print("=" * 60)
    print("drone_tasks 结构分析")
    print("=" * 60)
    
    total_launch_points = len(drone_tasks)
    total_drones = sum(len(task_info['drone_tasks']) for task_info in drone_tasks.values())
    total_customers = sum(task.num_customers for task_info in drone_tasks.values() 
                         for task in task_info['drone_tasks'])
    
    print(f"结构统计:")
    print(f"  发射点数量: {total_launch_points}")
    print(f"  无人机总数: {total_drones}")
    print(f"  服务客户总数: {total_customers}")
    print(f"  平均每个发射点的无人机数: {total_drones/total_launch_points:.1f}")
    print(f"  平均每架无人机服务客户数: {total_customers/total_drones:.1f}")
    print()
    
    print("详细分析:")
    for launch_point, task_info in drone_tasks.items():
        recovery_point = task_info['recovery_point']
        num_drones = len(task_info['drone_tasks'])
        customers_in_launch = sum(task.num_customers for task in task_info['drone_tasks'])
        
        if launch_point == recovery_point:
            mode = "同步"
        elif recovery_point is None:
            mode = "无回收"
        else:
            mode = "异步"
            
        print(f"  发射点{launch_point} → 回收点{recovery_point} ({mode}模式)")
        print(f"    无人机数: {num_drones}, 服务客户数: {customers_in_launch}")
        
        for task in task_info['drone_tasks']:
            route = task.get_route_sequence()
            print(f"    - 无人机{task.drone_id}: {' → '.join(map(str, route))} (能耗:{task.total_energy})")
    print()

def demonstrate_data_access_patterns():
    """演示drone_tasks的数据访问模式"""
    print("=" * 60)
    print("drone_tasks 数据访问模式")
    print("=" * 60)
    
    # 使用场景2的数据
    drone_tasks = scenario_2_multi_customer_chains()
    
    print("常见访问模式:")
    print()
    
    # 1. 遍历所有发射点
    print("1. 遍历所有发射点:")
    print("   for launch_point in drone_tasks.keys():")
    print("       print(f'发射点: {launch_point}')")
    print("   输出:")
    for launch_point in drone_tasks.keys():
        print(f"     发射点: {launch_point}")
    print()
    
    # 2. 访问特定发射点的信息
    print("2. 访问特定发射点的信息:")
    print("   launch_point = 6")
    print("   task_info = drone_tasks[6]")
    print("   recovery_point = task_info['recovery_point']")
    print("   drone_tasks_list = task_info['drone_tasks']")
    launch_point = 6
    task_info = drone_tasks[6]
    recovery_point = task_info['recovery_point']
    drone_tasks_list = task_info['drone_tasks']
    print(f"   结果: 发射点{launch_point}, 回收点{recovery_point}, {len(drone_tasks_list)}架无人机")
    print()
    
    # 3. 遍历所有无人机任务
    print("3. 遍历所有无人机任务:")
    print("   for launch_point, task_info in drone_tasks.items():")
    print("       for task in task_info['drone_tasks']:")
    print("           print(f'无人机{task.drone_id}: {task.customer_sequence}')")
    print("   输出:")
    for launch_point, task_info in drone_tasks.items():
        for task in task_info['drone_tasks']:
            print(f"     无人机{task.drone_id}: {task.customer_sequence}")
    print()
    
    # 4. 查找特定无人机
    print("4. 查找特定无人机:")
    print("   target_drone_id = 3")
    print("   for launch_point, task_info in drone_tasks.items():")
    print("       for task in task_info['drone_tasks']:")
    print("           if task.drone_id == target_drone_id:")
    print("               print(f'找到无人机{target_drone_id}: {task.get_route_sequence()}')")
    target_drone_id = 3
    for launch_point, task_info in drone_tasks.items():
        for task in task_info['drone_tasks']:
            if task.drone_id == target_drone_id:
                print(f"   结果: 找到无人机{target_drone_id}: {task.get_route_sequence()}")
    print()

if __name__ == "__main__":
    demonstrate_drone_tasks_structures()
    
    print("\n" + "="*80)
    drone_tasks_1 = scenario_1_simple_case()
    analyze_drone_tasks_structure(drone_tasks_1)
    
    print("\n" + "="*80)
    drone_tasks_2 = scenario_2_multi_customer_chains()
    analyze_drone_tasks_structure(drone_tasks_2)
    
    print("\n" + "="*80)
    drone_tasks_3 = scenario_3_complex_mixed()
    analyze_drone_tasks_structure(drone_tasks_3)
    
    print("\n" + "="*80)
    demonstrate_data_access_patterns()
