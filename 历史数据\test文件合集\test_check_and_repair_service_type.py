import unittest
from typing import Tuple, List
import sys
import os
import copy

# 添加项目根目录到路径，确保可以导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_loader import Problem, Individual
from repair import check_and_repair_service_type

class TestCheckAndRepairServiceType(unittest.TestCase):
    
    def setUp(self):
        """初始化测试环境，创建模拟问题实例"""
        # 创建一个简化的Problem实例
        self.problem = type('MockProblem', (), {
            'dimension': 14,             # 总节点数：0-13
            'num_vehicles': 2,           # 2辆卡车
            'num_drones': 2,             # 每辆卡车2架无人机
            'locations': {i: (i, i) for i in range(14)},
            'demands': {i: (1.0, 1.0) for i in range(14)},
            'drone_params': {'max_load': 5.0}
        })()
    
    def test_depot_with_wrong_service_type(self):
        """测试配送中心被错误分配为服务方式1的情况"""
        # 创建一个染色体，配送中心被错误地标记为服务方式1
        path_layer = [0, 1, 2, 3, 0, 4, 5, 6, 0]
        service_layer = [1, 0, 1, 0, 1, 0, 1, 0, 1]  # 所有0都被错误标记为1
        
        individual = Individual(chromosomes=(path_layer, service_layer))
        original_chromosomes = copy.deepcopy(individual.chromosomes)
        
        # 调用函数，预期返回False（表示修复了不可行解）
        result = check_and_repair_service_type(self.problem, individual)
        
        # 验证结果
        self.assertFalse(result, "应返回False表示修复了不可行解")
        
        # 验证所有配送中心的服务方式都被修改为0
        path_after, service_after = individual.chromosomes
        for i, node in enumerate(path_after):
            if node == 0:
                self.assertEqual(service_after[i], 0, f"配送中心在位置{i}的服务方式应为0")
        
        # 打印修复前后的对比
        print("\n测试配送中心服务方式修复:")
        print(f"修复前: {list(zip(original_chromosomes[0], original_chromosomes[1]))}")
        print(f"修复后: {list(zip(individual.chromosomes[0], individual.chromosomes[1]))}")

    def test_all_service_types_already_correct(self):
        """测试所有服务方式已经正确分配的情况"""
        # 创建一个染色体，所有配送中心都已正确标记为服务方式0
        path_layer = [0, 1, 2, 3, 0, 4, 5, 6, 0]
        service_layer = [0, 0, 1, 0, 0, 0, 1, 0, 0]  # 所有0都已正确标记为0
        
        individual = Individual(chromosomes=(path_layer, service_layer))
        original_chromosomes = copy.deepcopy(individual.chromosomes)
        
        # 调用函数，预期返回True（表示解已经是可行的）
        result = check_and_repair_service_type(self.problem, individual)
        
        # 验证结果
        self.assertTrue(result, "应返回True表示解已经是可行的")
        
        # 验证染色体保持不变
        self.assertEqual(individual.chromosomes, original_chromosomes, "染色体不应被修改")
        
        # 打印结果
        print("\n测试服务方式已正确:")
        print(f"原始染色体: {list(zip(original_chromosomes[0], original_chromosomes[1]))}")
        print(f"修复后染色体: {list(zip(individual.chromosomes[0], individual.chromosomes[1]))}")

    def test_two_route_complex_scenario(self):
        """测试包含两条卡车路径的复杂场景"""
        # 创建一个包含两条卡车路径的染色体，部分配送中心服务方式错误
        path_layer = [0, 1, 2, 3, 4, 5, 0, 6, 7, 8, 9, 10, 11, 12, 13, 0]
        service_layer = [1, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1]
        
        individual = Individual(chromosomes=(path_layer, service_layer))
        original_chromosomes = copy.deepcopy(individual.chromosomes)
        
        # 调用函数
        result = check_and_repair_service_type(self.problem, individual)
        
        # 验证结果
        self.assertFalse(result, "应返回False表示修复了不可行解")
        
        # 验证所有配送中心的服务方式都被修改为0
        path_after, service_after = individual.chromosomes
        for i, node in enumerate(path_after):
            if node == 0:
                self.assertEqual(service_after[i], 0, f"配送中心在位置{i}的服务方式应为0")
        
        # 打印修复前后的对比
        print("\n测试复杂场景修复:")
        print(f"修复前: {list(zip(original_chromosomes[0], original_chromosomes[1]))}")
        # print(f"修复前：{original_chromosomes}")
        print(f"修复后: {list(zip(individual.chromosomes[0], individual.chromosomes[1]))}")
        # print(f"修复后：{individual.chromosomes}")
        
        # 验证客户点的服务方式保持不变
        for i, (node, service) in enumerate(zip(original_chromosomes[0], original_chromosomes[1])):
            if node != 0:  # 非配送中心
                self.assertEqual(service, individual.chromosomes[1][i], "客户点的服务方式不应被修改")

if __name__ == '__main__':
    unittest.main()