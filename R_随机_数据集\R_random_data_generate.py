import random
import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
import matplotlib.font_manager as fm
from typing import List, Dict, Tuple, Set

def read_vrp_file(file_path: str) -> Dict:
    """读取VRP文件，提取节点坐标和需求信息"""
    
    data = {
        'header_info': [],
        'nodes': {},
        'demands': {}
    }
    
    current_section = None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return data
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测当前处理的部分
        if line in ['NODE_COORD_SECTION', 'DEMAND_SECTION', 'DEPOT_SECTION']:
            current_section = line
            continue
        elif line == 'CUSTOMER':
            current_section = 'CUSTOMER'
            continue
        elif line == 'VEHICLE':
            current_section = 'VEHICLE'
            continue
            
        # 处理节点坐标
        if current_section == 'NODE_COORD_SECTION':
            try:
                parts = line.split()
                if len(parts) >= 3:
                    node_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    data['nodes'][node_id] = (x_coord, y_coord)
            except (ValueError, IndexError):
                continue
                
        # 处理需求信息
        elif current_section == 'DEMAND_SECTION':
            try:
                parts = line.split()
                if len(parts) >= 3:
                    node_id = int(parts[0])
                    delivery = float(parts[1])
                    pickup = float(parts[2])
                    comment = " ".join(parts[3:]) if len(parts) > 3 else ""
                    data['demands'][node_id] = (delivery, pickup, comment)
            except (ValueError, IndexError):
                continue
        
        # 处理Solomon格式的客户数据
        elif current_section == 'CUSTOMER':
            # 跳过表头行
            if "CUST NO." in line or "XCOORD" in line or "YCOORD" in line:
                continue
                
            try:
                parts = line.split()
                if len(parts) >= 7:  # Solomon格式通常有7个或更多字段
                    node_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    demand = float(parts[3])
                    
                    # 保存节点坐标
                    data['nodes'][node_id] = (x_coord, y_coord)
                    
                    # 保存需求信息 (在Solomon格式中，通常只有送货需求)
                    data['demands'][node_id] = (demand, 0.0, "")
            except (ValueError, IndexError) as e:
                # 如果解析失败，尝试更灵活的方式
                try:
                    # 查找数字并提取
                    numbers = [float(p) for p in parts if p.replace('.', '', 1).isdigit()]
                    if len(numbers) >= 4:
                        node_id = int(numbers[0])
                        x_coord = numbers[1]
                        y_coord = numbers[2]
                        demand = numbers[3]
                        
                        data['nodes'][node_id] = (x_coord, y_coord)
                        data['demands'][node_id] = (demand, 0.0, "")
                except:
                    continue
    
    print(f"读取到 {len(data['nodes'])} 个节点和 {len(data['demands'])} 个需求记录")
    
    return data

def scale_coordinates(data: Dict, scale_factor: float = 0.6) -> Dict:
    """
    缩放所有节点坐标
    
    参数:
        data: 原始数据
        scale_factor: 缩放因子，默认为0.6
        
    返回:
        缩放后的数据
    """
    scaled_data = data.copy()
    scaled_nodes = {}
    
    for node_id, (x, y) in data['nodes'].items():
        scaled_nodes[node_id] = (x * scale_factor, y * scale_factor)
    
    scaled_data['nodes'] = scaled_nodes
    return scaled_data

def create_sampled_dataset(original_data: Dict, 
                          customer_count: int, 
                          instance_id: int, 
                          output_dir: str) -> str:
    """
    创建采样数据集
    
    参数:
        original_data: 原始数据集
        customer_count: 客户点数量
        instance_id: 实例ID（1, 2, 3等）
        output_dir: 输出目录
    
    返回:
        生成的文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 文件名
    filename = f"R101-{customer_count+1}-{instance_id}.vrp"
    output_path = os.path.join(output_dir, filename)
    
    # 提取配送中心（ID=0）
    depot = original_data['nodes'][0]
    depot_demand = original_data['demands'].get(0, (0, 0, "// 配送中心"))
    
    # 随机抽取客户点（不包括配送中心）
    customer_ids = list(range(1, len(original_data['nodes'])))
    selected_customers = random.sample(customer_ids, customer_count)
    
    # 排序，确保ID顺序
    selected_customers.sort()
    
    # 根据客户点比例划分需求类型
    num_both_demands = int(customer_count * 0.25)  # 25%有送货和取货需求
    num_delivery_only = int(customer_count * 0.55)  # 55%仅有送货需求
    num_pickup_only = customer_count - num_both_demands - num_delivery_only  # 剩余为仅取货需求
    
    # 随机分配需求类型
    demand_types = ['both'] * num_both_demands + ['delivery'] * num_delivery_only + ['pickup'] * num_pickup_only
    random.shuffle(demand_types)
    
    # 创建新的VRP文件内容 - 使用VRPD格式
    new_content = []
    
    # 添加文件头
    new_content.append(f"NAME : R101-{customer_count+1}-{instance_id}")
    new_content.append("COMMENT : (Solomon dataset, Modified for VRPD)")
    new_content.append("TYPE : VRPD")
    new_content.append(f"DIMENSION : {customer_count + 1}")
    new_content.append("EDGE_WEIGHT_TYPE : EUC_2D")
    new_content.append(f"NUM_CUSTOMERS : {customer_count}")

    
    # 添加节点坐标部分
    new_content.append("")
    new_content.append("NODE_COORD_SECTION")
    # 添加配送中心节点，坐标单位转为km
    new_content.append(f" 0 {depot[0]:.2f} {depot[1]:.2f}")
    
    # 重新编号客户点（从1开始）
    for new_id, orig_id in enumerate(selected_customers, 1):
        x, y = original_data['nodes'][orig_id]
        new_content.append(f" {new_id} {x:.2f} {y:.2f}")
    
    # 添加需求部分
    new_content.append("")
    new_content.append("DEMAND_SECTION")
    # 添加配送中心需求
    delivery, pickup, comment = depot_demand
    new_content.append(f" 0 {int(delivery)} {int(pickup)}      {comment}")
    
    # 为每个客户点生成需求
    for new_id in range(1, customer_count + 1):
        # 确定需求类型
        demand_type = demand_types[new_id - 1]
        
        # 根据包裹重量分布生成包裹重量
        weight_type = random.choices(
            ['light', 'medium', 'heavy'],
            weights=[0.8, 0.15, 0.05],
            k=1
        )[0]
        
        # 根据重量类型确定具体重量
        if weight_type == 'light':
            weight = round(random.uniform(1, 5), 1)  # 轻小件: 1-5kg
        elif weight_type == 'medium':
            weight = round(random.uniform(5.1, 10), 1)  # 中型件: 5.1-10kg
        else:
            weight = round(random.uniform(10.1, 20), 1)  # 大件: 10.1-20kg
        
        # 根据需求类型设置送货和取货重量
        if demand_type == 'both':
            delivery = weight
            pickup = round(random.uniform(1, weight), 1)  # 取货重量不超过送货重量
            comment = f"// 送货取货双需求客户 - {weight_type}包裹"
        elif demand_type == 'delivery':
            delivery = weight
            pickup = 0
            comment = f"// 仅送货需求客户 - {weight_type}包裹"
        else:  # pickup only
            delivery = 0
            pickup = weight
            comment = f"// 仅取货需求客户 - {weight_type}包裹"
            
        new_content.append(f" {new_id} {delivery:.1f} {pickup:.1f}      {comment}")
    
    # 添加配送中心部分
    new_content.append("")
    new_content.append("DEPOT_SECTION")
    new_content.append(" 0")
    new_content.append(" -1")
    new_content.append("EOF")
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(new_content))
    
    print(f"已创建数据集: {output_path}")
    
    # 验证生成的文件是否有足够的节点
    test_data = read_vrp_file(output_path)
    if len(test_data['nodes']) < 2:
        print(f"警告: 生成的数据集 {output_path} 节点数量不足")
    
    return output_path

def check_distribution(nodes_dict: Dict[int, Tuple[float, float]], title: str, output_dir: str = "distribution_plots") -> None:
    """检查节点分布并保存可视化图"""
    try:
        x_coords = [coord[0] for coord in nodes_dict.values()]
        y_coords = [coord[1] for coord in nodes_dict.values()]
        
        plt.figure(figsize=(10, 8))
        plt.scatter(x_coords, y_coords, c='blue', marker='o', alpha=0.7)
        
        # 标记配送中心
        if 0 in nodes_dict:
            plt.scatter(*nodes_dict[0], c='red', marker='*', s=300)
            
        plt.title(f"节点空间分布 - {title}")
        plt.xlabel("X坐标 (km)")
        plt.ylabel("Y坐标 (km)")
        plt.grid(True, alpha=0.3)
        
        # 添加节点标签
        for node_id, (x, y) in nodes_dict.items():
            if node_id == 0:
                plt.text(x, y+0.5, f"配送中心", fontsize=10, ha='center')
            else:
                plt.text(x, y+0.3, f"{node_id}", fontsize=8)
        
        os.makedirs(output_dir, exist_ok=True)
        plot_path = os.path.join(output_dir, f"{title.replace(' ', '_')}.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已生成节点分布图: {plot_path}")
        
        # 分析随机性 - 计算最近邻距离
        nearest_distances = []
        for i, (x1, y1) in nodes_dict.items():
            if i == 0:  # 跳过配送中心
                continue
                
            min_dist = float('inf')
            for j, (x2, y2) in nodes_dict.items():
                if i != j:
                    dist = np.sqrt((x1-x2)**2 + (y1-y2)**2)
                    min_dist = min(min_dist, dist)
            nearest_distances.append(min_dist)
        
        # 绘制最近邻距离分布
        plt.figure(figsize=(8, 6))
        plt.hist(nearest_distances, bins=10, alpha=0.7, color='green')
        plt.title(f"最近邻距离分布 - {title}")
        plt.xlabel("最近邻距离 (km)")
        plt.ylabel("频次")
        plt.grid(True, alpha=0.3)
        dist_path = os.path.join(output_dir, f"{title.replace(' ', '_')}_dist.png")
        plt.savefig(dist_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已生成距离分布图: {dist_path}")
    except Exception as e:
        print(f"生成分布图时出错: {e}")

def test_randomness(data_file: str) -> None:
    data = read_vrp_file(data_file)
    nodes = data['nodes']
    print(f"数据集 {data_file} 包含 {len(nodes)} 个节点")
    
    # 如果节点数量过少，直接返回
    if len(nodes) < 2:
        print("警告: 节点数量不足，无法计算距离")
        print("-" * 50)
        return

    # 提取坐标
    coords = np.array([(x, y) for x, y in nodes.values()])
    
    # K函数分析 (简化版)
    distances = []
    n = len(coords)
    for i in range(n):
        for j in range(i+1, n):
            dist = np.sqrt(np.sum((coords[i] - coords[j])**2))
            distances.append(dist)
    
    # 简单的随机性检查 - 打印统计信息
    distances = np.array(distances)
    print(f"数据集: {data_file}")
    
    # 检查是否有足够的数据点计算距离
    if len(distances) > 0:
        print(f"平均距离: {np.mean(distances):.2f}")
        print(f"距离方差: {np.var(distances):.2f}")
        print(f"距离中位数: {np.median(distances):.2f}")
        print(f"距离范围: {np.min(distances):.2f} - {np.max(distances):.2f}")
    else:
        print("警告: 没有可计算的距离（节点数量不足）")
    
    print("-" * 50)

def main():
    # 设置随机种子以确保可重复结果
    random.seed(42)
    
    # 原始数据文件
    original_file = "不知道为什么不能用的数据集/R_随机_数据集/1_Solomon_R101.vrp"
    # 输出目录
    output_dir = os.path.dirname(__file__)
    
    # 读取原始数据
    print(f"开始读取文件: {original_file}")
    original_data = read_vrp_file(original_file)
    
    # 缩放坐标 (乘以0.6，转换为km单位)
    scaled_data = scale_coordinates(original_data, 0.25)
    
    # 检查原始数据分布
    check_distribution(scaled_data['nodes'], "R101_原始数据集_缩放后(0.6倍)", 
                      os.path.join(output_dir, "distribution_plots"))
    
    # 要生成的数据集规模
    sizes = [20, 30, 50, 60, 80]
    
    # 每个规模生成3个实例
    for size in sizes:
        for instance in range(1, 4):
            # 创建新数据集
            output_file = create_sampled_dataset(scaled_data, size, instance, output_dir)
            
            # 检查新生成的数据集分布
            new_data = read_vrp_file(output_file)
            check_distribution(new_data['nodes'], f"R101-{size+1}-{instance}", 
                              os.path.join(output_dir, "distribution_plots"))
            
            # 测试随机性
            test_randomness(output_file)

if __name__ == "__main__":
    # 设置中文字体
    try:
        # 尝试设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong']
        plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
    except:
        print("警告: 无法设置中文字体，图形中的中文可能显示为方框")
    
    main()
