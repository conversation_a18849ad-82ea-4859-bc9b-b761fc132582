进/read_test.py
正在读取文件: results_A-n80-k2-d4.vrp_1runs.pkl...
成功读取了1次运行的结果。

========== 多次运行结果统计 ==========
运行次数: 1
平均总成本: 689.09
最小总成本: 689.09 (运行 1)
最大总成本: 689.09 (运行 1)
总成本标准差: 0.00

========== 算法精度与稳定性分析 ==========
最大偏差: 0.00 (0.00%)
平均偏差: 0.00 (0.00%)
平均求解时间: 477.12秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 | 0.001451 |   689.09 |        0 | 477.12秒   |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 1) ==========
适应度: 0.001451
总成本: 689.09
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 40, 21, 62, 24, 2, 37, 8, 43, 78, 16, 68, 25, 41, 33, 54, 4, 22, 45, 50, 76, 58, 32, 72, 64, 31, 17, 5, 23, 0, 47, 56,
 13, 29, 20, 61, 57, 26, 35, 65, 19, 75, 39, 60, 74, 34, 79, 48, 18, 28, 52, 11, 63, 10, 14, 71, 7, 1, 53, 0, 42, 66, 67, 70, 36, 38, 55, 69, 9, 15, 46, 59, 27, 6, 30, 44, 12, 77, 51, 3, 73, 49, 0)                                                                     服务方式层: (0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 
1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0) 
染色体解码结果:
  车辆 1 路径: [0, 40, 21, 62, 24, 2, 37, 8, 78, 25, 41, 33, 54, 4, 22, 45, 50, 76, 72, 64, 31, 17, 5, 23, 0]
    无人机任务:
      发射点 8 → 任务: [43]
      发射点 78 → 任务: [16, 68]
      发射点 76 → 任务: [58, 32]
  车辆 2 路径: [0, 47, 13, 29, 20, 61, 57, 26, 19, 75, 39, 60, 74, 34, 79, 48, 28, 52, 11, 63, 10, 7, 1, 53, 0]
    无人机任务:
      发射点 47 → 任务: [56]
      发射点 26 → 任务: [35, 65]
      发射点 48 → 任务: [18]
      发射点 10 → 任务: [14, 71]
  车辆 3 路径: [0, 42, 66, 67, 70, 55, 15, 46, 59, 27, 6, 30, 44, 12, 77, 73, 0]
    无人机任务:
      发射点 70 → 任务: [36, 38]
      发射点 55 → 任务: [69, 9]
      发射点 77 → 任务: [51, 3]
      发射点 73 → 任务: [49]

程序执行完毕。
