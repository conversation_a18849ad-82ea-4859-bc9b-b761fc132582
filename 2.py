from data_loader import Problem, Individual, DroneTask

# 创建一个 drone_tasks_extended 示例
drone_tasks_extended = {
    6: {
        'drone_tasks': [
            DroneTask(1, [4, 7, 11], 6, 12, 156.8),
            DroneTask(2, [3, 9], 6, 12, 89.4)
        ],
        'recovery_point': 12
    },
    15: {
        'drone_tasks': [
            DroneTask(3, [14, 17, 20], 15, 22, 134.5)
        ],
        'recovery_point': 22
    },
    25: {
        'drone_tasks': [
            DroneTask(4, [1, 8], 25, 30, 78.9)
        ],
        'recovery_point': 30
    }
}

print("原始 drone_tasks_extended:")
print(drone_tasks_extended)
print()

# 演示 1: 获取字典的所有键（发射点）
print("=== 演示 1: 获取字典的键 ===")
keys = drone_tasks_extended.keys()
print(f"字典的键: {keys}")
print(f"键的类型: {type(keys)}")
print()

# 演示 2: 将键转换为迭代器
print("=== 演示 2: 将键转换为迭代器 ===")
keys_iterator = iter(drone_tasks_extended.keys())
print(f"迭代器对象: {keys_iterator}")
print(f"迭代器类型: {type(keys_iterator)}")
print()

# 演示 3: 使用 next() 获取迭代器中的元素
print("=== 演示 3: 使用 next() 获取元素 ===")
try:
    first_key = next(keys_iterator)
    print(f"第一个键: {first_key}")
    
    second_key = next(keys_iterator)
    print(f"第二个键: {second_key}")
    
    third_key = next(keys_iterator)
    print(f"第三个键: {third_key}")
    
    # 尝试获取第四个元素（不存在）
    fourth_key = next(keys_iterator)
    print(f"第四个键: {fourth_key}")
    
except StopIteration:
    print("迭代器已耗尽，没有更多元素了！")
print()

# 演示 4: 获取字典的所有值
print("=== 演示 4: 获取字典的值 ===")
values = drone_tasks_extended.values()
print(f"字典的值: {list(values)}")  # 转换为列表以便查看
print()

# 演示 5: 获取第一个值（这就是代码中的操作）
print("=== 演示 5: 获取第一个值（代码中的实际操作）===")
first_value = next(iter(drone_tasks_extended.values()))
print(f"第一个值: {first_value}")
print(f"第一个值的类型: {type(first_value)}")
print()

# 演示 6: 检查第一个值的格式
print("=== 演示 6: 检查第一个值的格式 ===")
if isinstance(first_value, dict) and 'drone_tasks' in first_value:
    print("✓ 这是扩展格式")
    print(f"  包含的键: {first_value.keys()}")
    print(f"  drone_tasks 数量: {len(first_value['drone_tasks'])}")
    print(f"  recovery_point: {first_value['recovery_point']}")
elif isinstance(first_value, list):
    print("✓ 这是传统格式（列表）")
    print(f"  列表内容: {first_value}")
else:
    print(f"✗ 未知格式，类型: {type(first_value)}")
print()

# 演示 7: 对比不同的访问方式
print("=== 演示 7: 不同访问方式的对比 ===")

# 方式1: 直接访问（需要知道具体的键）
print("方式1 - 直接访问:")
print(f"  drone_tasks_extended[6] = {drone_tasks_extended[6]}")

# 方式2: 使用 iter() + next()（不需要知道具体的键）
print("方式2 - 使用 iter() + next():")
first_value_method2 = next(iter(drone_tasks_extended.values()))
print(f"  第一个值 = {first_value_method2}")

# 方式3: 使用 for 循环（获取第一个值后退出）
print("方式3 - 使用 for 循环:")
for value in drone_tasks_extended.values():
    print(f"  第一个值 = {value}")
    break  # 只获取第一个值

print()

# 演示 8: 空字典的情况
print("=== 演示 8: 空字典的情况 ===")
empty_dict = {}
try:
    first_value_empty = next(iter(empty_dict.values()))
    print(f"空字典的第一个值: {first_value_empty}")
except StopIteration:
    print("空字典没有值，抛出 StopIteration 异常")