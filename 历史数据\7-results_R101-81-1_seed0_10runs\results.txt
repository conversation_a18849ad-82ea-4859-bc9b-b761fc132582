========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 150
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.9
交叉率: 0.8
精英比例: 0.1
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本	1012.20
最小总成本	950.28 (运行ID 8)
最大总成本	1064.19 (运行ID 9)
总成本标准差	36.93

========== 算法精度与稳定性分析 ==========
最大偏差	61.92 (6.12%)
平均偏差	29.06 (2.87%)
平均求解时间	308.37秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000983 | 1016.92 |    0.00 | 319.25
     2 |        2 | 0.001004 |  996.21 |    0.00 | 303.90
     3 |        3 | 0.000944 | 1059.54 |    0.00 | 309.26
     4 |        4 | 0.000965 | 1036.32 |    0.00 | 304.17
     5 |        5 | 0.000982 | 1018.58 |    0.00 | 306.24
     6 |        6 | 0.000978 | 1022.92 |    0.00 | 308.56
     7 |        7 | 0.001011 |  988.80 |    0.00 | 318.77
     8 |        8 | 0.001052 |  950.28 |    0.00 | 304.70
     9 |        9 | 0.000940 | 1064.19 |    0.00 | 304.51
    10 |       10 | 0.001033 |  968.23 |    0.00 | 304.36

最佳解详细信息:
运行ID: 8
适应度: 0.001052
总成本: 950.28
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 80, 78, 31, 0, 76, 77, 50, 6, 51, 79, 52, 5, 14, 72, 37, 12, 32, 35, 36, 13, 48, 62, 34, 2, 73, 11, 0, 45, 49, 21, 33, 18, 61, 64, 19, 63, 47, 20, 56, 4, 46, 68, 10, 57, 24, 66, 29, 54, 55, 60, 9, 69, 28, 67, 3, 65, 43, 23, 0, 22, 58, 1, 59, 17, 25, 75, 27, 53, 16, 40, 8, 39, 30, 42, 70, 41, 7, 15, 38, 71, 44, 26, 74, 0)
染色体 2: (0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0)

最佳解路线详情:
路线 1: [0, 80, 0]
  无人机任务:
    从节点 80 发射无人机访问: [78, 31]
路线 2: [0, 76, 77, 50, 6, 79, 52, 72, 37, 35, 36, 13, 48, 2, 73, 11, 0]
  无人机任务:
    从节点 6 发射无人机访问: [51]
    从节点 52 发射无人机访问: [5, 14]
    从节点 37 发射无人机访问: [12, 32]
    从节点 48 发射无人机访问: [62, 34]
路线 3: [0, 45, 33, 18, 61, 64, 47, 20, 56, 4, 46, 68, 24, 66, 29, 54, 55, 60, 9, 69, 28, 67, 3, 65, 23, 0]
  无人机任务:
    从节点 45 发射无人机访问: [49, 21]
    从节点 64 发射无人机访问: [19, 63]
    从节点 68 发射无人机访问: [10, 57]
    从节点 65 发射无人机访问: [43]
路线 4: [0, 22, 58, 1, 59, 75, 53, 16, 40, 30, 42, 70, 15, 44, 0]
  无人机任务:
    从节点 59 发射无人机访问: [17, 25]
    从节点 75 发射无人机访问: [27]
    从节点 40 发射无人机访问: [8, 39]
    从节点 70 发射无人机访问: [41, 7]
    从节点 15 发射无人机访问: [38, 71]
    从节点 44 发射无人机访问: [26, 74]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000547 | 1828.78 |    0.00
  10 | 0.000547 | 1828.78 |    0.00
  20 | 0.000547 | 1828.78 |    0.00
  30 | 0.000575 | 1738.64 |    0.00
  40 | 0.000575 | 1738.64 |    0.00
  50 | 0.000575 | 1738.64 |    0.00
  60 | 0.000578 | 1729.89 |    0.00
  70 | 0.000578 | 1729.89 |    0.00
  80 | 0.000645 | 1550.75 |    0.00
  90 | 0.000656 | 1524.23 |    0.00
 100 | 0.000711 | 1407.36 |    0.00
 110 | 0.000711 | 1407.36 |    0.00
 120 | 0.000802 | 1246.74 |    0.00
 130 | 0.000824 | 1213.00 |    0.00
 140 | 0.000919 | 1088.54 |    0.00
 150 | 0.000978 | 1022.57 |    0.00
 160 | 0.001012 |  987.67 |    0.00
 170 | 0.001012 |  987.67 |    0.00
 180 | 0.001028 |  972.61 |    0.00
 190 | 0.001049 |  953.33 |    0.00
 200 | 0.001052 |  950.28 |    0.00
