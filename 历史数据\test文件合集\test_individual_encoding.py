import copy
import json
from data_loader import Individual, Problem
import numpy as np

def test_individual_decode():
    """测试Individual.decode方法的解码功能"""
    print("开始测试Individual.decode方法...")
    
    # 创建测试用的染色体
    test_chromosomes = ([0, 0, 2, 8, 7, 5, 1, 3, 9, 6, 10, 4, 0], 
                        [0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0])
    
    # 预期解码结果
    expected_routes = [[0, 0], [0, 2, 7, 5, 1, 3, 6, 4, 0]]
    expected_drone_tasks = {2: [8], 3: [9], 6: [10]}
    
    print("\n测试染色体:")
    print("路径层:", test_chromosomes[0])
    print("服务方式层:", test_chromosomes[1])
    
    # 调用decode方法
    decoded_result = Individual.decode(test_chromosomes, None)
    
    # 从解码结果中提取路径和无人机任务
    decoded_routes = [route for route, _ in decoded_result]
    
    decoded_drone_tasks = {}
    for _, tasks in decoded_result:
        for launch_node, targets in tasks.items():
            if launch_node not in decoded_drone_tasks:
                decoded_drone_tasks[launch_node] = []
            decoded_drone_tasks[launch_node].extend(targets)
    
    # 打印解码结果
    print("\n解码结果:")
    print("路径:", decoded_routes)
    print("无人机任务:", decoded_drone_tasks)
    
    # 验证解码结果是否符合预期
    routes_match = decoded_routes == expected_routes
    
    # 检查无人机任务是否匹配
    tasks_match = True
    for launch, targets in expected_drone_tasks.items():
        if launch not in decoded_drone_tasks:
            tasks_match = False
            break
        if sorted(decoded_drone_tasks[launch]) != sorted(targets):
            tasks_match = False
            break
    
    # 额外检查是否有意外的任务
    for launch in decoded_drone_tasks:
        if launch not in expected_drone_tasks:
            tasks_match = False
            break
    
    print("\n验证结果:")
    print(f"路径匹配: {'成功' if routes_match else '失败'}")
    print(f"无人机任务匹配: {'成功' if tasks_match else '失败'}")
    print(f"总体结果: {'通过' if routes_match and tasks_match else '失败'}")
    
    if not routes_match:
        print("\n路径不匹配详情:")
        print(f"预期: {expected_routes}")
        print(f"实际: {decoded_routes}")
    
    if not tasks_match:
        print("\n无人机任务不匹配详情:")
        print(f"预期: {expected_drone_tasks}")
        print(f"实际: {decoded_drone_tasks}")
    
    print("\ndecode测试完成!")
    
    return routes_match and tasks_match

def test_individual_encode():
    """测试Individual.encode方法的编码功能"""
    print("\n开始测试Individual.encode方法...")
    
    # 创建测试用的路径和无人机任务
    test_routes = [[0, 0], [0, 2, 7, 5, 1, 3, 6, 4, 0]]
    test_drone_tasks = {2: [8], 3: [9], 6: [10]}
    
    print("\n测试输入:")
    print("路径:", test_routes)
    print("无人机任务:", test_drone_tasks)
    
    # 预期编码结果（大致的结构，具体序列可能因实现而异）
    expected_path_layer = [0, 0, 2, 8, 7, 5, 1, 3, 9, 6, 10, 4, 0]
    expected_service_layer = [0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0]
    
    # 调用encode方法
    encoded_result = Individual.encode(test_routes, test_drone_tasks, None)
    
    # 打印编码结果
    print("\n编码结果:")
    print("路径层:", encoded_result[0])
    print("服务方式层:", encoded_result[1])
    
    # 验证编码功能 - 通过反向解码验证
    # 因为编码实现可能有多种有效方式，所以我们通过解码结果验证而不是直接比较序列
    decoded_result = Individual.decode(encoded_result, None)
    
    # 从解码结果中提取路径和无人机任务
    decoded_routes = [route for route, _ in decoded_result]
    
    decoded_drone_tasks = {}
    for _, tasks in decoded_result:
        for launch_node, targets in tasks.items():
            if launch_node not in decoded_drone_tasks:
                decoded_drone_tasks[launch_node] = []
            decoded_drone_tasks[launch_node].extend(targets)
    
    # 验证反解码是否与原始输入一致
    # 注意：空路径[0,0]可能在某些实现中会被特殊处理
    routes_match = True
    for route in test_routes:
        if len(route) > 2 and route not in decoded_routes:  # 忽略空路径[0,0]的比较
            routes_match = False
            break
    
    # 检查无人机任务是否匹配
    tasks_match = True
    for launch, targets in test_drone_tasks.items():
        if launch not in decoded_drone_tasks:
            tasks_match = False
            break
        if sorted(decoded_drone_tasks[launch]) != sorted(targets):
            tasks_match = False
            break
    
    print("\n验证结果:")
    print(f"反向解码路径匹配: {'成功' if routes_match else '失败'}")
    print(f"反向解码无人机任务匹配: {'成功' if tasks_match else '失败'}")
    print(f"总体结果: {'通过' if routes_match and tasks_match else '失败'}")
    
    if not routes_match:
        print("\n路径不匹配详情:")
        print(f"原始: {test_routes}")
        print(f"反解码: {decoded_routes}")
    
    if not tasks_match:
        print("\n无人机任务不匹配详情:")
        print(f"原始: {test_drone_tasks}")
        print(f"反解码: {decoded_drone_tasks}")
    
    print("\nencode测试完成!")
    
    return routes_match and tasks_match

def test_encode_decode_roundtrip():
    """测试编码-解码-编码的一致性"""
    print("\n开始测试编码-解码-编码回路...")
    
    # 创建测试用的路径和无人机任务
    test_routes = [[0, 0], [0, 2, 7, 5, 1, 3, 6, 4, 0]]
    test_drone_tasks = {2: [8], 3: [9], 6: [10]}
    
    # 第一次编码
    first_encoded = Individual.encode(test_routes, test_drone_tasks, None)
    print("\n第一次编码结果:")
    print("路径层:", first_encoded[0])
    print("服务方式层:", first_encoded[1])
    
    # 解码第一次编码结果
    decoded = Individual.decode(first_encoded, None)
    
    # 从解码结果中提取路径和无人机任务
    decoded_routes = [route for route, _ in decoded]
    
    decoded_drone_tasks = {}
    for _, tasks in decoded:
        for launch_node, targets in tasks.items():
            if launch_node not in decoded_drone_tasks:
                decoded_drone_tasks[launch_node] = []
            decoded_drone_tasks[launch_node].extend(targets)
    
    print("\n解码结果:")
    print("路径:", decoded_routes)
    print("无人机任务:", decoded_drone_tasks)
    
    # 第二次编码（基于解码结果）
    second_encoded = Individual.encode(decoded_routes, decoded_drone_tasks, None)
    print("\n第二次编码结果:")
    print("路径层:", second_encoded[0])
    print("服务方式层:", second_encoded[1])
    
    # 比较两次编码结果是否一致
    # 注意：不同的有效编码方式可能产生不同但等效的染色体
    # 因此我们比较它们的解码结果而不是直接比较染色体
    second_decoded = Individual.decode(second_encoded, None)
    second_routes = [route for route, _ in second_decoded]
    
    second_drone_tasks = {}
    for _, tasks in second_decoded:
        for launch_node, targets in tasks.items():
            if launch_node not in second_drone_tasks:
                second_drone_tasks[launch_node] = []
            second_drone_tasks[launch_node].extend(targets)
    
    # 验证两次解码结果是否一致
    routes_match = decoded_routes == second_routes
    
    tasks_match = True
    for launch, targets in decoded_drone_tasks.items():
        if launch not in second_drone_tasks:
            tasks_match = False
            break
        if sorted(second_drone_tasks[launch]) != sorted(targets):
            tasks_match = False
            break
    
    for launch in second_drone_tasks:
        if launch not in decoded_drone_tasks:
            tasks_match = False
            break
    
    print("\n验证结果:")
    print(f"路径匹配: {'成功' if routes_match else '失败'}")
    print(f"无人机任务匹配: {'成功' if tasks_match else '失败'}")
    print(f"总体结果: {'通过' if routes_match and tasks_match else '失败'}")
    
    print("\n编码-解码-编码回路测试完成!")
    
    return routes_match and tasks_match

def run_all_tests():
    """运行所有测试"""
    print("="*80)
    print("开始Individual编码解码功能测试")
    print("="*80)
    
    # 运行decode测试
    decode_success = test_individual_decode()
    
    # 运行encode测试
    encode_success = test_individual_encode()
    
    # 运行回路测试
    roundtrip_success = test_encode_decode_roundtrip()
    
    # 打印总结果
    print("\n"+"="*80)
    print("测试总结:")
    print(f"decode测试: {'通过' if decode_success else '失败'}")
    print(f"encode测试: {'通过' if encode_success else '失败'}")
    print(f"编码-解码回路测试: {'通过' if roundtrip_success else '失败'}")
    print(f"总体结果: {'全部通过' if decode_success and encode_success and roundtrip_success else '存在失败'}")
    print("="*80)

if __name__ == "__main__":
    run_all_tests()
