#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重命名后的解码函数
验证函数重命名是否成功，以及编码-解码对应关系
"""

from data_loader import Individual, DroneTask

def test_renamed_functions():
    """测试重命名后的函数"""
    print("=" * 80)
    print("测试重命名后的解码函数")
    print("=" * 80)
    
    # 测试数据
    chromosomes = (
        [0, 6, 4, 7, 12, 0],  # path_layer
        [0, 2, 1, 1, 3, 0]    # service_layer
    )
    
    print("测试数据:")
    print(f"  path_layer:    {chromosomes[0]}")
    print(f"  service_layer: {chromosomes[1]}")
    print()
    
    # 测试传统解码方法
    print("1. 测试 _decode_traditional() 方法:")
    try:
        result_traditional = Individual._decode_traditional(chromosomes)
        print("  ✅ _decode_traditional() 调用成功")
        print(f"  结果类型: {type(result_traditional)}")
        print(f"  结果长度: {len(result_traditional)}")
        if result_traditional:
            truck_route, drone_tasks = result_traditional[0]
            print(f"  卡车路径: {truck_route}")
            print(f"  无人机任务: {drone_tasks}")
            print(f"  无人机任务类型: {type(drone_tasks)}")
    except Exception as e:
        print(f"  ❌ _decode_traditional() 调用失败: {e}")
    print()
    
    # 测试扩展解码方法
    print("2. 测试 _decode_extended() 方法:")
    try:
        result_extended = Individual._decode_extended(chromosomes)
        print("  ✅ _decode_extended() 调用成功")
        print(f"  结果类型: {type(result_extended)}")
        print(f"  结果长度: {len(result_extended)}")
        if result_extended:
            truck_route, drone_tasks_extended = result_extended[0]
            print(f"  卡车路径: {truck_route}")
            print(f"  扩展无人机任务: {drone_tasks_extended}")
            print(f"  扩展任务类型: {type(drone_tasks_extended)}")
            if drone_tasks_extended:
                for launch_point, task_info in drone_tasks_extended.items():
                    print(f"    发射点{launch_point}:")
                    print(f"      回收点: {task_info.get('recovery_point')}")
                    print(f"      任务数量: {len(task_info.get('drone_tasks', []))}")
    except Exception as e:
        print(f"  ❌ _decode_extended() 调用失败: {e}")
    print()
    
    # 测试统一入口方法
    print("3. 测试统一的 decode() 方法:")
    try:
        # 测试传统模式
        result_unified_traditional = Individual.decode(chromosomes, extended=False)
        print("  ✅ decode(extended=False) 调用成功")
        print(f"  传统模式结果: {type(result_unified_traditional)}")
        
        # 测试扩展模式
        result_unified_extended = Individual.decode(chromosomes, extended=True)
        print("  ✅ decode(extended=True) 调用成功")
        print(f"  扩展模式结果: {type(result_unified_extended)}")
        
        # 验证结果一致性
        print("  🔍 验证结果一致性:")
        traditional_match = str(result_traditional) == str(result_unified_traditional)
        extended_match = str(result_extended) == str(result_unified_extended)
        print(f"    传统模式一致: {traditional_match}")
        print(f"    扩展模式一致: {extended_match}")
        
    except Exception as e:
        print(f"  ❌ 统一decode()方法调用失败: {e}")
    print()

def show_function_mapping():
    """展示函数对应关系"""
    print("=" * 80)
    print("编码-解码函数对应关系")
    print("=" * 80)
    
    mapping = [
        {
            'mode': '传统模式',
            'encode': '_encode_traditional()',
            'decode': '_decode_traditional()',
            'data_format': '简单字典 {launch_point: [customers]}',
            'use_case': '兼容传统VRP-D系统'
        },
        {
            'mode': '扩展模式', 
            'encode': '_encode_extended()',
            'decode': '_decode_extended()',
            'data_format': '复杂对象 {launch_point: {drone_tasks: [DroneTask], recovery_point: int}}',
            'use_case': '支持多客户服务链和完整任务信息'
        }
    ]
    
    print(f"{'模式':<8} {'编码函数':<20} {'解码函数':<20} {'数据格式'}")
    print("-" * 80)
    for item in mapping:
        print(f"{item['mode']:<8} {item['encode']:<20} {item['decode']:<20} {item['data_format']}")
    print()
    
    print("📋 使用建议:")
    for item in mapping:
        print(f"  {item['mode']}:")
        print(f"    编码: Individual.{item['encode']}")
        print(f"    解码: Individual.{item['decode']}")
        print(f"    用途: {item['use_case']}")
        print()

def test_encoding_decoding_consistency():
    """测试编码-解码一致性"""
    print("=" * 80)
    print("编码-解码一致性测试")
    print("=" * 80)
    
    # 创建测试数据
    routes = [[0, 6, 12, 0]]
    
    # 传统格式无人机任务
    drone_tasks_traditional = {6: [4, 7]}
    
    # 扩展格式无人机任务
    drone_tasks_extended = {
        6: {
            'drone_tasks': [
                DroneTask(1, [4], 6, 12, 45.6),
                DroneTask(2, [7], 6, 12, 38.9)
            ],
            'recovery_point': 12
        }
    }
    
    print("1. 传统模式编码-解码测试:")
    try:
        # 编码
        encoded_traditional = Individual._encode_traditional(routes, drone_tasks_traditional)
        print(f"  编码结果: {encoded_traditional}")
        
        # 解码
        decoded_traditional = Individual._decode_traditional(encoded_traditional)
        print(f"  解码结果: {decoded_traditional}")
        print("  ✅ 传统模式编码-解码成功")
    except Exception as e:
        print(f"  ❌ 传统模式编码-解码失败: {e}")
    print()
    
    print("2. 扩展模式编码-解码测试:")
    try:
        # 编码
        encoded_extended = Individual._encode_extended(routes, drone_tasks_extended)
        print(f"  编码结果: {encoded_extended}")
        
        # 解码
        decoded_extended = Individual._decode_extended(encoded_extended)
        print(f"  解码结果: {decoded_extended}")
        print("  ✅ 扩展模式编码-解码成功")
    except Exception as e:
        print(f"  ❌ 扩展模式编码-解码失败: {e}")
    print()

if __name__ == "__main__":
    test_renamed_functions()
    show_function_mapping()
    test_encoding_decoding_consistency()
