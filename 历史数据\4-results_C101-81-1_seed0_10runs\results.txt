  # 自适应变异因子 - 随着迭代进行而减小
  self.mutation_factor = self.mutation_factor * (1 - 0.9 * elapsed_time / self.max_runtime)


  if progress_ratio <= 0.2:
  ls_frequency = 30  # 前期
  elif progress_ratio <= 0.6:
  ls_frequency = 25  # 中期
  else:
  ls_frequency = 20  # 后期


  # 基础惩罚系数和参数
  namuda2 = 5.0  # 元/Wh
  gamma = 3.0
  alpha = 1.5


  # 计算各方法生成的数量
  savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
  random_count = pop_size - savings_count  # 50%使用随机生成


  ========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1056.52
最小总成本: 956.60 (运行 5)
最大总成本: 1149.67 (运行 9)
总成本标准差: 61.25

========== 算法精度与稳定性分析 ==========
最大偏差: 193.07 (20.18%)
平均偏差: 99.92 (10.45%)
平均求解时间: 302.34秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.001003 |  997.18 |    0.00 | 302.07
     2 |        2 | 0.000993 | 1007.19 |    0.00 | 303.72
     3 |        3 | 0.000936 | 1068.59 |    0.00 | 301.61
     4 |        4 | 0.000981 | 1019.27 |    0.00 | 301.98
     5 |        5 | 0.001045 |  956.60 |    0.00 | 301.84
     6 |        6 | 0.000935 | 1069.38 |    0.00 | 301.95
     7 |        7 | 0.000897 | 1115.23 |    0.00 | 304.15
     8 |        8 | 0.000962 | 1039.23 |    0.00 | 301.71
     9 |        9 | 0.000870 | 1149.67 |    0.00 | 302.31
    10 |       10 | 0.000875 | 1142.84 |    0.00 | 302.02

最佳解详细信息:
运行ID: 5
适应度: 0.001045
总成本: 956.60
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 34, 39, 41, 33, 36, 40, 38, 35, 37, 32, 46, 47, 45, 44, 42, 43, 59, 49, 48, 51, 54, 53, 52, 50, 55, 0, 9, 10, 14, 15, 11, 12, 13, 8, 5, 6, 7, 4, 3, 1, 2, 76, 78, 73, 75, 74, 77, 80, 79, 60, 0, 19, 20, 22, 18, 16, 17, 21, 23, 24, 25, 26, 28, 27, 29, 31, 30, 0, 64, 62, 63, 61, 58, 56, 57, 65, 66, 69, 70, 67, 68, 71, 72, 0)
染色体 2: (0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 34, 33, 36, 35, 46, 44, 59, 48, 53, 55, 0]
  无人机任务:
    从节点 34 发射无人机访问: [39, 41]
    从节点 36 发射无人机访问: [40, 38]
    从节点 35 发射无人机访问: [37, 32]
    从节点 46 发射无人机访问: [47, 45]
    从节点 44 发射无人机访问: [42, 43]
    从节点 59 发射无人机访问: [49]
    从节点 48 发射无人机访问: [51, 54]
    从节点 53 发射无人机访问: [52, 50]
路线 2: [0, 9, 10, 14, 11, 8, 5, 4, 2, 76, 75, 74, 77, 80, 79, 60, 0]
  无人机任务:
    从节点 14 发射无人机访问: [15]
    从节点 11 发射无人机访问: [12, 13]
    从节点 5 发射无人机访问: [6, 7]
    从节点 4 发射无人机访问: [3, 1]
    从节点 76 发射无人机访问: [78, 73]
路线 3: [0, 19, 18, 16, 21, 23, 25, 26, 27, 29, 30, 0]
  无人机任务:
    从节点 19 发射无人机访问: [20, 22]
    从节点 16 发射无人机访问: [17]
    从节点 23 发射无人机访问: [24]
    从节点 26 发射无人机访问: [28]
    从节点 29 发射无人机访问: [31]
路线 4: [0, 64, 63, 61, 58, 56, 57, 65, 66, 69, 70, 71, 72, 0]
  无人机任务:
    从节点 64 发射无人机访问: [62]
    从节点 70 发射无人机访问: [67, 68]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000493 | 2029.74 |    0.00
  10 | 0.000493 | 2029.74 |    0.00
  20 | 0.000497 | 2011.81 |    0.00
  30 | 0.000497 | 2011.81 |    0.00
  40 | 0.000507 | 1972.60 |    0.00
  50 | 0.000507 | 1972.60 |    0.00
  60 | 0.000507 | 1972.60 |    0.00
  70 | 0.000507 | 1972.60 |    0.00
  80 | 0.000507 | 1972.60 |    0.00
  90 | 0.000507 | 1972.60 |    0.00
 100 | 0.000515 | 1940.37 |    0.00
 110 | 0.000534 | 1873.01 |    0.00
 120 | 0.000539 | 1853.86 |    0.00
 130 | 0.000539 | 1853.72 |    0.00
 140 | 0.000551 | 1816.20 |    0.00
 150 | 0.000551 | 1816.20 |    0.00
 160 | 0.000553 | 1807.71 |    0.00
 170 | 0.000566 | 1766.70 |    0.00
 180 | 0.000566 | 1765.94 |    0.00
 190 | 0.000576 | 1734.72 |    0.00
 200 | 0.000601 | 1664.22 |    0.00
 210 | 0.000614 | 1629.56 |    0.00
 220 | 0.000674 | 1483.28 |    0.00
 230 | 0.000771 | 1297.60 |    0.00
 240 | 0.000800 | 1249.62 |    0.00
 250 | 0.000849 | 1178.08 |    0.00
 260 | 0.000849 | 1178.07 |    0.00
 270 | 0.000868 | 1152.48 |    0.00
 280 | 0.000879 | 1138.23 |    0.00
 290 | 0.000889 | 1125.10 |    0.00
 300 | 0.000889 | 1125.10 |    0.00
 310 | 0.000891 | 1122.42 |    0.00
 320 | 0.000891 | 1122.42 |    0.00
 330 | 0.000895 | 1117.08 |    0.00
 340 | 0.000896 | 1115.89 |    0.00
 350 | 0.000896 | 1115.89 |    0.00
 360 | 0.000896 | 1115.89 |    0.00
 370 | 0.000896 | 1115.89 |    0.00
 380 | 0.000945 | 1058.08 |    0.00
 390 | 0.000945 | 1058.08 |    0.00
 400 | 0.000945 | 1058.08 |    0.00
 410 | 0.000945 | 1058.08 |    0.00
 420 | 0.000945 | 1058.08 |    0.00
 430 | 0.000945 | 1057.99 |    0.00
 440 | 0.001015 |  985.58 |    0.00
 450 | 0.001039 |  962.08 |    0.00
 460 | 0.001041 |  960.67 |    0.00
 470 | 0.001045 |  956.60 |    0.00
