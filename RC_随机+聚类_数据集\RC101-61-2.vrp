NAME : RC101-61-2
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 61
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 60

NODE_COORD_SECTION
 0 8.0 10.0
 1 4.0 16.0
 2 4.0 17.0
 3 3.6 15.0
 4 3.0 15.0
 5 2.0 8.0
 6 1.6 8.0
 7 1.6 9.0
 8 0.4 8.0
 9 0.0 8.0
 10 0.0 9.0
 11 8.8 1.0
 12 8.4 2.0
 13 8.4 3.0
 14 8.0 1.0
 15 7.6 1.0
 16 7.6 3.0
 17 19.0 7.0
 18 18.4 6.0
 19 17.6 6.0
 20 17.0 7.0
 21 13.0 17.0
 22 13.0 16.4
 23 12.4 16.0
 24 12.0 16.0
 25 11.0 17.0
 26 4.0 16.4
 27 3.6 16.0
 28 8.4 1.0
 29 8.4 2.4
 30 14.4 7.0
 31 11.0 4.0
 32 4.0 10.0
 33 11.0 12.0
 34 6.0 12.0
 35 10.0 7.0
 36 2.0 4.0
 37 9.0 13.0
 38 13.0 7.0
 39 13.0 4.0
 40 9.0 6.0
 41 7.0 8.0
 42 8.2 7.4
 43 12.8 8.4
 44 8.0 12.0
 45 6.2 10.4
 46 13.0 11.0
 47 12.0 2.4
 48 4.6 0.6
 49 1.6 11.2
 50 1.2 13.6
 51 7.4 6.2
 52 11.4 5.8
 53 2.4 4.8
 54 13.4 1.0
 55 10.6 8.6
 56 12.2 10.4
 57 11.4 9.6
 58 11.2 7.4
 59 5.2 10.4
 60 5.2 7.0

DEMAND_SECTION
 0 0 0      
 1 2.6 1.4      // 送货取货双需求客户 - light包裹
 2 1.7 0.0      // 仅送货需求客户 - light包裹
 3 9.0 0.0      // 仅送货需求客户 - medium包裹
 4 8.7 0.0      // 仅送货需求客户 - medium包裹
 5 0.0 4.8      // 仅取货需求客户 - light包裹
 6 2.4 0.0      // 仅送货需求客户 - light包裹
 7 0.0 2.7      // 仅取货需求客户 - light包裹
 8 1.7 1.2      // 送货取货双需求客户 - light包裹
 9 2.2 0.0      // 仅送货需求客户 - light包裹
 10 1.7 0.0      // 仅送货需求客户 - light包裹
 11 1.2 0.0      // 仅送货需求客户 - light包裹
 12 4.3 0.0      // 仅送货需求客户 - light包裹
 13 0.0 15.1      // 仅取货需求客户 - heavy包裹
 14 3.6 3.3      // 送货取货双需求客户 - light包裹
 15 6.0 3.9      // 送货取货双需求客户 - medium包裹
 16 15.5 0.0      // 仅送货需求客户 - heavy包裹
 17 3.4 0.0      // 仅送货需求客户 - light包裹
 18 1.2 0.0      // 仅送货需求客户 - light包裹
 19 3.1 0.0      // 仅送货需求客户 - light包裹
 20 0.0 2.3      // 仅取货需求客户 - light包裹
 21 4.4 2.1      // 送货取货双需求客户 - light包裹
 22 0.0 9.0      // 仅取货需求客户 - medium包裹
 23 0.0 12.4      // 仅取货需求客户 - heavy包裹
 24 18.5 11.6      // 送货取货双需求客户 - heavy包裹
 25 5.2 3.1      // 送货取货双需求客户 - medium包裹
 26 4.0 0.0      // 仅送货需求客户 - light包裹
 27 3.1 0.0      // 仅送货需求客户 - light包裹
 28 0.0 4.3      // 仅取货需求客户 - light包裹
 29 3.6 0.0      // 仅送货需求客户 - light包裹
 30 0.0 3.2      // 仅取货需求客户 - light包裹
 31 8.4 3.7      // 送货取货双需求客户 - medium包裹
 32 3.7 0.0      // 仅送货需求客户 - light包裹
 33 4.0 0.0      // 仅送货需求客户 - light包裹
 34 0.0 1.2      // 仅取货需求客户 - light包裹
 35 0.0 6.0      // 仅取货需求客户 - medium包裹
 36 4.6 0.0      // 仅送货需求客户 - light包裹
 37 3.5 1.8      // 送货取货双需求客户 - light包裹
 38 0.0 2.6      // 仅取货需求客户 - light包裹
 39 4.8 1.7      // 送货取货双需求客户 - light包裹
 40 1.6 0.0      // 仅送货需求客户 - light包裹
 41 4.2 0.0      // 仅送货需求客户 - light包裹
 42 3.6 0.0      // 仅送货需求客户 - light包裹
 43 1.8 0.0      // 仅送货需求客户 - light包裹
 44 1.3 0.0      // 仅送货需求客户 - light包裹
 45 2.1 0.0      // 仅送货需求客户 - light包裹
 46 3.5 2.5      // 送货取货双需求客户 - light包裹
 47 4.0 3.1      // 送货取货双需求客户 - light包裹
 48 2.9 0.0      // 仅送货需求客户 - light包裹
 49 9.5 0.0      // 仅送货需求客户 - medium包裹
 50 8.9 0.0      // 仅送货需求客户 - medium包裹
 51 2.0 1.9      // 送货取货双需求客户 - light包裹
 52 2.6 0.0      // 仅送货需求客户 - light包裹
 53 3.4 0.0      // 仅送货需求客户 - light包裹
 54 2.3 0.0      // 仅送货需求客户 - light包裹
 55 4.2 0.0      // 仅送货需求客户 - light包裹
 56 0.0 4.5      // 仅取货需求客户 - light包裹
 57 3.0 2.0      // 送货取货双需求客户 - light包裹
 58 3.2 1.5      // 送货取货双需求客户 - light包裹
 59 4.0 0.0      // 仅送货需求客户 - light包裹
 60 2.3 0.0      // 仅送货需求客户 - light包裹

DEPOT_SECTION
 0
-1
EOF