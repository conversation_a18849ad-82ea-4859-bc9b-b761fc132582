NAME : R101-31-1
COMMENT : (<PERSON> dataset, Modified for VRPD)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 8.75 8.75
 1 5.00 12.50
 2 2.50 10.75
 3 13.75 15.00
 4 5.00 16.25
 5 11.25 16.25
 6 11.25 5.00
 7 16.25 8.75
 8 16.25 5.00
 9 10.25 9.25
 10 7.75 13.00
 11 1.25 1.25
 12 10.50 1.75
 13 15.75 5.75
 14 6.00 14.50
 15 6.75 17.25
 16 15.50 19.25
 17 12.25 18.25
 18 14.00 9.75
 19 9.25 14.00
 20 11.75 4.00
 21 11.00 4.25
 22 12.25 2.75
 23 2.75 7.75
 24 4.00 5.50
 25 1.00 4.50
 26 6.50 13.00
 27 6.50 8.75
 28 7.75 16.75
 29 5.50 5.50
 30 6.50 6.75

DEMAND_SECTION
 0 0 0      
 1 3.5 0.0      // 仅送货需求客户 - light包裹
 2 1.4 1.2      // 送货取货双需求客户 - light包裹
 3 0.0 1.5      // 仅取货需求客户 - light包裹
 4 1.3 0.0      // 仅送货需求客户 - light包裹
 5 0.0 2.1      // 仅取货需求客户 - light包裹
 6 3.2 0.0      // 仅送货需求客户 - light包裹
 7 0.0 4.9      // 仅取货需求客户 - light包裹
 8 7.7 0.0      // 仅送货需求客户 - medium包裹
 9 3.2 3.0      // 送货取货双需求客户 - light包裹
 10 4.5 1.9      // 送货取货双需求客户 - light包裹
 11 8.7 0.0      // 仅送货需求客户 - medium包裹
 12 2.1 1.2      // 送货取货双需求客户 - light包裹
 13 3.7 0.0      // 仅送货需求客户 - light包裹
 14 7.6 0.0      // 仅送货需求客户 - medium包裹
 15 0.0 4.6      // 仅取货需求客户 - light包裹
 16 3.5 0.0      // 仅送货需求客户 - light包裹
 17 5.3 0.0      // 仅送货需求客户 - medium包裹
 18 1.5 0.0      // 仅送货需求客户 - light包裹
 19 11.7 0.0      // 仅送货需求客户 - heavy包裹
 20 0.0 3.8      // 仅取货需求客户 - light包裹
 21 1.4 0.0      // 仅送货需求客户 - light包裹
 22 8.5 2.1      // 送货取货双需求客户 - medium包裹
 23 0.0 2.5      // 仅取货需求客户 - light包裹
 24 2.7 1.1      // 送货取货双需求客户 - light包裹
 25 4.7 0.0      // 仅送货需求客户 - light包裹
 26 0.0 10.5      // 仅取货需求客户 - heavy包裹
 27 3.7 0.0      // 仅送货需求客户 - light包裹
 28 2.4 0.0      // 仅送货需求客户 - light包裹
 29 4.5 4.4      // 送货取货双需求客户 - light包裹
 30 4.7 0.0      // 仅送货需求客户 - light包裹

DEPOT_SECTION
 0
 -1
EOF