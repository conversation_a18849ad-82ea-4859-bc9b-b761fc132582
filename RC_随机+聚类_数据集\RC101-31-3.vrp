NAME : RC101-31-3
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 8.0 10.0
 1 5.0 17.0
 2 4.0 16.0
 3 1.0 7.0
 4 1.0 9.0
 5 0.4 8.0
 6 8.4 3.0
 7 8.0 3.0
 8 7.0 1.0
 9 19.0 6.0
 10 17.0 7.0
 11 13.4 17.0
 12 12.0 16.0
 13 11.0 16.4
 14 4.0 16.4
 15 0.4 9.0
 16 8.4 2.4
 17 11.0 12.0
 18 6.0 12.0
 19 3.0 2.0
 20 9.0 13.0
 21 13.0 7.0
 22 8.0 12.0
 23 6.2 10.4
 24 1.2 13.6
 25 4.2 4.8
 26 7.4 9.4
 27 10.6 8.6
 28 11.4 9.6
 29 11.2 7.4
 30 0.8 3.6

DEMAND_SECTION
 0 0 0      
 1 2.2 0.0      // 仅送货需求客户 - light包裹
 2 0.0 2.3      // 仅取货需求客户 - light包裹
 3 3.7 0.0      // 仅送货需求客户 - light包裹
 4 0.0 1.1      // 仅取货需求客户 - light包裹
 5 4.1 0.0      // 仅送货需求客户 - light包裹
 6 19.6 0.0      // 仅送货需求客户 - heavy包裹
 7 4.1 0.0      // 仅送货需求客户 - light包裹
 8 4.4 0.0      // 仅送货需求客户 - light包裹
 9 3.1 0.0      // 仅送货需求客户 - light包裹
 10 9.3 6.2      // 送货取货双需求客户 - medium包裹
 11 8.0 0.0      // 仅送货需求客户 - medium包裹
 12 0.0 2.9      // 仅取货需求客户 - light包裹
 13 3.0 0.0      // 仅送货需求客户 - light包裹
 14 1.5 1.2      // 送货取货双需求客户 - light包裹
 15 4.3 0.0      // 仅送货需求客户 - light包裹
 16 7.6 0.0      // 仅送货需求客户 - medium包裹
 17 3.0 1.7      // 送货取货双需求客户 - light包裹
 18 4.7 3.3      // 送货取货双需求客户 - light包裹
 19 1.3 1.2      // 送货取货双需求客户 - light包裹
 20 2.0 0.0      // 仅送货需求客户 - light包裹
 21 7.2 0.0      // 仅送货需求客户 - medium包裹
 22 0.0 4.5      // 仅取货需求客户 - light包裹
 23 1.3 1.0      // 送货取货双需求客户 - light包裹
 24 4.9 3.6      // 送货取货双需求客户 - light包裹
 25 0.0 4.1      // 仅取货需求客户 - light包裹
 26 4.7 0.0      // 仅送货需求客户 - light包裹
 27 1.3 0.0      // 仅送货需求客户 - light包裹
 28 0.0 1.6      // 仅取货需求客户 - light包裹
 29 0.0 1.0      // 仅取货需求客户 - light包裹
 30 6.0 0.0      // 仅送货需求客户 - medium包裹

DEPOT_SECTION
 0
-1
EOF