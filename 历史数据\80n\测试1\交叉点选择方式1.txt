========== 运行 1/1 ==========
初始化种群...
初始化完成，种群大小: 100
初始最佳适应度: 0.000053

========== 第20代最优解详情 ==========
适应度: 0.000890
总成本: 1123.76
惩罚值: 0.00

/DE_改进/read_test.py                                                                                                        正在读取文件: results_A-n80-k2-d4.vrp_1runs.pkl...
成功读取了1次运行的结果。

========== 多次运行结果统计 ==========
运行次数: 1
平均总成本: 678.89
最小总成本: 678.89 (运行 1)
最大总成本: 678.89 (运行 1)
总成本标准差: 0.00

========== 算法精度与稳定性分析 ==========
最大偏差: 0.00 (0.00%)
平均偏差: 0.00 (0.00%)
平均求解时间: 218.83秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 | 0.001473 |   678.89 |        0 | 218.83秒   |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 1) ==========
适应度: 0.001473
总成本: 678.89
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 51, 39, 31, 3, 15, 46, 33, 41, 25, 19, 69, 47, 35, 65, 56, 55, 9, 54, 72, 50, 76, 45, 22, 4, 32, 58, 38, 70, 66
, 67, 36, 0, 49, 73, 42, 53, 77, 60, 29, 23, 24, 6, 48, 18, 79, 28, 52, 14, 71, 11, 63, 34, 10, 21, 40, 0, 12, 5, 59, 27, 30, 37, 8, 2, 78, 68, 16, 43, 61, 57, 75, 20, 26, 1, 62, 44, 17, 74, 64, 7, 13, 0)                                                服务方式层: (0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0)                                                                                                                
染色体解码结果:
  车辆 1 路径: [0, 51, 39, 15, 41, 19, 35, 65, 56, 55, 9, 54, 72, 50, 22, 4, 32, 58, 70, 66, 67, 36, 0]
    无人机任务:
      发射点 39 → 任务: [31, 3]
      发射点 15 → 任务: [46, 33]
      发射点 41 → 任务: [25]
      发射点 19 → 任务: [69, 47]
      发射点 50 → 任务: [76, 45]
      发射点 58 → 任务: [38]
  车辆 2 路径: [0, 49, 73, 42, 53, 77, 60, 29, 23, 24, 48, 79, 28, 52, 11, 10, 21, 40, 0]
    无人机任务:
      发射点 24 → 任务: [6]
      发射点 48 → 任务: [18]
      发射点 52 → 任务: [14, 71]
      发射点 11 → 任务: [63, 34]
  车辆 3 路径: [0, 12, 5, 59, 27, 30, 37, 8, 2, 78, 68, 16, 43, 61, 57, 26, 1, 62, 44, 17, 74, 13, 0]
    无人机任务:
      发射点 57 → 任务: [75, 20]
      发射点 74 → 任务: [64, 7]

程序执行完毕。
