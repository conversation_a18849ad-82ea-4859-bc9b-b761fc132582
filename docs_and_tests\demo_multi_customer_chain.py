#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多客户服务链演示程序
展示无人机如何服务多个客户的完整格式
"""

from data_loader import Individual, DroneTask

def demo_multi_customer_chains():
    """演示多客户服务链的完整应用"""
    print("=" * 70)
    print("多客户服务链完整演示")
    print("=" * 70)
    
    # 创建模拟问题实例
    class MockProblem:
        def __init__(self):
            self.num_nodes = 32
            self.num_customers = 31
            self.num_vehicles = 2
            self.num_drones = 2

    problem = MockProblem()
    
    # 设计复杂的配送场景
    routes = [
        [0, 5, 10, 18, 0],      # 卡车1
        [0, 8, 15, 25, 30, 0]   # 卡车2
    ]
    
    print("配送场景设计:")
    print("  卡车1路径: [0, 5, 10, 18, 0]")
    print("  卡车2路径: [0, 8, 15, 25, 30, 0]")
    print()
    
    # 多客户服务链设计
    drone_tasks_multi = {
        5: {  # 发射点5
            'drone_tasks': [
                DroneTask(
                    drone_id=1,
                    customer_sequence=[2, 7, 12],     # 无人机1服务3个客户
                    launch_point=5,
                    recovery_point=10,
                    total_energy=185.6
                ),
                DroneTask(
                    drone_id=2,
                    customer_sequence=[4, 9],         # 无人机2服务2个客户
                    launch_point=5,
                    recovery_point=10,
                    total_energy=142.3
                )
            ],
            'recovery_point': 10
        },
        15: {  # 发射点15
            'drone_tasks': [
                DroneTask(
                    drone_id=3,
                    customer_sequence=[11, 16, 20, 22, 28],  # 无人机3服务5个客户！
                    launch_point=15,
                    recovery_point=25,
                    total_energy=298.7
                ),
                DroneTask(
                    drone_id=4,
                    customer_sequence=[14],           # 无人机4服务1个客户
                    launch_point=15,
                    recovery_point=25,
                    total_energy=89.4
                )
            ],
            'recovery_point': 25
        }
    }
    
    print("多客户服务链详细设计:")
    total_drone_customers = 0
    for launch_point, task_info in drone_tasks_multi.items():
        print(f"  发射点{launch_point} → 回收点{task_info['recovery_point']}:")
        for task in task_info['drone_tasks']:
            route_seq = task.get_route_sequence()
            print(f"    无人机{task.drone_id}: {' → '.join(map(str, route_seq))}")
            print(f"      服务{task.num_customers}个客户: {task.customer_sequence}")
            print(f"      预计能耗: {task.total_energy}单位")
            total_drone_customers += task.num_customers
    
    print(f"\n  总计: {total_drone_customers}个客户由无人机服务")
    print()
    
    # 执行编码
    print("执行多客户链编码...")
    chromosomes = Individual._encode_extended(routes, drone_tasks_multi, problem)
    
    print("编码结果:")
    print(f"  路径层: {chromosomes[0]}")
    print(f"  服务层: {chromosomes[1]}")
    print(f"  总长度: {len(chromosomes[0])}")
    print()
    
    # 分析编码结果
    print("编码结果分析:")
    path_layer, service_layer = chromosomes
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        service_desc = {
            0: "卡车服务",
            1: "无人机客户", 
            2: "发射点",
            3: "回收点",
            4: "回收+发射"
        }.get(service, f"未知({service})")
        print(f"  位置{i:2d}: 节点{node:2d} - {service_desc}")
    print()
    
    # 执行解码
    print("执行解码验证...")
    decoded_solution = Individual.decode_extended(chromosomes, problem)
    
    print("解码结果验证:")
    for i, (truck_route, drone_tasks_extended) in enumerate(decoded_solution):
        if len(truck_route) > 1:  # 非空路径
            print(f"  卡车{i+1}路径: {truck_route}")
            if drone_tasks_extended:
                for launch_point, task_info in drone_tasks_extended.items():
                    print(f"    发射点{launch_point} → 回收点{task_info['recovery_point']}")
                    for task in task_info['drone_tasks']:
                        route_seq = task.get_route_sequence()
                        print(f"      无人机{task.drone_id}: {' → '.join(map(str, route_seq))} (服务{task.num_customers}个客户)")
            else:
                print("    无无人机任务")
    print()
    
    # 成本分析（简化版）
    print("成本分析:")
    print("  注：实际成本需要完整的距离矩阵和时间计算")
    total_energy = sum(task.total_energy for task_info in drone_tasks_multi.values()
                      for task in task_info['drone_tasks'])
    print(f"  无人机总能耗: {total_energy:.1f}单位")
    print()
    
    # 效率分析
    print("多客户链效率分析:")
    drone_customers = sum(task.num_customers for task_info in drone_tasks_multi.values() 
                         for task in task_info['drone_tasks'])
    num_drones = sum(len(task_info['drone_tasks']) for task_info in drone_tasks_multi.values())
    avg_customers_per_drone = drone_customers / num_drones if num_drones > 0 else 0
    
    print(f"  无人机数量: {num_drones}")
    print(f"  服务客户总数: {drone_customers}")
    print(f"  平均每架无人机服务: {avg_customers_per_drone:.1f}个客户")
    print(f"  最大服务链长度: {max(task.num_customers for task_info in drone_tasks_multi.values() for task in task_info['drone_tasks'])}")
    print()
    
    print("演示完成！")
    print("=" * 70)

if __name__ == "__main__":
    demo_multi_customer_chains()
