import unittest
import random
import numpy as np
import os
import sys

# 导入必要的模块
from data_loader import Problem
from initialization import initialize_population
from algorithm import solve_vrpd

class TestVRPDSolver(unittest.TestCase):
    """VRP-D求解器测试类"""

    def setUp(self):
        """测试前的准备工作"""
        # 设置固定的随机种子以确保结果可复现
        random.seed(42)
        np.random.seed(42)
        
        # 加载问题实例
        self.problem_file = 'A-n32-k2-d4.vrp'
        self.problem = Problem(self.problem_file)
        
        # 测试参数
        self.pop_size = 100
        self.max_generations = 200
        self.mutation_factor = 0.8
        self.crossover_rate = 0.8
        self.elite_ratio = 0.05  # 1/5
        
    def test_initialize_population(self):
        """测试初始种群生成函数"""
        # 生成初始种群
        population = initialize_population(self.problem, self.pop_size)
        
        # 验证种群大小
        self.assertEqual(len(population), self.pop_size, 
                        f"种群大小应为{self.pop_size}，实际为{len(population)}")
        
        # 验证种群中个体的基本属性
        for idx, individual in enumerate(population):
            self.assertTrue(hasattr(individual, 'routes'), f"个体{idx}缺少routes属性")
            self.assertTrue(hasattr(individual, 'drone_tasks'), f"个体{idx}缺少drone_tasks属性")
            self.assertTrue(hasattr(individual, 'chromosomes'), f"个体{idx}缺少chromosomes属性")
            
            # 验证每个个体的路径至少包含一个有效路径
            valid_routes = 0
            for route in individual.routes:
                if len(route) >= 2 and route[0] == 0 and route[-1] == 0:
                    valid_routes += 1
            self.assertGreater(valid_routes, 0, f"个体{idx}没有有效路径")
    
    def test_solve_vrpd(self):
        """测试solve_vrpd求解函数"""
        # 调用solve_vrpd函数
        best_individual, history = solve_vrpd(
            self.problem,
            pop_size=self.pop_size,
            max_generations=self.max_generations,
            mutation_factor=self.mutation_factor,
            crossover_rate=self.crossover_rate,
            elite_ratio=self.elite_ratio
        )
        
        # 验证解的存在性
        self.assertIsNotNone(best_individual, "最佳个体不应为None")
        
        # 验证解的基本属性
        self.assertTrue(hasattr(best_individual, 'fitness'), "最佳个体应有fitness属性")
        self.assertGreater(best_individual.fitness, 0, "最佳个体适应度应大于0")
        
        # 验证解的结构
        self.assertTrue(hasattr(best_individual, 'routes'), "最佳个体应有routes属性")
        self.assertTrue(hasattr(best_individual, 'drone_tasks'), "最佳个体应有drone_tasks属性")
        
        # 验证路径数量符合车辆数要求
        self.assertEqual(len(best_individual.routes), self.problem.num_vehicles,
                         f"路径数量应为{self.problem.num_vehicles}，实际为{len(best_individual.routes)}")
        
        # 验证历史记录
        history_keys = ['best_fitness', 'avg_fitness', 'best_cost', 'feasible_count']
        for key in history_keys:
            self.assertIn(key, history, f"历史记录应包含{key}")
            self.assertLessEqual(len(history[key]), self.max_generations, 
                               f"{key}的记录数不应超过最大代数{self.max_generations}")

    def test_evolve_wrapper(self):
        """创建一个封装函数来测试main.py中evolve的用法"""
        def evolve_wrapper(problem_file, pop_size, max_generations, mutation_factor, crossover_rate, elite_ratio, random_seed=None):
            """模拟main.py中使用的evolve函数"""
            # 设置随机种子
            if random_seed is not None:
                random.seed(random_seed)
                np.random.seed(random_seed)
                
            # 加载问题
            problem = Problem(problem_file)
            
            # 调用solve_vrpd
            return solve_vrpd(
                problem,
                pop_size=pop_size,
                max_generations=max_generations,
                mutation_factor=mutation_factor,
                crossover_rate=crossover_rate,
                elite_ratio=elite_ratio
            )
        
        # 使用封装函数测试
        best_individual, history = evolve_wrapper(
            problem_file=self.problem_file,
            pop_size=self.pop_size,
            max_generations=self.max_generations,
            mutation_factor=self.mutation_factor,
            crossover_rate=self.crossover_rate,
            elite_ratio=self.elite_ratio,
            random_seed=42
        )
        
        # 验证结果
        self.assertIsNotNone(best_individual)
        self.assertGreater(best_individual.fitness, 0)

if __name__ == '__main__':
    unittest.main()