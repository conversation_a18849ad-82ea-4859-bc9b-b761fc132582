import random
import time
import copy
from typing import List, Dict, Tuple, Any
import numpy as np
from data_loader import Problem, Individual
from initialization import initialize_population
from operators import selection, mutation, crossover
from utils import evaluate_individual_with_penalty, update_population_fitness
from repair import (
    check_and_repair_drone_launch_point,
    check_and_repair_drone_task_limit,
    check_and_repair_service_type,
    check_and_repair_drone_payload,
    check_and_repair_path_separation
)


class DifferentialEvolution:
    """
    差分进化算法实现类
    
    该算法主要包含以下步骤：
    1. 初始化种群
    2. 计算初始种群适应度
    3. 循环迭代直到达到终止条件（最大运行时间）：
       a. 保留精英个体
       b. 生成新种群：
          - 变异：在种群中随机选择3个个体进行变异，最后会得到一个临时试验个体
          - 交叉：在剩余种群中随机选择1个个体，将其与临时试验个体进行交叉得到试验个体保存到中间种群中。
          - 适应度评估:对试验个体计算适应度值、总成本、惩罚值
          - 选择：采用贪婪策略，在原种群和中间种群中选择个体，生成新一代种群。
       c. 更新种群
       d. 更新最佳个体
       e. 检查时间是否到达终止条件
    4. 返回最优解
    """
    
    def __init__(self, problem: Problem, **kwargs):
        """
        初始化差分进化算法
        
        参数：
            problem: 问题实例
            **kwargs: 算法参数
        """
        self.problem = problem
        self.pop_size = kwargs.get('pop_size', 100)
        self.max_generations = kwargs.get('max_generations', 500)
        self.max_runtime = kwargs.get('max_runtime', 60)  # 默认最大运行时间60秒
        self.mutation_factor = kwargs.get('mutation_factor', 0.8)
        self.crossover_rate = kwargs.get('crossover_rate', 0.9)
        self.elite_ratio = kwargs.get('elite_ratio', 0.1)
        
        # 记录间隔参数
        self.gen_interval = kwargs.get('gen_interval', 5)  # 默认每5代记录一次
        self.time_interval = kwargs.get('time_interval', 1)  # 默认每1秒记录一次
        
        # 初始化种群和最佳个体
        self.population = []
        self.best_individual = None
        self.best_fitness = 0.0
        
        # 初始化历史记录
        self.history = {
            'best_fitness': [],           # 全局最佳适应度
            'avg_fitness': [],            # 平均适应度
            'best_total_cost': [],        # 全局最佳总成本
            'best_penalty': [],           # 全局最佳惩罚值
            'current_gen_best_fitness': [],  # 当前代最佳适应度
            'current_gen_best_cost': [],     # 当前代最佳总成本
            'current_gen_best_penalty': [],  # 当前代最佳惩罚值
            'generation_best': [],        # 每代最佳个体
            'elapsed_time': [],           # 已运行时间
            'generations': []             # 迭代次数
        }
    

    def initialize(self):
        """
        初始化种群
        使用initialize_population函数生成初始种群
        并计算每个个体的适应度
        """
        print("初始化种群...")
        self.population = initialize_population(self.problem, self.pop_size)
        
        # 评估初始种群适应度
        update_population_fitness(self.problem, self.population, 0, self.max_generations)
        
        # 更新最佳解
        self._update_best_individual()
        
        print(f"初始化完成，种群大小: {len(self.population)}")
        print(f"初始最佳适应度: {self.best_fitness:.6f}")
    

    def _update_best_individual(self):
        """
        更新全局最佳个体
        比较当前种群中的最佳个体与历史最佳个体，更新全局最佳
        """
        current_best = max(self.population, key=lambda ind: ind.fitness)
        
        if self.best_individual is None or current_best.fitness > self.best_fitness:
            # 只在确实找到更好的解时进行深拷贝
            self.best_individual = copy.deepcopy(current_best)
            self.best_fitness = current_best.fitness


    def evolve(self):
        """
        主进化循环 - 基于运行时间的终止条件
        
        1. 依次遍历原种群中的个体作为当前基向量
        2. 变异：随机选择两个个体进行变异
        3. 交叉：临时试验个体与轮盘赌选择的个体交叉
        4. 选择：从当前基向量和两个子代中选择最优个体
        5. 检查是否达到最大运行时间
        """
        start_time = time.time()
        generation = 1
        
        # 主循环：基于时间的终止条件
        while True:
            # 检查是否达到最大运行时间
            elapsed_time = time.time() - start_time
            if elapsed_time >= self.max_runtime:
                print(f"达到最大运行时间 {self.max_runtime}秒，算法终止")
                break
            
            # 检查是否达到最大代数(备选终止条件)
            if generation > self.max_generations:
                print(f"达到最大代数 {self.max_generations}，算法终止")
                break
                
            # 生成新种群
            new_population = []
            
            # 循环变异交叉局部搜索适应度评估
            for i in range(len(self.population)):
                current_individual = self.population[i]
                
                # 变异：选择r2和r3个体进行变异
                candidates = [ind for ind in self.population if ind != current_individual]
                r2, r3 = random.sample(candidates, 2)
                
                # 创建临时试验个体
                trial_individual = copy.deepcopy(current_individual)


                # 自适应变异因子 - 随着迭代进行而减小
                self.mutation_factor = self.mutation_factor * (1 - 0.4 * elapsed_time / self.max_runtime)

                # # 自适应变异因子 - 使用非线性衰减
                # progress_ratio = elapsed_time / self.max_runtime
                # if progress_ratio < 0.3:
                #     # 前30%时间保持高变异因子
                #     self.mutation_factor = self.mutation_factor * 0.998
                # elif progress_ratio < 0.7:
                #     # 中间40%时间中等衰减
                #     self.mutation_factor = self.mutation_factor * (1 - 0.3 * (progress_ratio - 0.3) / 0.4)
                # else:
                #     # 最后30%时间快速衰减
                #     self.mutation_factor = self.mutation_factor * (1 - 0.5 * (progress_ratio - 0.7) / 0.3)

                # # 设置变异因子下限，确保最低探索能力
                # self.mutation_factor = max(0.4, self.mutation_factor)

                """
                    增大初始  mutation_factor（如从0.8到1.0）：
                    增强算法早期的变异强度
                    提高全局探索能力
                    可能导致搜索过程更加随机
                    有助于跳出局部最优，但可能降低收敛速度

                    减小初始  mutation_factor（如从0.8到0.5）：
                    降低算法早期的变异强度
                    减弱全局探索能力
                    使搜索过程更加稳定
                    可能加速收敛，但增加陷入局部最优的风险

                    0.4（衰减系数）变化的影响
                    增大衰减系数（如从0.4到0.8）：
                    加快变异因子的衰减速度
                    算法更快地从探索转向开发
                    可能导致过早收敛
                    适合简单问题或需要快速收敛的场景

                    减小衰减系数（如从0.4到0.2）：
                    减慢变异因子的衰减速度
                    保持较长时间的探索能力
                    延迟收敛，但可能找到更好的解
                    适合复杂多峰问题

                    假设初始mutation_factor = 0.8，max_runtime = 300秒，不同衰减系数下的变异因子变化：
                    运行时间	原始设置
                    (decay=0.4)	快速衰减
                    (decay=0.8)	慢速衰减
                    (decay=0.2)	无衰减
                    (decay=0.0)
                    0秒	0.800	0.800	0.800	0.800
                    75秒	0.720	0.640	0.760	0.800
                    150秒	0.640	0.480	0.720	0.800
                    225秒	0.560	0.320	0.680	0.800
                    300秒	0.480	0.160	0.680	0.800
                    """
                
                # 应用差分变异
                mutation(trial_individual, self.problem, [current_individual, r2, r3], 
                        self.mutation_factor, generation, self.max_generations)
                
                # 使缓存失效
                trial_individual.invalidate_cache()
                
                # 交叉：找到不同于当前个体的交叉伙伴
                crossover_partner = None
                max_attempts = 10
                attempts = 0

                while (crossover_partner is None or crossover_partner == current_individual) and attempts < max_attempts:
                    crossover_partner = selection(self.population, generation, self.max_generations)
                    attempts += 1
                
                # 进行交叉操作，生成两个子代
                offspring1, offspring2 = crossover(trial_individual, crossover_partner, self.problem)
                
                # 使缓存失效
                offspring1.invalidate_cache()
                offspring2.invalidate_cache()
                
                # 评估子代适应度
                evaluate_individual_with_penalty(self.problem, offspring1, generation, self.max_generations)
                evaluate_individual_with_penalty(self.problem, offspring2, generation, self.max_generations)
                
                # 从当前个体和两个子代中选择适应度最高的一个
                candidates = [current_individual, offspring1, offspring2]
                best_candidate = max(candidates, key=lambda ind: ind.fitness)
                
                # 将最佳个体添加到新种群
                new_population.append(best_candidate)
            
            # 更新种群
            self.population = new_population

            # 自适应局部搜索策略：基于运行时间比例而不是迭代次数
            progress_ratio = elapsed_time / self.max_runtime  # 计算时间进度比例


            # # 周期性应用局部搜索（每20代一次）
            # if generation % 20 == 0:
            #     self.population = self.apply_local_search(
            #         self.population, self.problem, generation, self.max_generations
            #     )
            #     # print(f"运行至{generation}代，执行局部搜索。")
            
            # 前期(0-20%)：较少执行局部搜索
            # 中期(20%-60%)：适度执行局部搜索
            # 后期(70%-100%)：频繁执行局部搜索
            if progress_ratio <= 0.2:
                ls_frequency = 30  # 前期
            elif progress_ratio <= 0.5:
                ls_frequency = 20  # 中期
            else:
                ls_frequency = 10  # 后期
            
            # 根据当前代数决定是否执行局部搜索
            if generation % ls_frequency == 0:
                self.population = self.apply_local_search(
                    self.population, self.problem, generation, self.max_generations, elapsed_time
                )
                # self.population = self.apply_local_search(
                #     self.population, self.problem, generation, self.max_generations
                # )

            # 更新最佳个体
            self._update_best_individual()
            
            # 更新历史记录，包括当前运行时间
            self._update_history(generation, elapsed_time)
            
            # 如果已经达到时间阈值的整数秒，打印进度
            if int(elapsed_time) % 60 == 0 and int(elapsed_time) > 0 and int(elapsed_time - 1) % 60 != 0:
                current_gen_best = max(self.population, key=lambda ind: ind.fitness)
                print(f"运行时间: {int(elapsed_time)}秒, 迭代次数: {generation}, " +
                      f"最佳适应度: {self.best_fitness:.6f}, 最佳成本: {self.best_individual.total_cost:.2f}")
            
            # 递增代数
            generation += 1
        
        # 计算总耗时
        total_time = time.time() - start_time
        print(f"进化完成，总耗时: {total_time:.2f}s，总迭代次数: {generation-1}")
        print(f"最终最佳适应度: {self.best_fitness:.6f}, 最佳成本: {self.best_individual.total_cost:.2f}")
        
        return self.best_individual   

    def _update_history(self, generation: int, elapsed_time: float):
        """
        更新迭代历史记录
        
        记录当前迭代的各项指标，用于后续分析和可视化
        
        参数：
            generation: 当前代数
            elapsed_time: 已经运行的时间(秒)
        """
        # 获取记录间隔参数
        gen_interval = getattr(self, 'gen_interval', 5)  # 默认每5代记录一次
        time_interval = getattr(self, 'time_interval', 1)  # 默认每1秒记录一次
        
        # 确定是否需要记录当前代
        # 1. 按代数间隔记录
        record_by_gen = (generation % gen_interval == 0)
        
        # 2. 按时间间隔记录
        # 计算上次记录的时间
        last_time = self.history['elapsed_time'][-1] if self.history['elapsed_time'] else 0
        record_by_time = (int(elapsed_time) // time_interval > int(last_time) // time_interval)
        
        # 3. 在算法开始和结束阶段更频繁记录
        record_special = (generation <= 10) or (elapsed_time >= self.max_runtime * 0.95)
        
        # 如果满足任一条件，则记录
        if record_by_gen or record_by_time or record_special:
            # 找出当前代的最佳个体
            current_gen_best = max(self.population, key=lambda ind: ind.fitness)

            # 计算当前代的平均适应度
            avg_fitness = sum(ind.fitness for ind in self.population) / len(self.population)
        
            # 更新历史记录
            self.history['best_fitness'].append(self.best_fitness)
            self.history['avg_fitness'].append(avg_fitness)
            self.history['best_total_cost'].append(self.best_individual.total_cost)
            self.history['best_penalty'].append(self.best_individual.penalty)
            self.history['current_gen_best_fitness'].append(current_gen_best.fitness)
            self.history['current_gen_best_cost'].append(current_gen_best.total_cost)
            self.history['current_gen_best_penalty'].append(current_gen_best.penalty)
            self.history['elapsed_time'].append(elapsed_time)  # 记录已运行时间
            self.history['generations'].append(generation)     # 记录迭代次数
            
            # 仅在特定条件下保存完整个体
            if record_by_gen and (generation % (gen_interval * 5) == 0 or record_special):
                self.history['generation_best'].append(copy.deepcopy(current_gen_best))
    def apply_local_search(self, population: List[Individual], problem: Problem, 
                        current_gen: int, max_gen: int, elapsed_time: float) -> List[Individual]:
        """
        对种群中的部分个体应用局部搜索
        
        参数：
            population: 个体种群
            problem: 问题实例
            current_gen: 当前代数
            max_gen: 最大代数
            elapsed_time: 已运行时间(秒)
        
        返回：
            应用局部搜索后的种群
        """
        from destroy_repair import apply_destroy_repair
        
        # 基于种群适应度对个体排序
        sorted_population = sorted(population, key=lambda ind: ind.fitness, reverse=True)
        
        """
        增大  base_probability：
        提高整个进化过程中局部搜索的基础概率
        从一开始就有更高的局部搜索概率
        算法会更早地进行局部开发，可能加速收敛
        可能导致过早收敛到局部最优解

        减小  base_probability：
        降低整个进化过程中局部搜索的基础概率
        初期局部搜索概率更低
        算法前期更注重全局探索
        可能延迟收敛，但有助于避免过早陷入局部最优

        增大自适应范围（如从0.9到1.0）：
        扩大局部搜索概率的变化范围
        使算法在进化后期有更高的局部搜索概率
        增强算法后期的局部开发能力
        可能导致后期计算资源过多用于局部搜索

        减小自适应范围（如从0.9到0.5）：
        缩小局部搜索概率的变化范围
        使算法在整个进化过程中局部搜索概率变化不那么剧烈
        算法行为在不同阶段更加一致
        可能导致后期局部开发不足
        """
        
        # 确定局部搜索概率（随着进化进行逐渐增加）
        base_probability = 0.2
        adaptive_factor = elapsed_time / self.max_runtime 
        search_probability = base_probability + (0.8 * adaptive_factor)

        # 确定应用局部搜索的个体数量，采用自适应策略
        max_elite_percentage = 0.25
        min_elite_percentage = 0.1
        elite_percentage = min_elite_percentage + (max_elite_percentage - min_elite_percentage) * adaptive_factor
        elite_count = max(1, int(len(population) * elite_percentage))
        
        improved_count = 0  # 记录改进的个体数量
        
        # 对精英个体应用局部搜索
        for i in range(elite_count):
            # 基于概率决定是否应用局部搜索
            if random.random() < search_probability:
                individual = sorted_population[i]
                # 应用破坏-修复操作
                new_individual = apply_destroy_repair(individual, problem, current_gen, max_gen)
                new_individual.invalidate_cache()  # 使缓存失效
                
                # 评估新个体
                evaluate_individual_with_penalty(problem, new_individual, current_gen, max_gen)
                
                # 如果新个体更好，则替换原个体
                if new_individual.fitness > individual.fitness:
                    sorted_population[i] = new_individual
                    improved_count += 1
            
            # 动态调整：如果连续多个个体没有改进，提前退出
            if i >= 5 and improved_count == 0:
                break
        
        # 返回更新后的种群
        return sorted_population


def solve_vrpd(problem: Problem, random_seed: int, **kwargs) -> Tuple[Individual, Dict[str, List[Any]]]:
    """
    求解VRP-D问题的入口函数
    
    参数：
        problem: 问题实例
        random_seed: 随机数种子(必选)
        **kwargs: 算法参数
          - pop_size: 种群大小
          - max_runtime: 最大运行时间(秒)
          - max_generations: 最大代数(备选终止条件)
          - mutation_factor: 变异因子
          - crossover_rate: 交叉率
          - elite_ratio: 精英保留比例
          - gen_interval: 记录代数间隔
          - time_interval: 记录时间间隔(秒)
    
    返回：
        最优个体和算法历史记录的元组
    """
    # 设置随机数种子
    random.seed(random_seed)
    np.random.seed(random_seed)

    # 创建差分进化算法实例
    de = DifferentialEvolution(problem, **kwargs)
    
    # 设置记录间隔
    de.gen_interval = kwargs.get('gen_interval', 5)  # 默认每5代记录一次
    de.time_interval = kwargs.get('time_interval', 1)  # 默认每1秒记录一次
    
    # 初始化种群
    de.initialize()
    
    # 进化求解
    best_individual = de.evolve()
    
    return best_individual, de.history




