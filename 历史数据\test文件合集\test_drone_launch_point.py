import copy
from data_loader import Individual, Problem
from repair import check_and_repair_drone_launch_point

def test_drone_launch_point():
    """测试无人机客户点缺少前置发射点的修复功能"""
    print("开始测试无人机客户点前置发射点修复功能...")
    
    # 创建一个简单的模拟问题实例
    mock_problem = type('MockProblem', (), {
        'num_drones': 2,  # 每个发射点最多2个无人机
        'drone_params': {'max_load': 5.0},
        'locations': {i: (i, i) for i in range(15)},  # 简单坐标
        'demands': {i: (1.0, 1.0) for i in range(15)}  # 简单需求
    })()
    
    # 使用指定的染色体，其中包含无人机客户点没有前置发射点的情况
    path_layer    = [0, 0, 2, 8, 7, 5, 1, 3, 9, 6, 10, 4, 0]
    service_layer = [0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0]
    
    # 创建个体
    individual = Individual(chromosomes=(path_layer, service_layer))
    original_chromosomes = copy.deepcopy(individual.chromosomes)
    
    # 分析初始状态，检查无人机客户点的发射点情况
    print("\n修复前:")
    print("路径层:", path_layer)
    print("服务方式层:", service_layer)
    analyze_drone_launch_points(individual)
    
    # 调用修复函数
    print("\n调用修复函数...")
    result = check_and_repair_drone_launch_point(mock_problem, individual)
    
    # 打印修复结果
    path_after, service_after = individual.chromosomes
    print("\n修复后:")
    print("路径层:", path_after)
    print("服务方式层:", service_after)
    print("修复结果:", "原本可行" if result else "不可行，已修复")
    
    # 分析修复后的状态
    analyze_drone_launch_points(individual)
    
    # 比较修复前后的变化
    print("\n修复前后对比:")
    _, service_before = original_chromosomes
    _, service_after = individual.chromosomes
    
    changes = []
    for i, (before, after) in enumerate(zip(service_before, service_after)):
        if before != after:
            node = path_layer[i]
            changes.append((i, node, before, after))
    
    if changes:
        print("服务方式变化:")
        for idx, node, before, after in changes:
            print(f"  位置 {idx}: 客户点 {node} 从 {'无人机服务' if before==1 else '卡车服务'} 变为 {'无人机服务' if after==1 else '卡车服务'}")
    else:
        print("  无服务方式变化")
    
    # 验证修复是否成功（所有无人机客户点都有前置发射点）
    is_valid = validate_drone_launch_points(individual)
    print("\n最终验证:", "所有无人机客户点都有前置发射点" if is_valid else "仍有无人机客户点缺少前置发射点")

def analyze_drone_launch_points(individual):
    """分析个体中的无人机客户点是否有前置发射点"""
    path_layer, service_layer = individual.chromosomes
    
    # 找出所有无人机服务点
    drone_service_points = [(i, node) for i, (node, service) in enumerate(zip(path_layer, service_layer)) 
                           if service == 1 and node != 0]
    
    print("无人机服务点分析:")
    for idx, node in drone_service_points:
        # 向前查找最近的卡车服务点作为发射点
        launch_found = False
        launch_node = None
        
        for j in range(idx-1, -1, -1):
            if path_layer[j] != 0 and service_layer[j] == 0:
                launch_found = True
                launch_node = path_layer[j]
                break
        
        if launch_found:
            print(f"  客户点 {node} (位置 {idx}): 有前置发射点 {launch_node}")
        else:
            print(f"  客户点 {node} (位置 {idx}): 没有前置发射点！")

def validate_drone_launch_points(individual):
    """验证所有无人机客户点是否都有前置发射点"""
    path_layer, service_layer = individual.chromosomes
    
    # 找出所有无人机服务点
    drone_service_points = [(i, node) for i, (node, service) in enumerate(zip(path_layer, service_layer)) 
                           if service == 1 and node != 0]
    
    # 检查每个无人机服务点是否有前置发射点
    for idx, _ in drone_service_points:
        launch_found = False
        
        for j in range(idx-1, -1, -1):
            if path_layer[j] != 0 and service_layer[j] == 0:
                launch_found = True
                break
        
        if not launch_found:
            return False
    
    return True

if __name__ == "__main__":
    test_drone_launch_point()
