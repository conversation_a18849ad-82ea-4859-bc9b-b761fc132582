# 确定局部搜索概率（随着进化进行逐渐增加）
base_probability = 0.2
adaptive_factor = current_gen / max_gen
search_probability = base_probability + (0.9 * adaptive_factor)

# 确定应用局部搜索的个体数量，采用自适应策略
max_elite_percentage = 0.2
min_elite_percentage = 0.05
elite_percentage = min_elite_percentage + (max_elite_percentage - min_elite_percentage) * adaptive_factor
elite_count = max(1, int(len(population) * elite_percentage))


if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.5:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期


# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.4 * elapsed_time / self.max_runtime)


# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成


========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1009.65
最小总成本: 940.19 (运行 5)
最大总成本: 1078.14 (运行 4)
总成本标准差: 47.32

========== 算法精度与稳定性分析 ==========
最大偏差: 137.95 (14.67%)
平均偏差: 69.46 (7.39%)
平均求解时间: 302.02秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000978 | 1022.61 |    0.00 | 301.86
     2 |        2 | 0.001053 |  950.06 |    0.00 | 302.19
     3 |        3 | 0.000971 | 1017.00 |   12.55 | 301.86
     4 |        4 | 0.000928 | 1078.14 |    0.00 | 302.20
     5 |        5 | 0.001064 |  940.19 |    0.00 | 301.99
     6 |        6 | 0.000988 | 1011.95 |    0.00 | 301.79
     7 |        7 | 0.000936 | 1068.00 |    0.00 | 302.23
     8 |        8 | 0.000945 | 1058.41 |    0.00 | 302.31
     9 |        9 | 0.001049 |  953.63 |    0.00 | 301.82
    10 |       10 | 0.001004 |  996.50 |    0.00 | 301.98

最佳解详细信息:
运行ID: 5
适应度: 0.001064
总成本: 940.19
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 17, 18, 22, 19, 24, 21, 23, 27, 29, 28, 26, 25, 31, 30, 20, 16, 0, 52, 50, 53, 49, 51, 48, 59, 63, 56, 61, 57, 58, 64, 62, 65, 68, 67, 66, 69, 70, 71, 72, 78, 76, 75, 73, 74, 77, 80, 79, 1, 2, 60, 0, 34, 39, 41, 37, 38, 40, 36, 32, 35, 47, 46, 42, 43, 45, 44, 33, 55, 54, 0, 6, 3, 4, 7, 8, 5, 9, 10, 12, 13, 14, 11, 15, 0)
染色体 2: (0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0)

最佳解路线详情:
路线 1: [0, 17, 18, 22, 21, 23, 27, 26, 25, 20, 16, 0]
  无人机任务:
    从节点 22 发射无人机访问: [19, 24]
    从节点 27 发射无人机访问: [29, 28]
    从节点 25 发射无人机访问: [31, 30]
路线 2: [0, 52, 53, 49, 59, 63, 61, 64, 65, 68, 69, 71, 72, 78, 76, 75, 73, 74, 77, 80, 79, 1, 60, 0]
  无人机任务:
    从节点 52 发射无人机访问: [50]
    从节点 49 发射无人机访问: [51, 48]
    从节点 63 发射无人机访问: [56]
    从节点 61 发射无人机访问: [57, 58]
    从节点 64 发射无人机访问: [62]
    从节点 68 发射无人机访问: [67, 66]
    从节点 69 发射无人机访问: [70]
    从节点 1 发射无人机访问: [2]
路线 3: [0, 34, 39, 37, 36, 47, 43, 33, 0]
  无人机任务:
    从节点 39 发射无人机访问: [41]
    从节点 37 发射无人机访问: [38, 40]
    从节点 36 发射无人机访问: [32, 35]
    从节点 47 发射无人机访问: [46, 42]
    从节点 43 发射无人机访问: [45, 44]
    从节点 33 发射无人机访问: [55, 54]
路线 4: [0, 6, 7, 8, 10, 12, 14, 15, 0]
  无人机任务:
    从节点 6 发射无人机访问: [3, 4]
    从节点 8 发射无人机访问: [5, 9]
    从节点 12 发射无人机访问: [13]
    从节点 14 发射无人机访问: [11]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000493 | 2029.74 |    0.00
  10 | 0.000494 | 2023.32 |    0.00
  20 | 0.000494 | 2023.32 |    0.00
  30 | 0.000494 | 2023.32 |    0.00
  40 | 0.000497 | 2010.88 |    0.00
  50 | 0.000511 | 1955.99 |    0.00
  60 | 0.000520 | 1921.52 |    0.00
  70 | 0.000520 | 1921.52 |    0.00
  80 | 0.000520 | 1921.52 |    0.00
  90 | 0.000520 | 1921.52 |    0.00
 100 | 0.000565 | 1768.37 |    0.00
 110 | 0.000565 | 1768.37 |    0.00
 120 | 0.000565 | 1768.37 |    0.00
 130 | 0.000604 | 1655.67 |    0.00
 140 | 0.000604 | 1655.67 |    0.00
 150 | 0.000653 | 1530.27 |    0.00
 160 | 0.000653 | 1530.27 |    0.00
 170 | 0.000660 | 1514.43 |    0.00
 180 | 0.000693 | 1442.90 |    0.00
 190 | 0.000726 | 1377.27 |    0.00
 200 | 0.000728 | 1373.97 |    0.00
 210 | 0.000733 | 1365.02 |    0.00
 220 | 0.000756 | 1323.42 |    0.00
 230 | 0.000813 | 1229.30 |    0.00
 240 | 0.000829 | 1205.59 |    0.00
 250 | 0.000829 | 1205.59 |    0.00
 260 | 0.000862 | 1159.54 |    0.00
 270 | 0.000894 | 1119.00 |    0.00
 280 | 0.000936 | 1067.87 |    0.00
 290 | 0.000936 | 1067.87 |    0.00
 300 | 0.000975 | 1025.32 |    0.00
 310 | 0.000982 | 1018.83 |    0.00
 320 | 0.000991 | 1008.95 |    0.00
 330 | 0.000994 | 1005.78 |    0.00
 340 | 0.001019 |  981.44 |    0.00
 350 | 0.001023 |  977.84 |    0.00
 360 | 0.001064 |  940.19 |    0.00
 370 | 0.001064 |  940.19 |    0.00
