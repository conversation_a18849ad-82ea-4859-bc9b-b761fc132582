if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.6:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期

# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)

# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成
========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 996.65
最小总成本: 914.35 (运行 9)
最大总成本: 1090.21 (运行 4)
总成本标准差: 52.59

========== 算法精度与稳定性分析 ==========
最大偏差: 175.87 (19.23%)
平均偏差: 82.31 (9.00%)
平均求解时间: 302.43秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.001044 |  957.64 |    0.00 | 302.56
     2 |        2 | 0.000943 | 1060.22 |    0.00 | 302.10
     3 |        3 | 0.001000 |  999.89 |    0.00 | 301.90
     4 |        4 | 0.000917 | 1090.21 |    0.00 | 302.03
     5 |        5 | 0.000959 | 1042.80 |    0.00 | 302.02
     6 |        6 | 0.001065 |  939.08 |    0.00 | 305.70
     7 |        7 | 0.001038 |  963.77 |    0.00 | 301.84
     8 |        8 | 0.000998 | 1002.09 |    0.00 | 301.83
     9 |        9 | 0.001094 |  914.35 |    0.00 | 301.50
    10 |       10 | 0.001004 |  996.51 |    0.00 | 302.80

最佳解详细信息:
运行ID: 9
适应度: 0.001094
总成本: 914.35
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 16, 20, 23, 29, 31, 30, 26, 25, 27, 28, 41, 39, 38, 40, 37, 36, 35, 33, 32, 34, 0, 55, 49, 53, 54, 48, 51, 44, 46, 47, 43, 45, 42, 59, 52, 50, 64, 62, 61, 63, 56, 58, 57, 65, 66, 69, 68, 67, 70, 71, 72, 0, 6, 7, 5, 8, 9, 11, 14, 15, 10, 13, 12, 79, 77, 75, 80, 74, 73, 76, 78, 2, 1, 4, 3, 60, 0, 17, 18, 24, 19, 21, 22, 0)
染色体 2: (0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0)

最佳解路线详情:
路线 1: [0, 16, 20, 23, 29, 26, 25, 41, 39, 37, 33, 34, 0]
  无人机任务:
    从节点 29 发射无人机访问: [31, 30]
    从节点 25 发射无人机访问: [27, 28]
    从节点 39 发射无人机访问: [38, 40]
    从节点 37 发射无人机访问: [36, 35]
    从节点 33 发射无人机访问: [32]
路线 2: [0, 55, 54, 44, 43, 59, 64, 62, 61, 56, 57, 65, 66, 69, 70, 72, 0]
  无人机任务:
    从节点 55 发射无人机访问: [49, 53]
    从节点 54 发射无人机访问: [48, 51]
    从节点 44 发射无人机访问: [46, 47]
    从节点 43 发射无人机访问: [45, 42]
    从节点 59 发射无人机访问: [52, 50]
    从节点 61 发射无人机访问: [63]
    从节点 56 发射无人机访问: [58]
    从节点 69 发射无人机访问: [68, 67]
    从节点 70 发射无人机访问: [71]
路线 3: [0, 6, 7, 9, 11, 10, 79, 77, 74, 76, 78, 2, 1, 4, 3, 60, 0]
  无人机任务:
    从节点 7 发射无人机访问: [5, 8]
    从节点 11 发射无人机访问: [14, 15]
    从节点 10 发射无人机访问: [13, 12]
    从节点 77 发射无人机访问: [75, 80]
    从节点 74 发射无人机访问: [73]
路线 4: [0, 17, 18, 21, 0]
  无人机任务:
    从节点 18 发射无人机访问: [24, 19]
    从节点 21 发射无人机访问: [22]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000526 | 1900.07 |    0.00
  10 | 0.000526 | 1900.07 |    0.00
  20 | 0.000526 | 1900.07 |    0.00
  30 | 0.000619 | 1615.02 |    0.00
  40 | 0.000619 | 1615.02 |    0.00
  50 | 0.000619 | 1615.02 |    0.00
  60 | 0.000620 | 1613.50 |    0.00
  70 | 0.000623 | 1605.10 |    0.00
  80 | 0.000642 | 1556.52 |    0.00
  90 | 0.000698 | 1432.00 |    0.00
 100 | 0.000698 | 1432.00 |    0.00
 110 | 0.000720 | 1388.12 |    0.00
 120 | 0.000727 | 1376.14 |    0.00
 130 | 0.000772 | 1295.72 |    0.00
 140 | 0.000772 | 1294.61 |    0.00
 150 | 0.000782 | 1278.56 |    0.00
 160 | 0.000782 | 1278.56 |    0.00
 170 | 0.000782 | 1278.56 |    0.00
 180 | 0.000782 | 1278.56 |    0.00
 190 | 0.000789 | 1267.63 |    0.00
 200 | 0.000798 | 1252.40 |    0.00
 210 | 0.000804 | 1243.23 |    0.00
 220 | 0.000804 | 1243.23 |    0.00
 230 | 0.000888 | 1125.51 |    0.00
 240 | 0.000891 | 1122.21 |    0.00
 250 | 0.000940 | 1063.73 |    0.00
 260 | 0.000940 | 1063.73 |    0.00
 270 | 0.000940 | 1063.73 |    0.00
 280 | 0.000971 | 1029.48 |    0.00
 290 | 0.000972 | 1029.12 |    0.00
 300 | 0.000979 | 1020.95 |    0.00
 310 | 0.000985 | 1015.52 |    0.00
 320 | 0.001061 |  942.29 |    0.00
 330 | 0.001061 |  942.29 |    0.00
 340 | 0.001061 |  942.29 |    0.00
 350 | 0.001062 |  941.43 |    0.00
 360 | 0.001079 |  926.87 |    0.00
 370 | 0.001081 |  925.00 |    0.00
 380 | 0.001082 |  924.47 |    0.00
 390 | 0.001083 |  923.25 |    0.00
 400 | 0.001083 |  923.25 |    0.00
 410 | 0.001087 |  919.80 |    0.00
 420 | 0.001087 |  919.80 |    0.00
 430 | 0.001087 |  919.80 |    0.00
 440 | 0.001087 |  919.80 |    0.00
 450 | 0.001087 |  919.80 |    0.00
 460 | 0.001087 |  919.80 |    0.00
 470 | 0.001087 |  919.80 |    0.00
