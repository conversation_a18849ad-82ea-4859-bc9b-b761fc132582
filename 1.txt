# 创建一个 drone_tasks_extended 示例
drone_tasks_extended = {
    6: {
        'drone_tasks': [
            Drone<PERSON><PERSON>(1, [4, 7, 11], 6, 12, 156.8),
            DroneTask(2, [3, 9], 6, 12, 89.4)
        ],
        'recovery_point': 12
    },
    15: {
        'drone_tasks': [
            DroneTask(3, [14, 17, 20], 15, 22, 134.5)
        ],
        'recovery_point': 22
    },
    25: {
        'drone_tasks': [
            DroneTask(4, [1, 8], 25, 30, 78.9)
        ],
        'recovery_point': 30
    }
}

原始 drone_tasks_extended:
{6: {'drone_tasks': [...], 'recovery_point': 12}, 15: {'drone_tasks': [...], 'recovery_point': 22}, 25: {'drone_tasks': [...], 'recovery_point': 30}}

=== 演示 1: 获取字典的键 ===
字典的键: dict_keys([6, 15, 25])
键的类型: <class 'dict_keys'>

=== 演示 2: 将键转换为迭代器 ===
迭代器对象: <dict_keyiterator object at 0x...>
迭代器类型: <class 'dict_keyiterator'>

=== 演示 3: 使用 next() 获取元素 ===
第一个键: 6
第二个键: 15
第三个键: 25
迭代器已耗尽，没有更多元素了！

=== 演示 5: 获取第一个值（代码中的实际操作）===
第一个值: {'drone_tasks': [...], 'recovery_point': 12}
第一个值的类型: <class 'dict'>

=== 演示 6: 检查第一个值的格式 ===
✓ 这是扩展格式
  包含的键: dict_keys(['drone_tasks', 'recovery_point'])
  drone_tasks 数量: 2
  recovery_point: 12