# 创建一个 drone_tasks_extended 示例
drone_tasks_extended = {
    6: {
        'drone_tasks': [
            Drone<PERSON><PERSON>(1, [4, 7, 11], 6, 12, 156.8),
            DroneTask(2, [3, 9], 6, 12, 89.4)
        ],
        'recovery_point': 12
    },
    15: {
        'drone_tasks': [
            DroneTask(3, [14, 17, 20], 15, 22, 134.5)
        ],
        'recovery_point': 22
    },
    25: {
        'drone_tasks': [
            DroneTask(4, [1, 8], 25, 30, 78.9)
        ],
        'recovery_point': 30
    }
}

原始 drone_tasks_extended:
{6: {'drone_tasks': [...], 'recovery_point': 12}, 15: {'drone_tasks': [...], 'recovery_point': 22}, 25: {'drone_tasks': [...], 'recovery_point': 30}}

=== 演示 1: 获取字典的键 ===
字典的键: dict_keys([6, 15, 25])
键的类型: <class 'dict_keys'>

=== 演示 2: 将键转换为迭代器 ===
迭代器对象: <dict_keyiterator object at 0x...>
迭代器类型: <class 'dict_keyiterator'>

=== 演示 3: 使用 next() 获取元素 ===
第一个键: 6
第二个键: 15
第三个键: 25
迭代器已耗尽，没有更多元素了！

=== 演示 5: 获取第一个值（代码中的实际操作）===
第一个值: {'drone_tasks': [...], 'recovery_point': 12}
第一个值的类型: <class 'dict'>

=== 演示 6: 检查第一个值的格式 ===
✓ 这是扩展格式
  包含的键: dict_keys(['drone_tasks', 'recovery_point'])
  drone_tasks 数量: 2
  recovery_point: 12



Terminal
$ python decode_extended_detailed_explanation.py
Command
$ python decode_extended_detailed_explanation.py
Output
================================================================================
_decode_extended 函数逐行详细解释
================================================================================
📋 示例输入数据:
  path_layer:    [0, 6, 4, 7, 12, 0]
  service_layer: [0, 2, 1, 1, 3, 0]
  解释: 卡车从配送中心出发，在节点6发射无人机服务客户4和7，在节点12回收无人机，返回配送中心

================================================================================
第1步：输入处理和验证
================================================================================
代码行559：
  path_layer, service_layer = chromosomes if not hasattr(chromosomes, 'chromosomes') else chromosomes.chromosomes

逻辑解释：
  - 检查输入是否为Individual对象（有chromosomes属性）
  - 如果是普通元组，直接解包
  - 如果是Individual对象，提取其chromosomes属性

执行结果：
  path_layer = [0, 6, 4, 7, 12, 0]
  service_layer = [0, 2, 1, 1, 3, 0]

代码行561-562：
  if len(path_layer) != len(service_layer):
      raise ValueError('路径层和服务方式层长度不一致')

逻辑解释：
  - 验证两个层的长度必须相等
  - 每个路径节点都必须有对应的服务方式

执行结果：
  path_layer长度: 6
  service_layer长度: 6
  长度检查: ✅ 通过

================================================================================
第2步：路径分割
================================================================================
代码行565：
  depot_positions = [i for i, node in enumerate(path_layer) if node == 0]

逻辑解释：
  - 找到所有配送中心（节点0）在路径中的位置索引
  - 用于后续按配送中心分割路径段

执行过程：
  索引0: 节点0 ← 配送中心
  索引1: 节点6
  索引2: 节点4
  索引3: 节点7
  索引4: 节点12
  索引5: 节点0 ← 配送中心

执行结果：depot_positions = [0, 5]

代码行567-574：路径段分割循环
  routes_segments = []
  start_idx = 0
  for i in range(len(depot_positions) - 1):
      end_idx = depot_positions[i + 1]
      segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, end_idx + 1)]
      routes_segments.append(segment)
      start_idx = depot_positions[i + 1]

执行过程：
  depot_positions = [0, 5]
  需要处理 1 个路径段
  路径段1: 从索引0到5
    segment = [(0, 0), (6, 2), (4, 1), (7, 1), (12, 3), (0, 0)]

代码行577-579：处理最后一段
  if start_idx < len(path_layer):
      segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, len(path_layer))]
      routes_segments.append(segment)

  最后一段: 从索引5到5
    segment = [(0, 0)]

最终结果：routes_segments = [[(0, 0), (6, 2), (4, 1), (7, 1), (12, 3), (0, 0)], [(0, 0)]]

================================================================================
第3步：路径段解码
================================================================================
代码行582-590：初始化解码变量
  decoded_solution = []
  for segment in routes_segments:
      truck_route = []
      drone_tasks_extended = {}
      active_drone_tasks = {}  # {launch_point: {'customers': [], 'recovery_point': None}}
      drone_id_counter = 1

逻辑解释：
  - decoded_solution: 存储最终解码结果
  - truck_route: 当前路径段的卡车路径
  - drone_tasks_extended: 扩展格式的无人机任务
  - active_drone_tasks: 跟踪当前活跃的无人机任务
  - drone_id_counter: 无人机ID计数器

处理路径段: [(0, 0), (6, 2), (4, 1), (7, 1), (12, 3), (0, 0)]

代码行592-629：逐节点处理循环
  i = 0
  while i < len(segment):
      node, service = segment[i]
      # 根据服务方式处理...

================================================================================
第4步：逐节点处理详解
================================================================================
逐节点处理过程：

步骤1: 处理节点0，服务方式0
  代码行596-597：
    if service == 0:
        truck_route.append(node)
    → 添加到卡车路径: 0
  当前状态:
    truck_route = [0]
    active_drone_tasks = {}

步骤2: 处理节点6，服务方式2
  代码行606-609：
    elif service == 2:
        truck_route.append(node)
        active_drone_tasks[node] = {'customers': [], 'recovery_point': None}
    → 添加到卡车路径: 6
    → 开始新的无人机任务，发射点: 6
  当前状态:
    truck_route = [0, 6]
    active_drone_tasks = {6: {'customers': [], 'recovery_point': None}}

步骤3: 处理节点4，服务方式1
  代码行599-604：
    elif service == 1:
        if active_drone_tasks:
            latest_launch = max(active_drone_tasks.keys())
            active_drone_tasks[latest_launch]['customers'].append(node)
    → 添加客户4到发射点6的任务
  当前状态:
    truck_route = [0, 6]
    active_drone_tasks = {6: {'customers': [4], 'recovery_point': None}}

步骤4: 处理节点7，服务方式1
  代码行599-604：
    elif service == 1:
        if active_drone_tasks:
            latest_launch = max(active_drone_tasks.keys())
            active_drone_tasks[latest_launch]['customers'].append(node)
    → 添加客户7到发射点6的任务
  当前状态:
    truck_route = [0, 6]
    active_drone_tasks = {6: {'customers': [4, 7], 'recovery_point': None}}

步骤5: 处理节点12，服务方式3
  代码行611-617：
    elif service == 3:
        truck_route.append(node)
        for launch_point in sorted(active_drone_tasks.keys()):
            if active_drone_tasks[launch_point]['recovery_point'] is None:
                active_drone_tasks[launch_point]['recovery_point'] = node
                break
    → 添加到卡车路径: 12
    → 设置发射点6的回收点为: 12
  当前状态:
    truck_route = [0, 6, 12]
    active_drone_tasks = {6: {'customers': [4, 7], 'recovery_point': 12}}

步骤6: 处理节点0，服务方式0
  代码行596-597：
    if service == 0:
        truck_route.append(node)
    → 添加到卡车路径: 0
  当前状态:
    truck_route = [0, 6, 12, 0]
    active_drone_tasks = {6: {'customers': [4, 7], 'recovery_point': 12}}

================================================================================
第5步：DroneTask对象创建
================================================================================
代码行632-651：转换为DroneTask对象
  for launch_point, task_info in active_drone_tasks.items():
      if task_info['customers']:  # 只处理有客户的任务
          if task_info['customers']:
              drone_task = DroneTask(
                  drone_id=drone_id_counter,
                  customer_sequence=task_info['customers'],
                  launch_point=launch_point,
                  recovery_point=task_info['recovery_point'],
                  total_energy=0.0
              )
              drone_tasks_list = [drone_task]
              drone_id_counter += 1
          else:
              drone_tasks_list = []

执行过程：
  处理发射点6:
    客户列表: [4, 7]
    回收点: 12
    → 创建DroneTask对象:
      drone_id: 1
      customer_sequence: [4, 7]
      launch_point: 6
      recovery_point: 12
      total_energy: 0.0

代码行648-651：构建扩展格式
  drone_tasks_extended[launch_point] = {
      'drone_tasks': drone_tasks_list,
      'recovery_point': task_info['recovery_point']
  }
    → 添加到扩展格式: {'drone_tasks': [<data_loader.DroneTask object at 0x000001E50F079D08>], 'recovery_point': 12}       

================================================================================
第6步：最终结果组装
================================================================================
代码行653：
  decoded_solution.append((truck_route, drone_tasks_extended))

逻辑解释：
  - 将卡车路径和扩展无人机任务组成元组
  - 添加到解码结果列表中

执行结果：
  truck_route = [0, 6, 12, 0]
  drone_tasks_extended = {6: {'drone_tasks': [<data_loader.DroneTask object at 0x000001E510B2AD08>], 'recovery_point': 12}}
  decoded_solution = [
    ([0, 6, 12, 0], {6: {'drone_tasks': [<data_loader.DroneTask object at 0x000001E510B2AD08>], 'recovery_point': 12}})   
  ]

代码行655：
  return decoded_solution

最终返回格式：
  [
    (truck_route, drone_tasks_extended),
    ...
  ]

================================================================================
完整示例演练
================================================================================
输入:
  chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])

调用函数:
输出:
  result = [([0, 6, 12, 0], {6: {'drone_tasks': [<data_loader.DroneTask object at 0x000001E51777B488>], 'recovery_point': 12}}), ([0], {})]

结果解析:
  卡车路径: [0, 6, 12, 0]
  无人机任务:
    发射点6:
      回收点: 12
      任务数量: 1
      任务1: DroneTask(id=1, customers=[4, 7])