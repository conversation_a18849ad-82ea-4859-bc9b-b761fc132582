#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解码函数的详细数据格式说明和示例
包含输入输出变量的完整数据结构解释
"""

from data_loader import Individual, DroneTask

def explain_input_formats():
    """解释输入数据格式"""
    print("=" * 80)
    print("解码函数输入数据格式详解")
    print("=" * 80)
    
    print("📋 输入参数: chromosomes")
    print("-" * 50)
    
    print("格式1: 元组格式 (path_layer, service_layer)")
    print("  类型: Tuple[List[int], List[int]]")
    print("  结构:")
    print("    path_layer: List[int] - 路径层")
    print("      示例: [0, 6, 4, 7, 12, 0]")
    print("      说明:")
    print("        - 0: 配送中心")
    print("        - 6, 12: 发射点/回收点")
    print("        - 4, 7: 无人机客户点")
    print()
    
    print("    service_layer: List[int] - 服务方式层")
    print("      示例: [0, 2, 1, 1, 3, 0]")
    print("      说明:")
    print("        - 0: 配送中心或仅卡车服务")
    print("        - 1: 无人机客户点")
    print("        - 2: 发射点+卡车服务")
    print("        - 3: 回收点+卡车服务")
    print("        - 4: 回收+发射+卡车服务")
    print()
    
    print("格式2: Individual对象")
    print("  类型: Individual")
    print("  属性: chromosomes.chromosomes = (path_layer, service_layer)")
    print("  用法: 当输入为Individual实例时，自动提取chromosomes属性")
    print()
    
    print("📋 输入参数: problem")
    print("-" * 50)
    print("  类型: Problem 或 None")
    print("  用途: 提供问题实例信息，用于:")
    print("    - 计算客户点间距离")
    print("    - 计算无人机能耗")
    print("    - 获取客户需求信息")
    print("  默认: None (使用默认值)")
    print()

def explain_traditional_output():
    """解释传统解码输出格式"""
    print("=" * 80)
    print("传统解码 (_decode_traditional) 输出格式详解")
    print("=" * 80)
    
    print("📋 返回类型: List[Tuple[List[int], Dict[int, List[int]]]]")
    print("-" * 60)
    
    print("结构层次:")
    print("  Level 1: List - 路径段列表")
    print("    └── Level 2: Tuple - 单个路径段")
    print("        ├── truck_route: List[int] - 卡车路径")
    print("        └── drone_tasks: Dict[int, List[int]] - 无人机任务")
    print()
    
    print("详细格式:")
    print("  [")
    print("    (  # 第一个路径段")
    print("      [0, 6, 12, 0],  # truck_route: 卡车路径")
    print("      {               # drone_tasks: 无人机任务字典")
    print("        6: [4, 7, 11],    # 发射点6 -> 客户列表[4, 7, 11]")
    print("        8: [3, 9],        # 发射点8 -> 客户列表[3, 9]")
    print("      }")
    print("    ),")
    print("    (  # 第二个路径段（如果有多个路径段）")
    print("      [0, 15, 18, 0],")
    print("      {15: [2, 5]}")
    print("    )")
    print("  ]")
    print()
    
    print("数据类型详解:")
    print("  truck_route: List[int]")
    print("    - 包含卡车实际访问的所有节点")
    print("    - 包括配送中心、发射点、回收点")
    print("    - 不包括无人机客户点")
    print("    - 示例: [0, 6, 12, 0]")
    print()
    
    print("  drone_tasks: Dict[int, List[int]]")
    print("    - 键(int): 发射点ID")
    print("    - 值(List[int]): 该发射点服务的客户ID列表")
    print("    - 客户按服务顺序排列")
    print("    - 示例: {6: [4, 7, 11], 8: [3, 9]}")
    print()

def explain_extended_output():
    """解释扩展解码输出格式"""
    print("=" * 80)
    print("扩展解码 (_decode_extended) 输出格式详解")
    print("=" * 80)
    
    print("📋 返回类型: List[Tuple[List[int], Dict[int, Dict]]]")
    print("-" * 60)
    
    print("结构层次:")
    print("  Level 1: List - 路径段列表")
    print("    └── Level 2: Tuple - 单个路径段")
    print("        ├── truck_route: List[int] - 卡车路径")
    print("        └── drone_tasks_extended: Dict[int, Dict] - 扩展无人机任务")
    print("            └── Level 3: Dict - 单个发射点的任务信息")
    print("                ├── 'drone_tasks': List[DroneTask] - DroneTask对象列表")
    print("                └── 'recovery_point': int - 回收点ID")
    print()
    
    print("详细格式:")
    print("  [")
    print("    (  # 第一个路径段")
    print("      [0, 6, 12, 0],  # truck_route: 卡车路径")
    print("      {               # drone_tasks_extended: 扩展无人机任务")
    print("        6: {          # 发射点6的任务信息")
    print("          'drone_tasks': [")
    print("            DroneTask(")
    print("              drone_id=1,")
    print("              customer_sequence=[4, 7, 11],")
    print("              launch_point=6,")
    print("              recovery_point=12,")
    print("              total_energy=156.8")
    print("            ),")
    print("            DroneTask(...)  # 可能有多个DroneTask")
    print("          ],")
    print("          'recovery_point': 12")
    print("        },")
    print("        8: {          # 发射点8的任务信息")
    print("          'drone_tasks': [DroneTask(...)],")
    print("          'recovery_point': 15")
    print("        }")
    print("      }")
    print("    )")
    print("  ]")
    print()
    
    print("数据类型详解:")
    print("  truck_route: List[int] - 与传统格式相同")
    print("    示例: [0, 6, 12, 0]")
    print()
    
    print("  drone_tasks_extended: Dict[int, Dict]")
    print("    - 键(int): 发射点ID")
    print("    - 值(Dict): 该发射点的详细任务信息")
    print("      ├── 'drone_tasks': List[DroneTask] - DroneTask对象列表")
    print("      └── 'recovery_point': int - 回收点ID")
    print()
    
    print("  DroneTask对象属性:")
    print("    - drone_id: int - 无人机ID")
    print("    - customer_sequence: List[int] - 客户服务序列")
    print("    - launch_point: int - 发射点ID")
    print("    - recovery_point: int - 回收点ID")
    print("    - total_energy: float - 总能耗")
    print()

def show_practical_examples():
    """展示实际示例"""
    print("=" * 80)
    print("实际示例对比")
    print("=" * 80)
    
    # 示例输入
    chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
    
    print("📋 输入示例:")
    print(f"  chromosomes = {chromosomes}")
    print("  解释: 卡车从配送中心出发，在节点6发射无人机服务客户4和7，在节点12回收，返回配送中心")
    print()
    
    print("📋 传统解码输出:")
    result_traditional = Individual._decode_traditional(chromosomes)
    print(f"  result = {result_traditional}")
    print()
    print("  解析:")
    if result_traditional:
        truck_route, drone_tasks = result_traditional[0]
        print(f"    卡车路径: {truck_route}")
        print(f"    无人机任务: {drone_tasks}")
        print(f"    数据类型: truck_route={type(truck_route)}, drone_tasks={type(drone_tasks)}")
        if drone_tasks:
            for launch_point, customers in drone_tasks.items():
                print(f"      发射点{launch_point}: 服务客户{customers} (类型: {type(customers)})")
    print()
    
    print("📋 扩展解码输出:")
    result_extended = Individual._decode_extended(chromosomes)
    print(f"  result = {result_extended}")
    print()
    print("  解析:")
    if result_extended:
        truck_route, drone_tasks_extended = result_extended[0]
        print(f"    卡车路径: {truck_route}")
        print(f"    扩展无人机任务: {drone_tasks_extended}")
        print(f"    数据类型: truck_route={type(truck_route)}, drone_tasks_extended={type(drone_tasks_extended)}")
        if drone_tasks_extended:
            for launch_point, task_info in drone_tasks_extended.items():
                print(f"      发射点{launch_point}:")
                print(f"        回收点: {task_info.get('recovery_point')} (类型: {type(task_info.get('recovery_point'))})")
                drone_tasks_list = task_info.get('drone_tasks', [])
                print(f"        任务列表: {len(drone_tasks_list)}个DroneTask对象 (类型: {type(drone_tasks_list)})")
                for i, drone_task in enumerate(drone_tasks_list):
                    print(f"          任务{i+1}: {type(drone_task).__name__}")
                    print(f"            drone_id: {drone_task.drone_id}")
                    print(f"            customer_sequence: {drone_task.customer_sequence}")
                    print(f"            launch_point: {drone_task.launch_point}")
                    print(f"            recovery_point: {drone_task.recovery_point}")

def show_format_comparison():
    """展示格式对比"""
    print("=" * 80)
    print("传统格式 vs 扩展格式对比")
    print("=" * 80)
    
    comparison_table = [
        ["特性", "传统格式", "扩展格式"],
        ["-" * 20, "-" * 30, "-" * 40],
        ["数据结构", "简单字典", "复杂嵌套字典"],
        ["无人机任务", "List[int] 客户ID列表", "List[DroneTask] 对象列表"],
        ["任务信息", "仅客户ID", "完整任务信息(ID,能耗,回收点等)"],
        ["回收点信息", "❌ 不包含", "✅ 明确包含"],
        ["能耗信息", "❌ 不包含", "✅ 包含total_energy"],
        ["无人机ID", "❌ 不包含", "✅ 包含drone_id"],
        ["内存占用", "较小", "较大"],
        ["处理速度", "较快", "较慢"],
        ["信息完整性", "基础", "完整"],
        ["适用场景", "传统VRP-D算法", "高级分析和研究"]
    ]
    
    for row in comparison_table:
        print(f"{row[0]:<20} {row[1]:<30} {row[2]:<40}")
    print()

if __name__ == "__main__":
    explain_input_formats()
    explain_traditional_output()
    explain_extended_output()
    show_practical_examples()
    show_format_comparison()
