NAME : R101-31-3
COMMENT : (<PERSON> dataset, Modified for VRPD)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 8.75 8.75
 1 5.00 12.50
 2 7.50 15.00
 3 2.50 5.00
 4 5.00 10.00
 5 11.25 5.00
 6 7.75 13.00
 7 8.75 17.25
 8 16.25 13.75
 9 0.50 15.00
 10 10.50 1.75
 11 15.75 5.75
 12 8.00 3.00
 13 9.00 6.50
 14 5.25 6.00
 15 4.25 8.50
 16 3.00 6.00
 17 6.75 17.25
 18 15.50 19.25
 19 16.75 1.25
 20 14.00 9.75
 21 14.25 17.00
 22 11.75 4.00
 23 13.25 10.75
 24 14.25 12.00
 25 3.50 9.25
 26 1.00 4.50
 27 6.50 13.00
 28 7.75 16.75
 29 4.75 5.25
 30 5.00 6.50

DEMAND_SECTION
 0 0 0      
 1 0.0 4.8      // 仅取货需求客户 - light包裹
 2 2.6 2.4      // 送货取货双需求客户 - light包裹
 3 7.7 0.0      // 仅送货需求客户 - medium包裹
 4 4.2 0.0      // 仅送货需求客户 - light包裹
 5 7.5 0.0      // 仅送货需求客户 - medium包裹
 6 0.0 2.9      // 仅取货需求客户 - light包裹
 7 0.0 3.7      // 仅取货需求客户 - light包裹
 8 3.9 3.4      // 送货取货双需求客户 - light包裹
 9 9.9 5.8      // 送货取货双需求客户 - medium包裹
 10 8.0 0.0      // 仅送货需求客户 - medium包裹
 11 1.3 0.0      // 仅送货需求客户 - light包裹
 12 0.0 4.5      // 仅取货需求客户 - light包裹
 13 2.0 1.3      // 送货取货双需求客户 - light包裹
 14 2.4 0.0      // 仅送货需求客户 - light包裹
 15 6.5 0.0      // 仅送货需求客户 - medium包裹
 16 0.0 14.3      // 仅取货需求客户 - heavy包裹
 17 7.4 5.8      // 送货取货双需求客户 - medium包裹
 18 4.8 0.0      // 仅送货需求客户 - light包裹
 19 1.3 0.0      // 仅送货需求客户 - light包裹
 20 0.0 10.5      // 仅取货需求客户 - heavy包裹
 21 2.0 0.0      // 仅送货需求客户 - light包裹
 22 8.1 2.7      // 送货取货双需求客户 - medium包裹
 23 1.5 1.5      // 送货取货双需求客户 - light包裹
 24 2.0 0.0      // 仅送货需求客户 - light包裹
 25 0.0 3.4      // 仅取货需求客户 - light包裹
 26 17.2 0.0      // 仅送货需求客户 - heavy包裹
 27 1.7 0.0      // 仅送货需求客户 - light包裹
 28 3.3 0.0      // 仅送货需求客户 - light包裹
 29 7.9 0.0      // 仅送货需求客户 - medium包裹
 30 6.9 0.0      // 仅送货需求客户 - medium包裹

DEPOT_SECTION
 0
 -1
EOF