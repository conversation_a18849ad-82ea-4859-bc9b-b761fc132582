import unittest
from data_loader import Problem, Customer

class TestProblemFunctions(unittest.TestCase):
    """测试Problem类的各项函数"""
    
    def setUp(self):
        """测试前准备工作"""
        self.vrp_file = "A-n32-k2-d4.vrp"
        # 创建Problem实例但不自动加载数据
        self.problem = Problem(None)
    
    def test_load_data(self):
        """测试_load_data函数"""
        print("\n---- 测试 _load_data 函数 ----")
        
        # 手动调用_load_data函数
        self.problem._load_data(self.vrp_file)
        
        # 输出加载的所有数据
        print(f"基本信息:")
        print(f"  名称: {self.problem.name}")
        print(f"  维度: {self.problem.dimension}")
        print(f"  客户点数量: {self.problem.num_customers}")
        print(f"  卡车数量: {self.problem.num_vehicles}")
        print(f"  无人机数量: {self.problem.num_drones}")
        
        print("\n配送中心信息:")
        if self.problem.depot:
            print(f"  ID: {self.problem.depot.id}")
            print(f"  坐标: ({self.problem.depot.x}, {self.problem.depot.y})")
            print(f"  需求: ({self.problem.depot.pickup}, {self.problem.depot.delivery})")
        else:
            print("  配送中心未加载")
        
        print("\n客户点信息 (前5个):")
        for i, customer in enumerate(self.problem.customers[:5]):
            print(f"  客户 {customer.id}: 坐标({customer.x}, {customer.y}), 需求({customer.pickup}, {customer.delivery})")
        
        print("\n节点坐标 (前5个):")
        for i, (x, y) in enumerate(self.problem.nodes[:5]):
            print(f"  节点 {i}: ({x}, {y})")
        
        print("\n需求数据 (前5个):")
        count = 0
        for node_id, (pickup, delivery) in self.problem.demands.items():
            if count < 5:
                print(f"  节点 {node_id}: ({pickup}, {delivery})")
                count += 1
        
        # 验证基本属性
        self.assertIsNotNone(self.problem.name)
        self.assertGreater(self.problem.dimension, 0)
        self.assertGreater(len(self.problem.customers), 0)
    
    def test_update_params_dict(self):
        """测试_update_params_dict函数"""
        print("\n---- 测试 _update_params_dict 函数 ----")
        
        # 确保有数据可用
        if not hasattr(self.problem, 'name') or not self.problem.name:
            self.problem._load_data(self.vrp_file)
        
        # 手动调用_update_params_dict函数
        self.problem._update_params_dict()
        
        # 输出卡车参数字典
        print("\n卡车参数字典:")
        for key, value in self.problem.truck_params.items():
            print(f"  {key}: {value}")
        
        # 输出无人机参数字典
        print("\n无人机参数字典:")
        for key, value in self.problem.drone_params.items():
            print(f"  {key}: {value}")
        
        # 验证参数字典
        self.assertIsNotNone(self.problem.truck_params)
        self.assertIsNotNone(self.problem.drone_params)
        self.assertEqual(self.problem.truck_params['num_vehicles'], self.problem.num_vehicles)
        self.assertEqual(self.problem.drone_params['num_drones'], self.problem.num_drones)

if __name__ == '__main__':
    print("开始测试Problem类函数...")
    unittest.main()