import random
import os
import numpy as np
from sklearn.cluster import KMeans
from typing import List, Dict, Tu<PERSON>, Set
import matplotlib.pyplot as plt

def read_vrp_file(file_path: str) -> Dict:
    """读取VRP文件，提取节点坐标和需求信息"""
    
    data = {
        'header_info': [],
        'nodes': {},
        'demands': {}
    }
    
    current_section = None
    header_done = False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('//'):
            continue
        
        # 处理文件头部
        if 'NODE_COORD_SECTION' in line:
            current_section = 'coords'
            data['header_info'].append(line)
            continue
        elif 'DEMAND_SECTION' in line:
            current_section = 'demands'
            data['header_info'].append(line)
            continue
        elif 'DEPOT_SECTION' in line:
            current_section = 'depot'
            data['header_info'].append(line)
            continue
        elif 'CUSTOMER' in line:
            current_section = 'customer'
            data['header_info'].append(line)
            continue
        elif current_section is None:
            data['header_info'].append(line)
            continue
        
        # 处理节点坐标
        if current_section == 'coords':
            parts = line.split()
            if len(parts) >= 3:
                try:
                    node_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    data['nodes'][node_id] = (x_coord, y_coord)
                except ValueError:
                    continue
        
        # 处理需求数据
        elif current_section == 'demands':
            parts = line.split('//')
            if len(parts) >= 1:
                demand_parts = parts[0].strip().split()
                if len(demand_parts) >= 2:
                    try:
                        node_id = int(demand_parts[0])
                        
                        # 根据实际格式提取需求信息
                        if len(demand_parts) >= 3:
                            delivery = float(demand_parts[1])
                            pickup = float(demand_parts[2])
                        else:
                            delivery = float(demand_parts[1])
                            pickup = 0.0
                            
                        comment = ""
                        if len(parts) > 1:
                            comment = f"// {parts[1].strip()}"
                        data['demands'][node_id] = (delivery, pickup, comment)
                    except ValueError:
                        continue
                        
        # 处理客户数据（Solomon格式）
        elif current_section == 'customer':
            # 跳过表头行
            if "CUST NO." in line or not any(c.isdigit() for c in line):
                continue
                
            parts = line.split()
            if len(parts) >= 4:  # 至少需要节点ID、X坐标、Y坐标和需求
                try:
                    node_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    demand = float(parts[3])
                    
                    # 保存节点坐标
                    data['nodes'][node_id] = (x_coord, y_coord)
                    
                    # 保存需求信息（这里假设所有需求都是送货需求）
                    data['demands'][node_id] = (demand, 0.0, "")
                except ValueError:
                    continue
    
    return data

def perform_clustering(nodes_dict: Dict[int, Tuple[float, float]], customer_count: int) -> Dict[int, List[int]]:
    """
    对客户点坐标进行聚类分析，根据客户点数量动态调整聚类数
    
    参数:
        nodes_dict: 节点ID到坐标的映射
        customer_count: 客户点数量，用于确定聚类数
        
    返回:
        聚类ID到节点ID列表的映射
    """
    # 排除配送中心(ID=0)
    customer_nodes = {k: v for k, v in nodes_dict.items() if k > 0}
    
    # 提取坐标
    node_ids = list(customer_nodes.keys())
    coords = np.array([customer_nodes[nid] for nid in node_ids])
    
    # 根据客户点数量确定聚类数
    if customer_count <= 20:
        num_clusters = random.randint(1, 2)  # 20个客户点用2-3个聚类
    elif customer_count <= 30:
        num_clusters = random.randint(2, 3)  # 30个客户点用3-4个聚类
    elif customer_count <= 50:
        num_clusters = random.randint(3, 4)  # 50个客户点用4-5个聚类
    elif customer_count <= 60:
        num_clusters = random.randint(3, 5)  # 60个客户点用5-6个聚类
    else:
        num_clusters = random.randint(5, 7)  # 更多客户点用6-8个聚类
    
    print(f"客户点数量: {customer_count}, 使用聚类数: {num_clusters}")
    
    # 进行K-means聚类
    kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)
    clusters = kmeans.fit_predict(coords)
    
    # 将节点ID按聚类分组
    cluster_mapping = {}
    for i, cluster_id in enumerate(clusters):
        if cluster_id not in cluster_mapping:
            cluster_mapping[cluster_id] = []
        cluster_mapping[cluster_id].append(node_ids[i])
    
    return cluster_mapping

def create_clustered_sampling(original_data: Dict, 
                             customer_count: int, 
                             instance_id: int, 
                             output_dir: str,
                             scale_factor: float = 0.6) -> str:
    """
    创建保持聚类结构的采样数据集，并缩放坐标
    
    参数:
        original_data: 原始数据集
        customer_count: 客户点数量
        instance_id: 实例ID（1, 2, 3等）
        output_dir: 输出目录
        scale_factor: 坐标缩放因子(0.6表示缩小为原来的60%)
        
    返回:
        生成的文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 文件名
    filename = f"C101-{customer_count+1}-{instance_id}.vrp"
    output_path = os.path.join(output_dir, filename)
    
    # 提取配送中心（ID=0）并缩放坐标
    depot_x, depot_y = original_data['nodes'][0]
    depot = (depot_x * scale_factor, depot_y * scale_factor)
    
    depot_demand = original_data['demands'].get(0, (0, 0, "// 配送中心"))
    
    # 对客户点进行聚类，传入客户点数量以动态确定聚类数
    cluster_mapping = perform_clustering(original_data['nodes'], customer_count)
    
    # 计算每个聚类需要抽取的点数
    total_customer_nodes = sum(len(nodes) for nodes in cluster_mapping.values())
    
    selected_customers = []
    remaining_count = customer_count
    
    # 按比例从每个聚类中抽取客户点
    for cluster_id, nodes in cluster_mapping.items():
        # 计算这个聚类应该抽取的点数（按比例）
        if cluster_id == list(cluster_mapping.keys())[-1]:
            # 最后一个聚类，取剩余全部点数
            sample_count = remaining_count
        else:
            sample_count = max(1, round(len(nodes) / total_customer_nodes * customer_count))
            # 确保不会超过剩余需要的点数
            sample_count = min(sample_count, remaining_count)
        
        # 随机抽取改为优先选择靠近聚类中心的点
        if sample_count > len(nodes):
            sampled_nodes = nodes  # 如果需要的点数大于聚类中的点数，取全部
        else:
            # 计算聚类中心
            cluster_coords = np.array([original_data['nodes'][nid] for nid in nodes])
            center = np.mean(cluster_coords, axis=0)
            
            # 计算每个点到聚类中心的距离
            distances = []
            for nid in nodes:
                coord = original_data['nodes'][nid]
                dist = np.sqrt((coord[0] - center[0])**2 + (coord[1] - center[1])**2)
                distances.append((nid, dist))
            
            # 按距离排序，优先选择靠近中心的点
            distances.sort(key=lambda x: x[1])
            sampled_nodes = [nid for nid, _ in distances[:sample_count]]
        
        selected_customers.extend(sampled_nodes)
        remaining_count -= len(sampled_nodes)
        
        # 如果已经抽取了足够的点，就停止
        if remaining_count <= 0:
            break
    
    # 如果还需要更多点（可能由于四舍五入导致未取足）
    if remaining_count > 0:
        # 从所有未选点中随机选择
        unselected = [node_id for node_id in original_data['nodes'].keys() 
                     if node_id > 0 and node_id not in selected_customers]
        
        if len(unselected) >= remaining_count:
            extra_nodes = random.sample(unselected, remaining_count)
            selected_customers.extend(extra_nodes)
    
    # 排序，确保ID顺序
    selected_customers.sort()
    
    # 创建新文件内容
    new_content = []
    
    # 添加文件头部信息
    new_content.append(f"NAME : {filename[:-4]}")
    new_content.append("COMMENT : (Solomon dataset, Modified for VRPD with scaled coordinates)")
    new_content.append("TYPE : VRPD")
    new_content.append(f"DIMENSION : {customer_count+1}")
    new_content.append("EDGE_WEIGHT_TYPE : EUC_2D")
    new_content.append(f"NUM_CUSTOMERS : {customer_count}")
    
    # 添加节点坐标
    new_content.append("")
    new_content.append("NODE_COORD_SECTION")
    # 添加配送中心节点(缩放后的坐标)
    new_content.append(f" 0 {depot[0]:.1f} {depot[1]:.1f}")
    
    # 重新编号客户点（从1开始）并缩放坐标
    for new_id, orig_id in enumerate(selected_customers, 1):
        x, y = original_data['nodes'][orig_id]
        # 缩放坐标
        scaled_x = x * scale_factor
        scaled_y = y * scale_factor
        new_content.append(f" {new_id} {scaled_x:.1f} {scaled_y:.1f}")
    
    # 添加需求部分
    new_content.append("")
    new_content.append("DEMAND_SECTION")
    
    # 添加配送中心需求
    delivery, pickup, comment = depot_demand
    new_content.append(f" 0 {int(delivery)} {int(pickup)}      {comment}")
    
    # 根据客户点比例划分需求类型
    num_both_demands = int(customer_count * 0.25)  # 25%有送货和取货需求
    num_delivery_only = int(customer_count * 0.55)  # 55%仅有送货需求
    num_pickup_only = customer_count - num_both_demands - num_delivery_only  # 剩余为仅取货需求
    
    # 创建客户点的需求类型列表
    demand_types = ['both'] * num_both_demands + ['delivery'] * num_delivery_only + ['pickup'] * num_pickup_only
    random.shuffle(demand_types)  # 随机打乱
    
    # 重新编号客户点（从1开始）并生成需求
    for new_id, orig_id in enumerate(selected_customers, 1):
        # 确定需求类型
        demand_type = demand_types[new_id - 1]
        
        # 根据包裹重量分布生成包裹重量
        weight_type = random.choices(
            ['light', 'medium', 'heavy'],
            weights=[0.8, 0.15, 0.05],
            k=1
        )[0]
        
        # 根据重量类型确定具体重量
        if weight_type == 'light':
            weight = round(random.uniform(1, 5), 1)  # 轻小件: 1-5kg
        elif weight_type == 'medium':
            weight = round(random.uniform(5.1, 10), 1)  # 中型件: 5.1-10kg
        else:
            weight = round(random.uniform(10.1, 20), 1)  # 大件: 10.1-20kg
        
        # 根据需求类型设置送货和取货重量
        if demand_type == 'both':
            delivery = weight
            pickup = round(random.uniform(1, 10), 1)  # 取货重量1-10kg
            comment = f"// 送货取货双需求客户 - {weight_type}包裹"
        elif demand_type == 'delivery':
            delivery = weight
            pickup = 0
            comment = f"// 仅送货需求客户 - {weight_type}包裹"
        else:  # pickup only
            delivery = 0
            pickup = weight
            comment = f"// 仅取货需求客户 - {weight_type}包裹"
            
        new_content.append(f" {new_id} {delivery:.1f} {pickup:.1f}      {comment}")
    # 重新编号客户点（从1开始）
    for new_id, orig_id in enumerate(selected_customers, 1):
        if orig_id in original_data['demands']:
            delivery, pickup, comment = original_data['demands'][orig_id]
            new_content.append(f" {new_id} {int(delivery)} {int(pickup)}      {comment}")
        else:
            # 如果没有需求数据，则使用默认值
            new_content.append(f" {new_id} 10 0      // 默认需求")
    
    # 添加配送中心部分
    new_content.append("")
    new_content.append("DEPOT_SECTION")
    new_content.append(" 0")
    new_content.append("-1")
    new_content.append("EOF")
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(new_content))
    
    print(f"已创建数据集: {output_path}")
    return output_path

def visualize_clusters_with_centers(nodes_dict: Dict[int, Tuple[float, float]], 
                                   cluster_mapping: Dict[int, List[int]],
                                   title: str,
                                   output_dir: str = "clustering_plots") -> None:
    """可视化聚类结果，并显示聚类中心"""
    plt.figure(figsize=(12, 10))
    
    # 为每个聚类使用不同颜色
    colors = plt.cm.tab10(np.linspace(0, 1, len(cluster_mapping)))
    
    # 绘制每个聚类的点和中心
    for i, (cluster_id, nodes) in enumerate(cluster_mapping.items()):
        # 提取坐标
        x = [nodes_dict[nid][0] for nid in nodes]
        y = [nodes_dict[nid][1] for nid in nodes]
        
        # 绘制点
        plt.scatter(x, y, c=[colors[i]], s=50, label=f'聚类 {cluster_id+1}')
        
        # 计算并绘制聚类中心
        if nodes:
            center_x = sum(x) / len(x)
            center_y = sum(y) / len(y)
            plt.scatter(center_x, center_y, c=[colors[i]], marker='*', s=300, edgecolors='black')
    
    # 标记配送中心
    if 0 in nodes_dict:
        depot_x, depot_y = nodes_dict[0]
        plt.scatter(depot_x, depot_y, c='red', marker='s', s=200, label='配送中心')
    
    plt.title(f"聚类分布 - {title}")
    plt.xlabel("X坐标")
    plt.ylabel("Y坐标")
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 保存图像
    os.makedirs(output_dir, exist_ok=True)
    clean_title = title.replace(" ", "_")
    plt.savefig(f"{output_dir}/{clean_title}_clusters.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已生成聚类分布图: {clean_title}_clusters.png")

def evaluate_clustering(nodes_dict: Dict[int, Tuple[float, float]], 
                       cluster_mapping: Dict[int, List[int]]) -> Dict:
    """评估聚类质量"""
    # 计算每个聚类的中心
    cluster_centers = {}
    for cluster_id, nodes in cluster_mapping.items():
        if nodes:
            coords = np.array([nodes_dict[nid] for nid in nodes])
            cluster_centers[cluster_id] = np.mean(coords, axis=0)
    
    # 计算每个聚类的内部距离（点到中心的平均距离）
    intra_cluster_distances = {}
    for cluster_id, nodes in cluster_mapping.items():
        if nodes and cluster_id in cluster_centers:
            center = cluster_centers[cluster_id]
            distances = [np.linalg.norm(np.array(nodes_dict[nid]) - center) for nid in nodes]
            intra_cluster_distances[cluster_id] = np.mean(distances)
    
    # 计算聚类间距离（中心点之间的距离）
    inter_cluster_distances = []
    for i in cluster_centers:
        for j in cluster_centers:
            if i < j:  # 避免重复计算
                dist = np.linalg.norm(cluster_centers[i] - cluster_centers[j])
                inter_cluster_distances.append(dist)
    
    # 计算轮廓系数的简化版本
    avg_intra_distance = np.mean(list(intra_cluster_distances.values())) if intra_cluster_distances else 0
    avg_inter_distance = np.mean(inter_cluster_distances) if inter_cluster_distances else 0
    
    # 如果聚类间距离远大于聚类内距离，则聚类效果好
    clustering_quality = avg_inter_distance / avg_intra_distance if avg_intra_distance > 0 else 0
    
    return {
        'intra_cluster_distances': intra_cluster_distances,
        'avg_intra_distance': avg_intra_distance,
        'avg_inter_distance': avg_inter_distance,
        'clustering_quality': clustering_quality
    }

def main():
    # 设置随机种子以确保可重复结果
    random.seed(42)
    
    # 原始数据文件
    original_file = "不知道为什么不能用的数据集/C_聚类_数据集/3_Solomon_C101.vrp"
    
    # 输出目录
    output_dir = "C_聚类_数据集"
    
    # 读取原始数据
    print(f"开始读取文件: {original_file}")
    original_data = read_vrp_file(original_file)
    
    # 缩放因子
    scale_factor = 0.25
    
    # 对原始数据进行聚类分析（这里只是为了可视化原始数据集的聚类）
    # 使用一个合理的聚类数进行可视化
    cluster_mapping = perform_clustering(original_data['nodes'], 80)  # 使用最大客户点数来可视化
    
    # 可视化原始数据集聚类(缩放后)
    visualize_clusters_with_centers(original_data['nodes'], 
                                   cluster_mapping,
                                   "原始数据集")
    
    # 可视化聚类结果
    visualize_clusters_with_centers(original_data['nodes'], 
                                   cluster_mapping, 
                                   "原始数据集聚类")
    
    # 要生成的数据集规模
    sizes = [20, 30, 50, 60, 80]
    
    # 每个规模生成3个实例
    for size in sizes:
        for instance in range(1, 4):
            # 创建新数据集，不再传入固定的聚类数
            output_file = create_clustered_sampling(original_data, size, instance, output_dir, scale_factor)
            
            # 读取新生成的数据集
            new_data = read_vrp_file(output_file)
            
            # 为可视化重新计算聚类
            new_cluster_mapping = perform_clustering(original_data['nodes'], size)
            
            # 可视化新生成的数据集分布
            selected_nodes = [k for k in new_data['nodes'].keys() if k > 0]
            visualize_clusters_with_centers(original_data['nodes'], 
                                           new_cluster_mapping,
                                           f"C101-{size+1}-{instance}")
            
            # 可视化聚类结果
            visualize_clusters_with_centers(original_data['nodes'], 
                                           new_cluster_mapping, 
                                           f"C101-{size+1}-{instance}聚类")
            
            # 评估聚类质量
            quality = evaluate_clustering(original_data['nodes'], new_cluster_mapping)
            print(f"聚类质量评估 - C101-{size+1}-{instance}:")
            print(f"  平均聚类内距离: {quality['avg_intra_distance']:.2f}")
            print(f"  平均聚类间距离: {quality['avg_inter_distance']:.2f}")
            print(f"  聚类质量指标: {quality['clustering_quality']:.2f} (值越大越好)")

if __name__ == "__main__":
    main()
