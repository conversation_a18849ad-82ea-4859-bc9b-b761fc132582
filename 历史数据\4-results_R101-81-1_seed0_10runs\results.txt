========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 991.50
最小总成本: 931.30 (运行 2)
最大总成本: 1095.31 (运行 6)
总成本标准差: 55.94

========== 算法精度与稳定性分析 ==========
最大偏差: 164.01 (17.61%)
平均偏差: 60.20 (6.46%)
平均求解时间: 306.17秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.001013 |  987.24 |    0.00 | 302.21
     2 |        2 | 0.001074 |  931.30 |    0.00 | 309.64
     3 |        3 | 0.001068 |  936.43 |    0.00 | 303.49
     4 |        4 | 0.000932 | 1073.15 |    0.00 | 302.17
     5 |        5 | 0.001015 |  985.14 |    0.00 | 313.17
     6 |        6 | 0.000913 | 1095.31 |    0.00 | 302.41
     7 |        7 | 0.001031 |  970.33 |    0.00 | 302.54
     8 |        8 | 0.000959 | 1042.90 |    0.00 | 302.46
     9 |        9 | 0.001053 |  949.47 |    0.00 | 306.73
    10 |       10 | 0.001060 |  943.77 |    0.00 | 316.86

最佳解详细信息:
运行ID: 2
适应度: 0.001074
总成本: 931.30
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 43, 28, 9, 60, 29, 54, 55, 17, 25, 27, 75, 59, 26, 53, 74, 58, 1, 22, 0, 51, 5, 79, 50, 73, 77, 76, 31, 78, 80, 35, 37, 12, 36, 72, 32, 13, 48, 2, 11, 6, 14, 52, 0, 15, 71, 38, 8, 39, 30, 40, 42, 70, 7, 41, 16, 44, 0, 45, 49, 33, 10, 21, 18, 62, 61, 63, 19, 64, 34, 20, 47, 56, 4, 46, 68, 57, 24, 67, 69, 66, 3, 65, 23, 0)
染色体 2: (0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 43, 28, 60, 55, 17, 25, 59, 26, 58, 22, 0]
  无人机任务:
    从节点 28 发射无人机访问: [9]
    从节点 60 发射无人机访问: [29, 54]
    从节点 25 发射无人机访问: [27, 75]
    从节点 26 发射无人机访问: [53, 74]
    从节点 58 发射无人机访问: [1]
路线 2: [0, 51, 5, 79, 50, 76, 31, 35, 36, 13, 48, 2, 11, 6, 0]
  无人机任务:
    从节点 50 发射无人机访问: [73, 77]
    从节点 31 发射无人机访问: [78, 80]
    从节点 35 发射无人机访问: [37, 12]
    从节点 36 发射无人机访问: [72, 32]
    从节点 6 发射无人机访问: [14, 52]
路线 3: [0, 15, 71, 38, 8, 39, 30, 70, 7, 44, 0]
  无人机任务:
    从节点 30 发射无人机访问: [40, 42]
    从节点 7 发射无人机访问: [41, 16]
路线 4: [0, 45, 49, 33, 18, 62, 63, 19, 20, 4, 46, 68, 57, 24, 67, 3, 65, 23, 0]
  无人机任务:
    从节点 33 发射无人机访问: [10, 21]
    从节点 62 发射无人机访问: [61]
    从节点 19 发射无人机访问: [64, 34]
    从节点 20 发射无人机访问: [47, 56]
    从节点 67 发射无人机访问: [69, 66]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000572 | 1749.15 |    0.00
  10 | 0.000574 | 1742.02 |    0.00
  20 | 0.000574 | 1742.02 |    0.00
  30 | 0.000599 | 1670.54 |    0.00
  40 | 0.000602 | 1661.15 |    0.00
  50 | 0.000602 | 1661.15 |    0.00
  60 | 0.000609 | 1641.89 |    0.00
  70 | 0.000625 | 1601.14 |    0.00
  80 | 0.000639 | 1564.71 |    0.00
  90 | 0.000700 | 1429.02 |    0.00
 100 | 0.000700 | 1429.02 |    0.00
 110 | 0.000700 | 1429.02 |    0.00
 120 | 0.000716 | 1397.40 |    0.00
 130 | 0.000725 | 1380.01 |    0.00
 140 | 0.000730 | 1370.36 |    0.00
 150 | 0.000740 | 1352.04 |    0.00
 160 | 0.000740 | 1352.04 |    0.00
 170 | 0.000753 | 1328.48 |    0.00
 180 | 0.000762 | 1312.27 |    0.00
 190 | 0.000763 | 1309.93 |    0.00
 200 | 0.000811 | 1232.40 |    0.00
 210 | 0.000819 | 1220.93 |    0.00
 220 | 0.000819 | 1220.93 |    0.00
 230 | 0.000819 | 1220.93 |    0.00
 240 | 0.000819 | 1220.93 |    0.00
 250 | 0.000844 | 1185.04 |    0.00
 260 | 0.000846 | 1182.11 |    0.00
 270 | 0.000846 | 1181.63 |    0.00
 280 | 0.000908 | 1101.71 |    0.00
 290 | 0.000911 | 1097.99 |    0.00
 300 | 0.000980 | 1020.11 |    0.00
 310 | 0.000980 | 1020.11 |    0.00
 320 | 0.000995 | 1005.11 |    0.00
 330 | 0.001005 |  994.53 |    0.00
 340 | 0.001008 |  992.50 |    0.00
 350 | 0.001012 |  988.44 |    0.00
 360 | 0.001012 |  988.44 |    0.00
 370 | 0.001012 |  988.44 |    0.00
 380 | 0.001022 |  978.65 |    0.00
 390 | 0.001022 |  978.65 |    0.00
 400 | 0.001045 |  956.56 |    0.00
 410 | 0.001051 |  951.06 |    0.00
