import random
import copy
from typing import Tuple, Optional, List
from data_loader import Individual, Problem
from repair import (
    check_and_repair_drone_launch_point,
    check_and_repair_drone_task_limit,
    check_and_repair_service_type,
    check_and_repair_drone_payload,
    check_and_repair_path_separation
)
from utils import evaluate_individual_with_penalty

def selection(population: List[Individual], current_gen: int, max_gen: int, elite_percentage: float = 0.05) -> Individual:
    """轮盘赌选择一个父代：
    步骤1：将种群中的个体的适应度值按从大到小的顺序排序；
    步骤2：对种群中的个体进行适应度缩放：
    a.首先取出种群中的最大适应度值和最小适应度值；
    b.设定阿尔法用于缩放程度动态调整，α=(1-t)/T，t为当前代数，T为设定的最大迭代次数；
    c.依次计算种群中所有个体的缩放适应度值：((当前个体的适应度值-种群中最小适应度值)/(种群中最大适应度值-种群中最小适应度值))**2
    d.计算种群中所有个体的缩放适应度值的总和
    步骤3：计算种群中的个体的选择概率P。个体选择概率=缩放适应度值/种群中所有个体的缩放适应度值的总和
    步骤4：计算累积概率Q。比如步骤4：计算选择概率 𝑝𝐴=10/100=0.1,𝑝𝐵=30/100=0.3,𝑝𝐶=20/100=0.2,𝑝𝐷=40/100=0.4 那么步骤5：计算累积概率qA=0.1,qB=0.4,qC=0.6,qD=1.0。并得到对应区间：对应区间： A: [0.0,0.1) B: [0.1,0.4) C: [0.4,0.6) D:[0.6,1.0) 
    步骤5：生成随机数判断落在哪个区间，进而选择个体。选择得到的个体用于接下来的交叉操作。比如：假设需要选择2个个体（如用于交叉生成下一代），随机数序列为：0.55、0.85。 1.  第一次选择（r=0.55） ◦  落在区间 [0.4,0.6) → 选择个体 C。    2.  第二次选择（r=0.85） ◦  落在区间 [0.6,1.0) → 选择个体 D。  最终选择结果：C、D。返回C、D
    """
    # 步骤1：按适应度从大到小排序
    sorted_population = sorted(population, key=lambda x: x.fitness, reverse=True)
    
    # 步骤2：对整个种群进行适应度缩放
    # a. 计算最大和最小适应度值
    max_fitness = sorted_population[0].fitness
    min_fitness = sorted_population[-1].fitness
    
    # 处理所有适应度值相同的特殊情况
    if max_fitness == min_fitness:
        return random.choice(sorted_population)
    
    # b. 计算alpha值
    alpha = (1.0 - current_gen / max_gen)
    
    # c. 计算缩放适应度值
    scaled_fitness = []
    for individual in sorted_population:
        # 适应度缩放公式
        rel_fitness = (individual.fitness - min_fitness) / (max_fitness - min_fitness)
        scaled_val = rel_fitness ** (alpha * 2)  # 使用α调整缩放强度
        scaled_fitness.append(scaled_val)
    
    # d. 计算总缩放适应度
    total_scaled_fitness = sum(scaled_fitness)
    
    # 步骤3：计算选择概率
    selection_probabilities = [sf / total_scaled_fitness for sf in scaled_fitness]
    
    # 步骤4：计算累积概率
    cumulative_probabilities = []
    cum_prob = 0
    for prob in selection_probabilities:
        cum_prob += prob
        cumulative_probabilities.append(cum_prob)
    
    # 步骤5：生成随机数并选择个体
    r = random.random()
    for i, cum_prob in enumerate(cumulative_probabilities):
        if r < cum_prob:
            return sorted_population[i]
    
    # 如果随机数恰好等于1或由于浮点误差未能匹配到任何区间
    return sorted_population[-1]


# def select_parents(population: List[Individual], current_gen: int, max_gen: int) -> Tuple[Individual, Individual]:
#     """轮盘赌选择两个不同的父代个体用于交叉操作
    
#     使用与selection函数相同的轮盘赌选择机制，但确保选择两个不同的个体

#     参数:
#         population: 当前种群
#         current_gen: 当前代数
#         max_gen: 最大代数

#     返回:
#         parent1, parent2: 两个不同的父代个体
#     """
#     # 步骤1：按适应度从大到小排序
#     sorted_population = sorted(population, key=lambda x: x.fitness, reverse=True)
    
#     # 步骤2：对整个种群进行适应度缩放
#     # a. 计算最大和最小适应度值
#     max_fitness = sorted_population[0].fitness
#     min_fitness = sorted_population[-1].fitness
    
#     # 处理所有适应度值相同的特殊情况
#     if max_fitness == min_fitness:
#         # 随机选择两个不同的个体
#         if len(sorted_population) < 2:
#             # 如果种群大小小于2，只能返回相同的个体
#             parent1 = sorted_population[0]
#             return parent1, parent1
#         else:
#             indices = random.sample(range(len(sorted_population)), 2)
#             return sorted_population[indices[0]], sorted_population[indices[1]]
    
#     # b. 计算alpha值
#     alpha = (1.0 - current_gen / max_gen)
    
#     # c. 计算缩放适应度值
#     scaled_fitness = []
#     for individual in sorted_population:
#         # 适应度缩放公式
#         rel_fitness = (individual.fitness - min_fitness) / (max_fitness - min_fitness)
#         scaled_val = rel_fitness ** (alpha * 2)  # 使用α调整缩放强度
#         scaled_fitness.append(scaled_val)
    
#     # d. 计算总缩放适应度
#     total_scaled_fitness = sum(scaled_fitness)
    
#     # 步骤3：计算选择概率
#     selection_probabilities = [sf / total_scaled_fitness for sf in scaled_fitness]
    
#     # 步骤4：计算累积概率
#     cumulative_probabilities = []
#     cum_prob = 0
#     for prob in selection_probabilities:
#         cum_prob += prob
#         cumulative_probabilities.append(cum_prob)
    
#     # 步骤5：生成随机数并选择第一个父代
#     r1 = random.random()
#     parent1_idx = 0
#     for i, cum_prob in enumerate(cumulative_probabilities):
#         if r1 < cum_prob:
#             parent1_idx = i
#             break
#     parent1 = sorted_population[parent1_idx]
    
#     # 步骤6：选择第二个父代，确保与第一个不同
#     max_attempts = min(10, len(sorted_population))  # 最多尝试10次
#     for _ in range(max_attempts):
#         r2 = random.random()
#         parent2_idx = None
#         for i, cum_prob in enumerate(cumulative_probabilities):
#             if r2 < cum_prob:
#                 parent2_idx = i
#                 break
        
#         # 如果找到了不同的个体，立即返回
#         if parent2_idx is not None and parent2_idx != parent1_idx:
#             return parent1, sorted_population[parent2_idx]
    
#     # 如果多次尝试后仍未找到，选择适应度第二好的或其他不同个体
#     available_indices = [i for i in range(len(sorted_population)) if i != parent1_idx]
#     if available_indices:
#         return parent1, sorted_population[available_indices[0]]
#     else:
#         return parent1, parent1


# def shen_mutation(individual: Individual, problem: Problem, population: List[Individual], 
#              mutation_factor: float = 0.8, current_gen: int = 0, max_gen: int = 100) -> None:
#     """
#     基于差分向量机制的变异算子
    
#     实现了排列编码的差分进化算法：
#     1. 减法操作：计算两个排列之间的差异
#     2. 乘法操作：通过最长递增子序列筛选差异
#     3. 加法操作：将差异应用于基础个体生成变异个体
#     """
#     # 随机选择三个不同个体
#     r1, r2, r3 = random.sample([ind for ind in population if ind != individual], 3)
    
#     # 创建试验个体
#     trial_individual = copy.deepcopy(individual)
#     path_layer, service_layer = list(trial_individual.chromosomes[0]), list(trial_individual.chromosomes[1])
    
#     # 提取不包含配送中心的客户点序列
#     x_r1 = [node for node in r1.chromosomes[0] if node != 0]
#     x_r2 = [node for node in r2.chromosomes[0] if node != 0]
#     x_r3 = [node for node in r3.chromosomes[0] if node != 0]
    
#     # 确保客户点集合相同
#     if set(x_r1) == set(x_r2) and set(x_r1) == set(x_r3):
#         # 计算r2的逆排列(r2^-1)
#         r2_inv = {}
#         for pos, value in enumerate(x_r2):
#             r2_inv[value] = pos
        
#         # 计算差异 x_diff = r2^-1 ∘ r3
#         x_diff = [r2_inv[value] for value in x_r3]
        
#         # 找出最长递增子序列(LIS)
#         lis_indices = longest_increasing_subsequence(x_diff)
        
#         # 计算差异算子集合A(LIS的补集)
#         diff_positions = [i for i in range(len(x_diff)) if i not in lis_indices]
        
#         # 根据变异因子F筛选差异算子得到A'
#         selected_positions = []
#         for pos in diff_positions:
#             if random.random() < mutation_factor:
#                 selected_positions.append(pos)
        
#         # 确保至少选择一个差异算子(如果有)
#         if not selected_positions and diff_positions:
#             selected_positions = [random.choice(diff_positions)]
        
#         # 应用差异到基础个体生成变异个体
#         if selected_positions:
#             # 提取需要移动的元素
#             elements_to_move = [x_r3[pos] for pos in selected_positions]
            
#             # 从路径中提取客户点序列
#             customer_path = [node for node in path_layer if node != 0]
            
#             # 移除选中元素
#             modified_path = [e for e in customer_path if e not in elements_to_move]
            
#             # 按顺序插入到新位置
#             for pos, elem in sorted(zip(selected_positions, elements_to_move)):
#                 insert_pos = min(pos, len(modified_path))
#                 modified_path.insert(insert_pos, elem)
            
#             # 重建完整路径(包含配送中心)
#             depot_positions = [i for i, node in enumerate(path_layer) if node == 0]
#             new_path = []
#             customer_idx = 0
            
#             for i in range(len(path_layer)):
#                 if i in depot_positions:
#                     new_path.append(0)  # 添加配送中心
#                 else:
#                     if customer_idx < len(modified_path):
#                         new_path.append(modified_path[customer_idx])
#                         customer_idx += 1
            
#             # 更新服务方式层
#             new_service = [0] * len(new_path)
#             for i, node in enumerate(new_path):
#                 if node == 0:  # 配送中心
#                     continue
#                 # 查找节点在r3中的服务方式
#                 for j, r3_node in enumerate(r3.chromosomes[0]):
#                     if r3_node == node:
#                         new_service[i] = r3.chromosomes[1][j]
#                         break
            
#             # 更新试验个体
#             trial_individual.chromosomes = (new_path, new_service)
    
#     # 应用修复操作
#     check_and_repair_service_type(problem, trial_individual)
#     check_and_repair_drone_launch_point(problem, trial_individual)
#     check_and_repair_drone_task_limit(problem, trial_individual)
#     check_and_repair_drone_payload(problem, trial_individual)
#     check_and_repair_path_separation(problem, trial_individual)

#     # 评估并更新
#     fitness = evaluate_individual_with_penalty(problem, trial_individual, current_gen, max_gen)
#     if fitness > individual.fitness:
#         individual.chromosomes = trial_individual.chromosomes
#         individual.fitness = fitness

# def longest_increasing_subsequence(arr):
#     """
#     计算数组的最长递增子序列的索引
#     返回LIS中元素在原数组中的索引
#     """
#     if not arr:
#         return []
    
#     n = len(arr)
#     dp = [1] * n  # dp[i]表示以arr[i]结尾的LIS长度
#     prev = [-1] * n  # 用于回溯构建LIS
    
#     for i in range(1, n):
#         for j in range(i):
#             if arr[i] > arr[j] and dp[i] < dp[j] + 1:
#                 dp[i] = dp[j] + 1
#                 prev[i] = j
    
#     # 找出最长LIS的终点
#     max_len = max(dp)
#     max_pos = dp.index(max_len)
    
#     # 回溯构建LIS索引
#     result = []
#     while max_pos != -1:
#         result.append(max_pos)
#         max_pos = prev[max_pos]
    
#     return sorted(result)  # 按原序列顺序排列的索引


# def local_search(individual: Individual, problem: Problem, current_gen: int = 0, max_gen: int = 100, max_segment_length: Optional[int] = None) -> None:
#     """
#     对个体进行自适应limit-2-opt局部搜索，循环尝试多次扰动并选择最优结果。
#     调用了修复算子，并计算了适应度值。
#     因为是多次扰动取最优扰动试验个体，所以需要计算适应度。
    
#     参数:
#     - individual: Individual实例，包含chromosomes属性(path_layer, service_layer)
#     - problem: 问题实例，用于评估和修复
#     - current_gen: 当前代数，用于自适应计算
#     - max_gen: 最大代数，用于自适应计算
#     - max_segment_length: 最大反转片段长度，默认为None(不限制)，实际运行后需要自行调整
    
#     返回:
#     - 无返回值，直接修改individual的chromosomes属性
#     """
#     # 保存原始个体信息
#     original_chromosomes = copy.deepcopy(individual.chromosomes)
#     original_fitness = individual.fitness if hasattr(individual, 'fitness') and individual.fitness != -float('inf') else 0
#     # # 打印变异的父代染色体信息
#     # print("\n变异操作的父代个体信息:")
#     # print(f"  路径层: {original_chromosomes[0]}")
#     # print(f"  服务方式层: {original_chromosomes[1]}")
#     # 计算自适应尝试次数
#     base_limit = 5
#     progress_factor = 1 + 0.5 * (current_gen / max_gen)  # 随着进化的进行，增加尝试次数
#     chrom_length_factor = min(2.0, len(individual.chromosomes[0]) / 20)  # 染色体越长，尝试次数越多
#     limit = max(3, min(20, int(base_limit * progress_factor * chrom_length_factor)))
    
#     # 记录最佳变异结果
#     best_chromosomes = original_chromosomes
#     best_fitness = original_fitness
    
#     # 循环尝试limit次变异
#     for _ in range(limit):
#         # 创建临时个体进行变异
#         trial_individual = Individual(chromosomes=copy.deepcopy(original_chromosomes))
        
#         # 对路径层进行2-opt变异
#         path_layer, service_layer = trial_individual.chromosomes
#         path_layer = list(path_layer)
#         service_layer = list(service_layer)
        
#         # 路径层变异
#         if len(path_layer) > 2:
#             max_idx = len(path_layer) - 2
            
#             if max_segment_length is None:
#                 max_segment_length = max_idx
#             else:
#                 max_segment_length = min(max_segment_length, max_idx)
                
#             segment_length = random.randint(2, max_segment_length)
#             start = random.randint(1, max_idx - segment_length + 1)
#             end = start + segment_length - 1
            
#             path_layer[start:end + 1] = path_layer[start:end + 1][::-1]
        
#         # 服务方式层变异
#         if len(service_layer) > 2:
#             max_idx = len(service_layer) - 2
            
#             if max_segment_length is None:
#                 max_segment_length = max_idx
#             else:
#                 max_segment_length = min(max_segment_length, max_idx)
                
#             segment_length = random.randint(2, max_segment_length)
#             start = random.randint(1, max_idx - segment_length + 1)
#             end = start + segment_length - 1
            
#             service_layer[start:end + 1] = service_layer[start:end + 1][::-1]
        
#         # 确保配送中心的服务方式为0，强制修复配送中心在服务方式层中为0
#         for i, node in enumerate(path_layer):
#             if node == 0:  # 配送中心
#                 service_layer[i] = 0
        
#         # 更新临时个体的染色体
#         trial_individual.chromosomes = (path_layer, service_layer)
        
#         # 修复临时个体
#         check_and_repair_service_type(problem, trial_individual)
#         check_and_repair_drone_launch_point(problem, trial_individual)
#         check_and_repair_drone_task_limit(problem, trial_individual)
#         check_and_repair_drone_payload(problem, trial_individual)
#         check_and_repair_path_separation(problem, trial_individual)

#         # check_and_repair_service_type(problem, trial_individual)  # 修复服务类型约束
#         # print(f"配送中心服务方式不为0修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")
#         # check_and_repair_drone_launch_point(problem, trial_individual)  # 修复无人机发射点约束
#         # print(f"无前置发射点修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")
#         # check_and_repair_drone_task_limit(problem, trial_individual)  # 修复无人机任务数量约束
#         # print(f"发射点无人机任务约束修复结果，trial_individual: {trial_individual.chromosomes}")
#         # check_and_repair_drone_payload(problem, trial_individual)  # 修复无人机载荷约束
#         # print(f"无人机客户点需求大于无人机最大载重修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")
#         # check_and_repair_path_separation(problem, trial_individual)  # 修复路径分隔约束
#         # print(f"路径分隔不合理修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")

#         # 评估修复后的个体
#         fitness = evaluate_individual_with_penalty(problem, trial_individual, current_gen, max_gen)
        
#         # 更新最佳结果
#         if fitness > best_fitness:
#             best_chromosomes = trial_individual.chromosomes
#             best_fitness = fitness
    
#     # 使用最佳结果更新个体
#     individual.chromosomes = best_chromosomes
#     individual.fitness = best_fitness
#     # # 打印变异后的染色体信息
#     # print("\n变异后的个体信息:")
#     # print(f"  路径层: {individual.chromosomes[0]}")
#     # print(f"  服务方式层: {individual.chromosomes[1]}")


# def crossover(parent1: Individual, parent2: Individual, problem: Problem,
#               crossover_rate: float = 0.8, current_gen: int = 0, max_gen: int = 100) -> Tuple[Individual, Individual]:
#     """
#     结构保持的路径层交叉操作，保留父代的路径分段结构和服务方式对应关系。
#     使用自适应交叉片段长度：初期较长，后期较短。  -crossover_2
#     没有1的效果好
    
#     参数:
#     - parent1: 第一个父代个体
#     - parent2: 第二个父代个体
#     - problem: 问题实例
#     - crossover_rate: 交叉率基准值
#     - current_gen: 当前代数
#     - max_gen: 最大代数
    
#     返回:
#     - 交叉生成的两个子代个体
#     """
#     # 获取父代染色体
#     path_layer1, service_layer1 = parent1.chromosomes
#     path_layer2, service_layer2 = parent2.chromosomes
    
#     # Step 1: 记录父代路径层中配送中心的索引位置
#     parent1_center_indices = [i for i, node in enumerate(path_layer1) if node == 0]
#     parent2_center_indices = [i for i, node in enumerate(path_layer2) if node == 0]
    
#     # Step 2: 提取不包含配送中心的客户点序列
#     parent1_cross = [node for node in path_layer1 if node != 0]
#     parent2_cross = [node for node in path_layer2 if node != 0]
    
#     # 确保两个父代包含相同的客户点集合
#     if set(parent1_cross) != set(parent2_cross):
#         raise ValueError("父代包含不同的客户点集合，无法进行交叉")
    
#     # Step 3: 选择交叉片段的位置和长度（使用自适应机制）
#     cross_len = len(parent1_cross)
#     if cross_len < 4:  # 客户点太少，不进行交叉
#         return parent1, parent2
    
#     # 计算自适应因子 - 从1递减到0.2
#     adaptive_factor = 1.0 - (0.8 * current_gen / max_gen)
    
#     # 计算自适应交叉片段长度比例 - 初期较大，后期较小
#     # 初期约为crossover_rate*cross_len，后期约为crossover_rate*0.5*cross_len
#     segment_ratio = crossover_rate * adaptive_factor
    
#     # 计算期望的片段长度（至少为2）
#     expected_segment_length = max(2, int(cross_len * segment_ratio))
    
#     # 确保交叉片段长度在合理范围内
#     expected_segment_length = min(expected_segment_length, cross_len - 2)
    
#     # 随机选择起始位置，确保剩余长度足够
#     max_start_pos = cross_len - expected_segment_length
#     cut1 = random.randint(0, max_start_pos)
#     cut2 = cut1 + expected_segment_length
    
#     # Step 4: 交换父代的交叉片段，生成初步子代
#     # 提取交叉片段
#     segment1 = parent1_cross[cut1:cut2]  # 父代1的交叉片段
#     segment2 = parent2_cross[cut1:cut2]  # 父代2的交叉片段
    
#     # 示例:
#     # segment1 = [8,4,3,9,6]  # 父代1交叉片段
#     # segment2 = [1,8,6,4,2]  # 父代2交叉片段
    
#     # 创建子代基因段（先只包含交叉片段的位置）
#     child1_cross = [None] * cross_len  # 子代1的客户点序列，初始为None
#     child2_cross = [None] * cross_len  # 子代2的客户点序列，初始为None
    
#     # 放入交叉片段
#     for i in range(cut1, cut2):
#         child1_cross[i] = segment2[i-cut1]  # 子代1接收父代2的交叉片段
#         child2_cross[i] = segment1[i-cut1]  # 子代2接收父代1的交叉片段
    
#     # 示例:
#     # child1_cross = [None,None,None,None,None,1,8,6,4,2,None,None,None,None]
#     # child2_cross = [None,None,None,None,None,8,4,3,9,6,None,None,None,None]
    
#     # Step 5: 提取子代所需的剩余基因
#     # 找出子代1需要的剩余基因（来自parent1_cross但不在segment2中的基因）
#     child1_needs = [gene for gene in parent1_cross if gene not in segment2]
#     # 找出子代2需要的剩余基因（来自parent2_cross但不在segment1中的基因）
#     child2_needs = [gene for gene in parent2_cross if gene not in segment1]

#     # child1_needs = [5,10,7,3,9,11,12,13,14]  # 子代1需要的剩余基因
#     # child2_needs = [10,7,5,1,2,14,13,12,11]  # 子代2需要的剩余基因
    
#     # 填充子代中的空位
#     idx1 = idx2 = 0
#     for i in range(cross_len):
#         if child1_cross[i] is None:  # 如果子代1的位置i是空的
#             child1_cross[i] = child1_needs[idx1]  # 填入需要的下一个基因
#             idx1 += 1
#         if child2_cross[i] is None:  # 如果子代2的位置i是空的
#             child2_cross[i] = child2_needs[idx2]  # 填入需要的下一个基因
#             idx2 += 1
    
#     # 示例填充后:
#     # child1_cross = [5,10,7,3,9,1,8,6,4,2,11,12,13,14]
#     # child2_cross = [10,7,5,1,2,8,4,3,9,6,14,13,12,11]
    
#     # Step 6: 插入配送中心，构建完整路径层
#     child1_path = []  # 子代1的完整路径层
#     child2_path = []  # 子代2的完整路径层
    
#     # 根据父代1的配送中心索引插入0
#     last_idx = 0
#     for center_idx in parent1_center_indices:
#         if center_idx == 0:  # 起始配送中心
#             child1_path.append(0)  # 添加起始配送中心
#         else:
#             # 计算要插入多少客户点
#             num_customers = sum(1 for idx in range(last_idx, center_idx) if path_layer1[idx] != 0)
#             # 从child1_cross取出相应数量的客户点
#             child1_path.extend(child1_cross[:num_customers])  # 添加客户点
#             child1_cross = child1_cross[num_customers:]  # 移除已添加的客户点
#             # 添加配送中心
#             child1_path.append(0)  # 添加路径终点配送中心(同时也是下一路径的起点)
#         last_idx = center_idx + 1  # 更新last_idx为当前处理的配送中心索引+1

    
#     # 根据父代2的配送中心索引插入0 - 类似逻辑处理子代2
#     last_idx = 0
#     for center_idx in parent2_center_indices:
#         if center_idx == 0:  # 起始配送中心
#             child2_path.append(0)
#         else:
#             # 计算要插入多少客户点
#             num_customers = sum(1 for idx in range(last_idx, center_idx) if path_layer2[idx] != 0)
#             # 从child2_cross取出相应数量的客户点
#             child2_path.extend(child2_cross[:num_customers])
#             child2_cross = child2_cross[num_customers:]
#             # 添加配送中心
#             child2_path.append(0)
#         last_idx = center_idx + 1
    
#     # 示例处理完成后:
#     # child2_path = [0,10,7,2,5,0,1,8,4,3,9,6,0,14,13,12,11,0]
    
#     # Step 7: 构建服务方式层
#     # 为子代1创建服务方式层
#     child1_service = [0] * len(child1_path)  # 初始化为全0
#     child2_service = [0] * len(child2_path)  # 初始化为全0
    
#     # 遍历子代1路径层中的每个客户点
#     for i, node in enumerate(child1_path):
#         if node == 0:  # 配送中心
#             continue  # 配送中心服务方式总是0，无需处理
        
#         # 找出该客户点在交叉片段中的位置
#         if node in segment2:  # 如果在交叉片段内(来自父代2)
#             # 查找在父代2中的服务方式
#             for j, n in enumerate(path_layer2):
#                 if n == node:
#                     child1_service[i] = service_layer2[j]  # 复制父代2的服务方式
#                     break
#         else:  # 如果在交叉片段外(来自父代1)
#             # 查找在父代1中的服务方式
#             for j, n in enumerate(path_layer1):
#                 if n == node:
#                     child1_service[i] = service_layer1[j]  # 复制父代1的服务方式
#                     break
    
#     # 遍历子代2路径层中的每个客户点 - 类似逻辑处理子代2服务方式
#     for i, node in enumerate(child2_path):
#         if node == 0:  # 配送中心
#             continue
        
#         # 找出该客户点在交叉片段中的位置
#         if node in segment1:  # 如果在交叉片段内(来自父代1)
#             # 查找在父代1中的服务方式
#             for j, n in enumerate(path_layer1):
#                 if n == node:
#                     child2_service[i] = service_layer1[j]  # 复制父代1的服务方式
#                     break
#         else:  # 如果在交叉片段外(来自父代2)
#             # 查找在父代2中的服务方式
#             for j, n in enumerate(path_layer2):
#                 if n == node:
#                     child2_service[i] = service_layer2[j]  # 复制父代2的服务方式
#                     break
    
#     # 示例: 子代2服务方式层最终结果
#     # child2_service = [0,0,0,0,0,0,0,1,0,1,0,0,0,0,0,1,1,0]
    
#     # 创建子代个体
#     child1 = Individual(chromosomes=(child1_path, child1_service))  # 创建子代1
#     child2 = Individual(chromosomes=(child2_path, child2_service))  # 创建子代2
#     # print(f"未修复，Child 1 chromosomes: {child1.chromosomes}")
#     # print(f"未修复，Child 2 chromosomes: {child2.chromosomes}")
    
#     # 修复和评估子代
#     # 修复子代1
#     check_and_repair_service_type(problem, child1)  # 修复服务类型约束
#     check_and_repair_drone_launch_point(problem, child1)  # 修复无人机发射点约束
#     check_and_repair_drone_task_limit(problem, child1)  # 修复无人机任务数量约束
#     check_and_repair_drone_payload(problem, child1)  # 修复无人机载荷约束
#     check_and_repair_path_separation(problem, child1)  # 修复路径分隔约束


#     # 修复子代2
#     check_and_repair_service_type(problem, child2)  # 修复服务类型约束
#     check_and_repair_drone_launch_point(problem, child2)  # 修复无人机发射点约束
#     check_and_repair_drone_task_limit(problem, child2)  # 修复无人机任务数量约束
#     check_and_repair_drone_payload(problem, child2)  # 修复无人机载荷约束
#     check_and_repair_path_separation(problem, child2)  # 修复路径分隔约束
    
#     return child1, child2  # 返回两个经过修复的子代  



def crossover(parent1: Individual, parent2: Individual, problem: Problem) -> Tuple[Individual, Individual]:
    """
    结构保持的路径层交叉操作，保留父代的路径分段结构和服务方式对应关系。
    仅调用了修复算子。  - crossover_1
    
    参数:
    - parent1: 第一个父代个体
    - parent2: 第二个父代个体
    - problem: 问题实例
    
    返回:
    - 交叉生成的两个子代个体
    """
    # 获取父代染色体
    path_layer1, service_layer1 = parent1.chromosomes  # 提取父代1的路径层和服务方式层
    path_layer2, service_layer2 = parent2.chromosomes  # 提取父代2的路径层和服务方式层
    # print(path_layer1)
    # print(service_layer1)
    # print(path_layer2)
    # print(service_layer2)
    
    # 示例:
    # path_layer1 =    [0,1,5,10,2,7,0,8,4,3,9,6,0,11,12,13,14,0]
    # service_layer1 = [0,0,1,1,0,1,0,0,1,0,1,0,0,0,1,1,0,0]
    # path_layer2 =    [0,10,7,9,3,0,5,1,8,6,4,2,0,14,13,12,11,0]
    # service_layer2 = [0,0,0,1,1,0,0,0,1,1,0,1,0,0,0,1,1,0]
    
    # Step 1: 记录父代路径层中配送中心的索引位置
    parent1_center_indices = [i for i, node in enumerate(path_layer1) if node == 0]  # 记录父代1中所有0的索引
    parent2_center_indices = [i for i, node in enumerate(path_layer2) if node == 0]  # 记录父代2中所有0的索引
    
    # 示例:
    # parent1_center_indices = [0, 6, 12, 17]  # 父代1中配送中心的位置
    # parent2_center_indices = [0, 5, 12, 17]  # 父代2中配送中心的位置
    
    # Step 2: 提取不包含配送中心的客户点序列
    parent1_cross = [node for node in path_layer1 if node != 0]  # 提取父代1中的所有客户点
    parent2_cross = [node for node in path_layer2 if node != 0]  # 提取父代2中的所有客户点
    
    # 示例:
    # parent1_cross = [1,5,10,2,7,8,4,3,9,6,11,12,13,14]  # 父代1的客户点序列
    # parent2_cross = [10,7,9,3,5,1,8,6,4,2,14,13,12,11]  # 父代2的客户点序列
    
    # 确保两个父代包含相同的客户点集合
    if set(parent1_cross) != set(parent2_cross):
        # print(parent1_cross)
        # print(parent2_cross)
        raise ValueError("父代包含不同的客户点集合，无法进行交叉")  # 安全检查，确保客户点集合一致
    
    # Step 3: 选择交叉片段的位置
    cross_len = len(parent1_cross)  # 客户点序列长度：14
    if cross_len < 4:  # 客户点太少，不进行交叉
        return parent1, parent2
    
    # 随机选择方式1：（比方式略好一点）
    # 假设随机选择的交叉点是5和10
    cut1 = random.randint(0, cross_len // 2)  # 第一个交叉点，在前半部分
    cut2 = random.randint(cut1 + 1, min(cut1 + cross_len // 2, cross_len - 1))  # 第二个交叉点
    
    # # 随机选择方式2：两个不同的交叉点，并确保cut1 < cut2
    # cuts = sorted(random.sample(range(cross_len), 2))
    # cut1, cut2 = cuts[0], cuts[1]

    # 示例:
    # cut1 = 5, cut2 = 10
    
    # Step 4: 交换父代的交叉片段，生成初步子代
    # 提取交叉片段
    segment1 = parent1_cross[cut1:cut2]  # 父代1的交叉片段
    segment2 = parent2_cross[cut1:cut2]  # 父代2的交叉片段
    
    # 示例:
    # segment1 = [8,4,3,9,6]  # 父代1交叉片段
    # segment2 = [1,8,6,4,2]  # 父代2交叉片段
    
    # 创建子代基因段（先只包含交叉片段的位置）
    child1_cross = [None] * cross_len  # 子代1的客户点序列，初始为None
    child2_cross = [None] * cross_len  # 子代2的客户点序列，初始为None
    
    # 放入交叉片段
    for i in range(cut1, cut2):
        child1_cross[i] = segment2[i-cut1]  # 子代1接收父代2的交叉片段
        child2_cross[i] = segment1[i-cut1]  # 子代2接收父代1的交叉片段
    
    # 示例:
    # child1_cross = [None,None,None,None,None,1,8,6,4,2,None,None,None,None]
    # child2_cross = [None,None,None,None,None,8,4,3,9,6,None,None,None,None]
    
    # Step 5: 提取子代所需的剩余基因
    # 找出子代1需要的剩余基因（来自parent1_cross但不在segment2中的基因）
    child1_needs = [gene for gene in parent1_cross if gene not in segment2]
    # 找出子代2需要的剩余基因（来自parent2_cross但不在segment1中的基因）
    child2_needs = [gene for gene in parent2_cross if gene not in segment1]

    # child1_needs = [5,10,7,3,9,11,12,13,14]  # 子代1需要的剩余基因
    # child2_needs = [10,7,5,1,2,14,13,12,11]  # 子代2需要的剩余基因
    
    # 填充子代中的空位
    idx1 = idx2 = 0
    for i in range(cross_len):
        if child1_cross[i] is None:  # 如果子代1的位置i是空的
            child1_cross[i] = child1_needs[idx1]  # 填入需要的下一个基因
            idx1 += 1
        if child2_cross[i] is None:  # 如果子代2的位置i是空的
            child2_cross[i] = child2_needs[idx2]  # 填入需要的下一个基因
            idx2 += 1
    
    # 示例填充后:
    # child1_cross = [5,10,7,3,9,1,8,6,4,2,11,12,13,14]
    # child2_cross = [10,7,5,1,2,8,4,3,9,6,14,13,12,11]
    
    # Step 6: 插入配送中心，构建完整路径层
    child1_path = []  # 子代1的完整路径层
    child2_path = []  # 子代2的完整路径层
    
    # 根据父代1的配送中心索引插入0
    last_idx = 0
    for center_idx in parent1_center_indices:
        if center_idx == 0:  # 起始配送中心
            child1_path.append(0)  # 添加起始配送中心
        else:
            # 计算要插入多少客户点
            num_customers = sum(1 for idx in range(last_idx, center_idx) if path_layer1[idx] != 0)
            # 从child1_cross取出相应数量的客户点
            child1_path.extend(child1_cross[:num_customers])  # 添加客户点
            child1_cross = child1_cross[num_customers:]  # 移除已添加的客户点
            # 添加配送中心
            child1_path.append(0)  # 添加路径终点配送中心(同时也是下一路径的起点)
        last_idx = center_idx + 1  # 更新last_idx为当前处理的配送中心索引+1
    
    # 示例第一次循环 (center_idx=0):
    # child1_path = [0]  # 添加起始配送中心
    # last_idx = 1
    
    # 示例第二次循环 (center_idx=6):
    # num_customers = 5 (索引1-5的客户点数)
    # child1_path = [0,5,10,7,3,9,0]  # 添加5个客户点和配送中心
    # child1_cross剩余 = [1,8,6,4,2,11,12,13,14]
    # last_idx = 7
    
    # 示例第三次循环 (center_idx=12):
    # num_customers = 5 (索引7-11的客户点数)
    # child1_path = [0,5,10,7,3,9,0,1,8,6,4,2,0]  # 添加5个客户点和配送中心
    # child1_cross剩余 = [11,12,13,14]
    # last_idx = 13
    
    # 示例第四次循环 (center_idx=17):
    # num_customers = 4 (索引13-16的客户点数)
    # child1_path = [0,5,10,7,3,9,0,1,8,6,4,2,0,11,12,13,14,0]  # 添加4个客户点和配送中心
    # child1_cross剩余 = []
    # last_idx = 18
    
    # 根据父代2的配送中心索引插入0 - 类似逻辑处理子代2
    last_idx = 0
    for center_idx in parent2_center_indices:
        if center_idx == 0:  # 起始配送中心
            child2_path.append(0)
        else:
            # 计算要插入多少客户点
            num_customers = sum(1 for idx in range(last_idx, center_idx) if path_layer2[idx] != 0)
            # 从child2_cross取出相应数量的客户点
            child2_path.extend(child2_cross[:num_customers])
            child2_cross = child2_cross[num_customers:]
            # 添加配送中心
            child2_path.append(0)
        last_idx = center_idx + 1
    
    # 示例处理完成后:
    # child2_path = [0,10,7,2,5,0,1,8,4,3,9,6,0,14,13,12,11,0]
    
    # Step 7: 构建服务方式层
    # 为子代1创建服务方式层
    child1_service = [0] * len(child1_path)  # 初始化为全0
    child2_service = [0] * len(child2_path)  # 初始化为全0
    
    # 遍历子代1路径层中的每个客户点
    for i, node in enumerate(child1_path):
        if node == 0:  # 配送中心
            continue  # 配送中心服务方式总是0，无需处理
        
        # 找出该客户点在交叉片段中的位置
        if node in segment2:  # 如果在交叉片段内(来自父代2)
            # 查找在父代2中的服务方式
            for j, n in enumerate(path_layer2):
                if n == node:
                    child1_service[i] = service_layer2[j]  # 复制父代2的服务方式
                    break
        else:  # 如果在交叉片段外(来自父代1)
            # 查找在父代1中的服务方式
            for j, n in enumerate(path_layer1):
                if n == node:
                    child1_service[i] = service_layer1[j]  # 复制父代1的服务方式
                    break
    
    # 示例: 子代1服务方式层处理
    # 节点5: 来自父代1, 服务方式为1
    # 节点1: 来自父代2交叉片段, 服务方式为0
    # ...依此类推
    # 最终child1_service = [0,0,1,0,1,0,0,0,1,0,1,0,0,0,1,1,0,0]
    
    # 遍历子代2路径层中的每个客户点 - 类似逻辑处理子代2服务方式
    for i, node in enumerate(child2_path):
        if node == 0:  # 配送中心
            continue
        
        # 找出该客户点在交叉片段中的位置
        if node in segment1:  # 如果在交叉片段内(来自父代1)
            # 查找在父代1中的服务方式
            for j, n in enumerate(path_layer1):
                if n == node:
                    child2_service[i] = service_layer1[j]  # 复制父代1的服务方式
                    break
        else:  # 如果在交叉片段外(来自父代2)
            # 查找在父代2中的服务方式
            for j, n in enumerate(path_layer2):
                if n == node:
                    child2_service[i] = service_layer2[j]  # 复制父代2的服务方式
                    break
    
    # 示例: 子代2服务方式层最终结果
    # child2_service = [0,0,0,0,0,0,0,1,0,1,0,0,0,0,0,1,1,0]
    
    # 创建子代个体
    child1 = Individual(chromosomes=(child1_path, child1_service))  # 创建子代1
    child2 = Individual(chromosomes=(child2_path, child2_service))  # 创建子代2
    # print(f"未修复，Child 1 chromosomes: {child1.chromosomes}")
    # print(f"未修复，Child 2 chromosomes: {child2.chromosomes}")
    
    # 修复和评估子代
    # 修复子代1
    check_and_repair_service_type(problem, child1)  # 修复服务类型约束
    check_and_repair_drone_launch_point(problem, child1)  # 修复无人机发射点约束
    check_and_repair_drone_task_limit(problem, child1)  # 修复无人机任务数量约束
    check_and_repair_drone_payload(problem, child1)  # 修复无人机载荷约束
    check_and_repair_path_separation(problem, child1)  # 修复路径分隔约束


    # 修复子代2
    check_and_repair_service_type(problem, child2)  # 修复服务类型约束
    check_and_repair_drone_launch_point(problem, child2)  # 修复无人机发射点约束
    check_and_repair_drone_task_limit(problem, child2)  # 修复无人机任务数量约束
    check_and_repair_drone_payload(problem, child2)  # 修复无人机载荷约束
    check_and_repair_path_separation(problem, child2)  # 修复路径分隔约束
    

    
    


    # # 应用一系列修复函数确保子代满足所有约束条件
    # is_modified = check_and_repair_service_type(problem, child1)
    # if not is_modified:
    #     print(f"配送中心服务方式不为0修复结果，Child 1 chromosomes: {child1.chromosomes}")

    # is_modified = check_and_repair_drone_launch_point(problem, child1)
    # if not is_modified:
    #     print(f"无前置发射点修复结果，Child 1 chromosomes: {child1.chromosomes}")

    # is_modified = check_and_repair_drone_task_limit(problem, child1)
    # if not is_modified:
    #     print(f"发射点无人机任务约束修复结果，Child 1 chromosomes: {child1.chromosomes}")

    # is_modified = check_and_repair_drone_payload(problem, child1)
    # if not is_modified:
    #     print(f"无人机客户点需求大于无人机最大载重修复结果，Child 1 chromosomes: {child1.chromosomes}")

    # is_modified = check_and_repair_path_separation(problem, child1)
    # if not is_modified:
    #     print(f"路径分隔不合理修复结果，Child 1 chromosomes: {child1.chromosomes}")

    # # 对子代2应用相同的修复操作
    # is_modified = check_and_repair_service_type(problem, child2)
    # if not is_modified:
    #     print(f"配送中心服务方式不为0修复结果，Child 2 chromosomes: {child2.chromosomes}")

    # is_modified = check_and_repair_drone_launch_point(problem, child2)
    # if not is_modified:
    #     print(f"无前置发射点修复结果，Child 2 chromosomes: {child2.chromosomes}")

    # is_modified = check_and_repair_drone_task_limit(problem, child2)
    # if not is_modified:
    #     print(f"发射点无人机任务约束修复结果，Child 2 chromosomes: {child2.chromosomes}")

    # is_modified = check_and_repair_drone_payload(problem, child2)
    # if not is_modified:
    #     print(f"无人机客户点需求大于无人机最大载重修复结果，Child 2 chromosomes: {child2.chromosomes}")

    # is_modified = check_and_repair_path_separation(problem, child2)
    # if not is_modified:
    #     print(f"路径分隔不合理修复结果，Child 2 chromosomes: {child2.chromosomes}")
    # 打印子代1的详细信息
    # print(f"\n交叉得到的子代1详细信息:")
    # print(f"  路径层: {child1.chromosomes[0]}")  
    # print(f"  服务方式层: {child1.chromosomes[1]}")

    # # 打印子代2的详细信息
    # print(f"\n交叉得到的子代2详细信息:")
    # print(f"  路径层: {child2.chromosomes[0]}")
    # print(f"  服务方式层: {child2.chromosomes[1]}")
    
    return child1, child2  # 返回两个经过修复的子代  



def mutation(individual: Individual, problem: Problem, population: List[Individual], 
             mutation_factor: float = 0.8, current_gen: int = 0, max_gen: int = 100) -> None:
    """
    基于差分向量机制的变异算子（增强版）
    
    实现了卡车与无人机协同配送路径优化问题中的差分进化算法：
    1. 差异量化规则：量化两个个体间的路径顺序差异和服务方式差异
    2. 缩放规则：根据变异因子F控制应用差异的程度
    3. 应用规则：将缩放后的差异应用到第三个个体上，生成变异个体
    
    - 无返回值，直接修改individual的chromosomes属性
    """
    # 随机选择两个不同个体
    r2, r3 = random.sample([ind for ind in population if ind != individual], 2)
    
    # 创建试验个体，即r1，遍历population中的每一个个体
    trial_individual = copy.deepcopy(individual)
    path_layer, service_layer = list(trial_individual.chromosomes[0]), list(trial_individual.chromosomes[1])
    
    # 1. 差异量化规则：计算r2和r3之间的差异
    
    # 1.1 路径层差异量化
    path_exchange_pairs = quantify_path_differences(r2.chromosomes[0], r3.chromosomes[0])
    
    # 1.2 服务方式层差异量化
    service_diff_positions = quantify_service_differences(r2.chromosomes[0], r2.chromosomes[1], 
                                                         r3.chromosomes[0], r3.chromosomes[1])
    
    # 2. 缩放规则：根据变异因子F选择部分差异
    
    # 2.1 路径层缩放
    selected_exchanges = scale_path_differences(path_exchange_pairs, mutation_factor)
    
    # 2.2 服务方式层缩放
    selected_service_positions = scale_service_differences(service_diff_positions, mutation_factor)
    
    # 3. 应用规则：将差异应用到基向量个体上
    
    # 3.1 路径层应用
    new_path = apply_path_exchanges(path_layer, selected_exchanges)
    
    # 3.2 服务方式层应用
    new_service = apply_service_changes(path_layer, service_layer, r2.chromosomes[0], r2.chromosomes[1], 
                                       r3.chromosomes[0], r3.chromosomes[1], selected_service_positions)
    
    # 更新试验个体
    trial_individual.chromosomes = (new_path, new_service)
    
    # 应用修复操作
    check_and_repair_service_type(problem, trial_individual)
    check_and_repair_drone_launch_point(problem, trial_individual)
    check_and_repair_drone_task_limit(problem, trial_individual)
    check_and_repair_drone_payload(problem, trial_individual)
    check_and_repair_path_separation(problem, trial_individual)


    # # 应用修复并有条件打印
    # is_modified = check_and_repair_service_type(problem, trial_individual)
    # if not is_modified:
    #     print(f"配送中心服务方式不为0修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")

    # is_modified = check_and_repair_drone_launch_point(problem, trial_individual)
    # if not is_modified:
    #     print(f"无前置发射点修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")

    # is_modified = check_and_repair_drone_task_limit(problem, trial_individual)
    # if not is_modified:
    #     print(f"发射点无人机任务约束修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")

    # is_modified = check_and_repair_drone_payload(problem, trial_individual)
    # if not is_modified:
    #     print(f"无人机客户点需求大于无人机最大载重修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")

    # is_modified = check_and_repair_path_separation(problem, trial_individual)
    # if not is_modified:
    #     print(f"路径分隔不合理修复结果，trial_individual chromosomes: {trial_individual.chromosomes}")

    # 评估试验个体
    fitness = evaluate_individual_with_penalty(problem, trial_individual, current_gen, max_gen)
    if fitness > individual.fitness:
        individual.chromosomes = trial_individual.chromosomes
        individual.fitness = fitness

    return trial_individual

# 辅助函数1：量化路径差异（客户点访问相对顺序），识别需要交换的客户点对
# 实现路径层的x_r1 (t)-x_r2 (t)，获取路径层的差分向量  
def quantify_path_differences(path1, path2):
    """
    计算两个路径间的差异，识别需要交换的客户点对
    
    参数:
    - path1: 第一个路径层
    - path2: 第二个路径层
    
    返回:
    - 交换对列表，每个交换对为(node_a, node_b)，表示这两个客户点的顺序在两个路径中不同
    """
    # 提取不含配送中心的客户点序列
    nodes1 = [node for node in path1 if node != 0]
    nodes2 = [node for node in path2 if node != 0]
    
    # 确保包含相同的客户点集合
    if set(nodes1) != set(nodes2):
        return []
    
    # 构建位置映射
    pos_map1 = {node: idx for idx, node in enumerate(nodes1)}
    pos_map2 = {node: idx for idx, node in enumerate(nodes2)}
    
    # 识别顺序不同的客户点对
    exchange_pairs = []
    for i in range(len(nodes1)):
        for j in range(i+1, len(nodes1)):
            node_a, node_b = nodes1[i], nodes1[j]
            # 如果在path1中a在b之前，但在path2中b在a之前，则记录交换对
            if (pos_map1[node_a] < pos_map1[node_b] and pos_map2[node_a] > pos_map2[node_b]) or \
               (pos_map1[node_a] > pos_map1[node_b] and pos_map2[node_a] < pos_map2[node_b]):
                exchange_pairs.append((node_a, node_b))
    
    return exchange_pairs

# 辅助函数2：量化服务方式差异，识别服务方式不同的客户点
def quantify_service_differences(path1, service1, path2, service2):
    """
    计算两个服务方式层间的差异，识别服务方式不同的客户点
    
    参数:
    - path1, service1: 第一个个体的路径层和服务方式层
    - path2, service2: 第二个个体的路径层和服务方式层
    
    返回:
    - 差异位置列表，每个元素为客户点编号，表示该客户点的服务方式在两个个体中不同
    """
    # 构建节点对应的服务方式映射
    service_map1 = {}
    service_map2 = {}
    
    for i, node in enumerate(path1):
        if node != 0:  # 忽略配送中心
            service_map1[node] = service1[i]
            
    for i, node in enumerate(path2):
        if node != 0:  # 忽略配送中心
            service_map2[node] = service2[i]
    
    # 找出服务方式不同的节点
    diff_nodes = []
    for node in service_map1:
        if node in service_map2 and service_map1[node] != service_map2[node]:
            diff_nodes.append(node)
    
    return diff_nodes

# 辅助函数3：缩放路径差异，根据变异因子选择部分交换对
# 实现路径层的F·(x_r2 - x_r3)，在离散空间：F表示应用多少比例的差异交换对
def scale_path_differences(exchange_pairs, mutation_factor):
    """
    根据变异因子F缩放路径差异
    
    参数:
    - exchange_pairs: 交换对列表
    - mutation_factor: 变异因子F
    
    返回:
    - 选中的交换对列表
    """
    if not exchange_pairs:
        return []
    
    # 计算要选择的交换对数量
    select_count = max(1, int(len(exchange_pairs) * mutation_factor))
    
    # 随机选择指定数量的交换对
    return random.sample(exchange_pairs, min(select_count, len(exchange_pairs)))

# 辅助函数4：缩放服务方式差异，根据变异因子选择部分差异位置
def scale_service_differences(diff_nodes, mutation_factor):
    """
    根据变异因子F缩放服务方式差异
    
    参数:
    - diff_nodes: 服务方式不同的客户点列表
    - mutation_factor: 变异因子F
    
    返回:
    - 选中的差异位置列表
    """
    if not diff_nodes:
        return []
    
    # 计算要选择的差异位置数量
    select_count = max(1, int(len(diff_nodes) * mutation_factor))
    
    # 随机选择指定数量的差异位置
    return random.sample(diff_nodes, min(select_count, len(diff_nodes)))

# 辅助函数5：应用路径交换
def apply_path_exchanges(original_path, exchanges):
    """
    将选中的交换对应用到路径上
    
    参数:
    - original_path: 原始路径层
    - exchanges: 要应用的交换对列表
    
    返回:
    - 变异后的路径层
    """
    path = original_path.copy()
    
    # 提取不含配送中心的客户点和配送中心位置
    depot_positions = [i for i, node in enumerate(path) if node == 0]
    customer_path = [node for node in path if node != 0]
    
    # 对每个交换对执行交换
    for node_a, node_b in exchanges:
        if node_a in customer_path and node_b in customer_path:
            pos_a = customer_path.index(node_a)
            pos_b = customer_path.index(node_b)
            customer_path[pos_a], customer_path[pos_b] = customer_path[pos_b], customer_path[pos_a]
    
    # 重建完整路径（包含配送中心）
    new_path = []
    customer_idx = 0
    
    for i in range(len(original_path)):
        if i in depot_positions:
            new_path.append(0)  # 添加配送中心
        else:
            if customer_idx < len(customer_path):
                new_path.append(customer_path[customer_idx])
                customer_idx += 1
    
    return new_path

# 辅助函数6：应用服务方式变化
def apply_service_changes(original_path, original_service, path_r2, service_r2, path_r3, service_r3, diff_nodes):
    """
    将选中的服务方式差异应用到服务方式层上
    
    参数:
    - original_path, original_service: 原始路径层和服务方式层
    - path_r2, service_r2: 参考个体r2的路径层和服务方式层
    - path_r3, service_r3: 参考个体r3的路径层和服务方式层
    - diff_nodes: 要应用服务方式变化的客户点列表
    
    返回:
    - 变异后的服务方式层
    """
    service = original_service.copy()
    
    # 构建r3中节点对应的服务方式映射
    service_map_r3 = {}
    for i, node in enumerate(path_r3):
        if node != 0:
            service_map_r3[node] = service_r3[i]
    
    # 应用服务方式变化
    for i, node in enumerate(original_path):
        if node in diff_nodes:
            # 使用r3中对应节点的服务方式
            if node in service_map_r3:
                service[i] = service_map_r3[node]
            # 确保配送中心服务方式为0
            if node == 0:
                service[i] = 0
    
    return service