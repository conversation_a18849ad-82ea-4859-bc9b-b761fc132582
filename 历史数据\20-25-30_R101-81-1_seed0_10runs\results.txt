========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.6
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1052.70
最小总成本: 935.16 (运行 3)
最大总成本: 1211.97 (运行 10)
总成本标准差: 94.52

========== 算法精度与稳定性分析 ==========
最大偏差: 276.81 (29.60%)
平均偏差: 117.54 (12.57%)
平均求解时间: 303.62秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000841 | 1189.15 |    0.00 | 303.38
     2 |        2 | 0.001011 |  989.51 |    0.00 | 303.53
     3 |        3 | 0.001069 |  935.16 |    0.00 | 303.66
     4 |        4 | 0.000999 | 1000.64 |    0.00 | 303.99
     5 |        5 | 0.000996 | 1003.65 |    0.00 | 303.95
     6 |        6 | 0.001003 |  997.07 |    0.00 | 303.20
     7 |        7 | 0.000896 | 1115.46 |    0.00 | 303.38
     8 |        8 | 0.000885 | 1129.35 |    0.00 | 303.55
     9 |        9 | 0.001047 |  955.08 |    0.00 | 303.53
    10 |       10 | 0.000825 | 1211.97 |    0.00 | 304.03

最佳解详细信息:
运行ID: 3
适应度: 0.001069
总成本: 935.16
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 52, 50, 37, 78, 31, 80, 77, 36, 35, 73, 2, 48, 13, 76, 6, 12, 11, 49, 21, 10, 68, 3, 0, 43, 59, 25, 26, 27, 17, 55, 29, 75, 60, 54, 9, 69, 28, 67, 24, 66, 65, 0, 22, 23, 46, 4, 47, 20, 56, 34, 64, 63, 19, 18, 61, 62, 33, 45, 44, 7, 53, 16, 42, 30, 41, 40, 39, 70, 74, 8, 71, 5, 38, 51, 1, 58, 15, 57, 72, 14, 32, 79, 0)
染色体 2: (0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0)

最佳解路线详情:
路线 1: [0, 52, 78, 31, 36, 35, 73, 2, 76, 11, 49, 21, 10, 0]
  无人机任务:
    从节点 52 发射无人机访问: [50, 37]
    从节点 31 发射无人机访问: [80, 77]
    从节点 2 发射无人机访问: [48, 13]
    从节点 76 发射无人机访问: [6, 12]
    从节点 10 发射无人机访问: [68, 3]
路线 2: [0, 43, 59, 25, 17, 55, 60, 9, 69, 28, 67, 65, 0]
  无人机任务:
    从节点 25 发射无人机访问: [26, 27]
    从节点 55 发射无人机访问: [29, 75]
    从节点 60 发射无人机访问: [54]
    从节点 67 发射无人机访问: [24, 66]
路线 3: [0, 22, 23, 46, 4, 47, 20, 64, 63, 61, 62, 33, 45, 44, 7, 16, 41, 70, 71, 5, 1, 58, 72, 79, 0]
  无人机任务:
    从节点 20 发射无人机访问: [56, 34]
    从节点 63 发射无人机访问: [19, 18]
    从节点 7 发射无人机访问: [53]
    从节点 16 发射无人机访问: [42, 30]
    从节点 41 发射无人机访问: [40, 39]
    从节点 70 发射无人机访问: [74, 8]
    从节点 5 发射无人机访问: [38, 51]
    从节点 58 发射无人机访问: [15, 57]
    从节点 72 发射无人机访问: [14, 32]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000572 | 1747.87 |    0.00
  10 | 0.000572 | 1747.87 |    0.00
  20 | 0.000581 | 1720.52 |    0.00
  30 | 0.000590 | 1694.25 |    0.00
  40 | 0.000597 | 1674.31 |    0.00
  50 | 0.000597 | 1674.31 |    0.00
  60 | 0.000600 | 1665.99 |    0.00
  70 | 0.000604 | 1655.08 |    0.00
  80 | 0.000617 | 1621.41 |    0.00
  90 | 0.000620 | 1611.89 |    0.00
 100 | 0.000643 | 1555.71 |    0.00
 110 | 0.000659 | 1516.61 |    0.00
 120 | 0.000687 | 1455.67 |    0.00
 130 | 0.000721 | 1387.31 |    0.00
 140 | 0.000730 | 1370.76 |    0.00
 150 | 0.000741 | 1349.39 |    0.00
 160 | 0.000746 | 1339.78 |    0.00
 170 | 0.000755 | 1324.07 |    0.00
 180 | 0.000767 | 1304.41 |    0.00
 190 | 0.000769 | 1300.30 |    0.00
 200 | 0.000820 | 1220.18 |    0.00
 210 | 0.000820 | 1220.18 |    0.00
 220 | 0.000820 | 1220.18 |    0.00
 230 | 0.000827 | 1209.87 |    0.00
 240 | 0.000827 | 1209.87 |    0.00
 250 | 0.000829 | 1206.98 |    0.00
 260 | 0.000893 | 1120.01 |    0.00
 270 | 0.000895 | 1117.40 |    0.00
 280 | 0.000915 | 1092.85 |    0.00
 290 | 0.000929 | 1076.71 |    0.00
 300 | 0.000957 | 1045.01 |    0.00
 310 | 0.000968 | 1032.94 |    0.00
 320 | 0.000997 | 1003.01 |    0.00
 330 | 0.001039 |  962.42 |    0.00
 340 | 0.001043 |  958.79 |    0.00
 350 | 0.001069 |  935.82 |    0.00
 360 | 0.001069 |  935.16 |    0.00
 370 | 0.001069 |  935.16 |    0.00
 380 | 0.001069 |  935.16 |    0.00
 390 | 0.001069 |  935.16 |    0.00
 400 | 0.001069 |  935.16 |    0.00
 410 | 0.001069 |  935.16 |    0.00
