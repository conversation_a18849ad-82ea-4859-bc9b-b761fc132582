========== 第20代最优解详情 ==========
适应度: 0.002800
总成本: 357.11
惩罚值: 0.00

========== 多次运行结果统计 ==========
运行次数: 1
平均总成本: 326.67
最小总成本: 326.67 (运行 1)
最大总成本: 326.67 (运行 1)
总成本标准差: 0.00

========== 算法精度与稳定性分析 ==========
最大偏差: 0.00 (0.00%)
平均偏差: 0.00 (0.00%)
平均求解时间: 62.52秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 | 0.003061 |   326.67 |        0 | 62.52秒    |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 1) ==========
适应度: 0.003061
总成本: 326.67
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 1, 7, 13, 21, 6, 17, 19, 31, 2, 3, 23, 11, 28, 4, 8, 18, 9, 22, 29, 10, 5, 25, 15, 0, 16, 30, 26, 24, 27, 14, 2
0, 12, 0)                                                                                                                      服务方式层: (0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0)

染色体解码结果:
  车辆 1 路径: [0, 1, 7, 6, 17, 2, 11, 8, 22, 5, 15, 0]
    无人机任务:
      发射点 7 → 任务: [13, 21]
      发射点 17 → 任务: [19, 31]
      发射点 2 → 任务: [3, 23]
      发射点 11 → 任务: [28, 4]
      发射点 8 → 任务: [18, 9]
      发射点 22 → 任务: [29, 10]
      发射点 5 → 任务: [25]
  车辆 2 路径: [0, 16, 24, 27, 14, 0]
    无人机任务:
      发射点 16 → 任务: [30, 26]
      发射点 14 → 任务: [20, 12]

程序执行完毕。
