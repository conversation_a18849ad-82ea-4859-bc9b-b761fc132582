import random
import numpy as np
import copy
import traceback
from initialization import initialize_population, improved_savings_algorithm, random_solution_generator
from data_loader import Problem, Individual

def test_initialize_population():
    """测试使用A-n32-k2-d4.vrp数据初始化种群函数，pop_size设为10"""
    try:
        # 设置随机种子，以确保结果可重现
        random.seed(42)
        np.random.seed(42)
        
        # 加载问题
        problem = Problem("A-n32-k2-d4.vrp")
        
        # 设置种群大小
        pop_size = 1
        
        # 使用原始方法直接生成种群
        population = initialize_population(problem, pop_size)
        
        # 计算各方法生成的个体数量
        improved_count = int(pop_size * 0.4)  # 40%使用改进的节约算法
        random_count = pop_size - improved_count  # 60%使用随机生成
        
        # 打印种群信息
        # 分别打印各种方法生成的个体
        print(f"\n使用改进的节约算法生成的个体 (共{improved_count}个):")
        for i in range(improved_count):
            ind = population[i]
            print(f"个体 {i+1}:")
            print(f"  路径: {ind.routes}")
            print(f"  无人机任务: {ind.drone_tasks}")
            # 尝试获取染色体信息，如果存在的话
            if hasattr(ind, 'chromosomes'):
                print(f"  染色体: {ind.chromosomes}")
        
        print(f"\n使用随机生成算法生成的个体 (共{random_count}个):")
        for i in range(improved_count, pop_size):
            ind = population[i]
            print(f"个体 {i+1}:")
            print(f"  路径: {ind.routes}")
            print(f"  无人机任务: {ind.drone_tasks}")
            # 尝试获取染色体信息，如果存在的话
            if hasattr(ind, 'chromosomes'):
                print(f"  染色体: {ind.chromosomes}")
        
    except Exception as e:
        traceback.print_exc()
        print(f"测试失败，错误信息: {str(e)}")

if __name__ == '__main__':
    test_initialize_population()