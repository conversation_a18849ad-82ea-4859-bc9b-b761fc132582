# 基础惩罚系数和参数
namuda2 = 5.0  # 元/Wh
gamma = 3.0
alpha = 1.5

# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成


# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.4 * elapsed_time / self.max_runtime)


if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.5:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期

========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 40
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1037.76
最小总成本: 961.92 (运行 9)
最大总成本: 1196.42 (运行 10)
总成本标准差: 67.50

========== 算法精度与稳定性分析 ==========
最大偏差: 234.49 (24.38%)
平均偏差: 75.84 (7.88%)
平均求解时间: 301.85秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |       41 | 0.001002 |  998.00 |    0.00 | 301.55
     2 |       42 | 0.000911 | 1097.82 |    0.00 | 301.71
     3 |       43 | 0.000943 | 1060.31 |    0.00 | 301.89
     4 |       44 | 0.001035 |  966.57 |    0.00 | 302.44
     5 |       45 | 0.000939 | 1064.55 |    0.00 | 301.73
     6 |       46 | 0.001009 |  990.91 |    0.00 | 301.71
     7 |       47 | 0.000967 | 1034.21 |    0.00 | 301.87
     8 |       48 | 0.000993 | 1006.93 |    0.00 | 302.03
     9 |       49 | 0.001040 |  961.92 |    0.00 | 301.86
    10 |       50 | 0.000836 | 1196.42 |    0.00 | 301.76

最佳解详细信息:
运行ID: 9
适应度: 0.001040
总成本: 961.92
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 52, 50, 49, 59, 64, 62, 56, 57, 58, 61, 63, 65, 66, 67, 69, 71, 68, 70, 72, 0, 60, 74, 77, 73, 75, 76, 80, 79, 78, 2, 1, 8, 22, 24, 19, 21, 20, 23, 16, 18, 17, 0, 7, 9, 5, 6, 3, 4, 10, 12, 13, 11, 14, 15, 27, 29, 31, 30, 26, 25, 28, 0, 34, 41, 39, 37, 40, 38, 36, 35, 33, 32, 46, 44, 43, 45, 42, 47, 54, 51, 53, 48, 55, 0)
染色体 2: (0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0)

最佳解路线详情:
路线 1: [0, 52, 49, 59, 64, 62, 56, 58, 61, 63, 65, 66, 69, 70, 72, 0]
  无人机任务:
    从节点 52 发射无人机访问: [50]
    从节点 56 发射无人机访问: [57]
    从节点 66 发射无人机访问: [67]
    从节点 69 发射无人机访问: [71, 68]
路线 2: [0, 60, 74, 75, 76, 78, 2, 8, 22, 21, 20, 18, 0]
  无人机任务:
    从节点 74 发射无人机访问: [77, 73]
    从节点 76 发射无人机访问: [80, 79]
    从节点 2 发射无人机访问: [1]
    从节点 22 发射无人机访问: [24, 19]
    从节点 20 发射无人机访问: [23, 16]
    从节点 18 发射无人机访问: [17]
路线 3: [0, 7, 5, 6, 10, 12, 11, 14, 15, 27, 29, 26, 0]
  无人机任务:
    从节点 7 发射无人机访问: [9]
    从节点 6 发射无人机访问: [3, 4]
    从节点 12 发射无人机访问: [13]
    从节点 29 发射无人机访问: [31, 30]
    从节点 26 发射无人机访问: [25, 28]
路线 4: [0, 34, 37, 40, 35, 33, 32, 46, 43, 45, 54, 53, 0]
  无人机任务:
    从节点 34 发射无人机访问: [41, 39]
    从节点 40 发射无人机访问: [38, 36]
    从节点 46 发射无人机访问: [44]
    从节点 45 发射无人机访问: [42, 47]
    从节点 54 发射无人机访问: [51]
    从节点 53 发射无人机访问: [48, 55]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000522 | 1915.52 |    0.00
  10 | 0.000522 | 1915.52 |    0.00
  20 | 0.000522 | 1915.52 |    0.00
  30 | 0.000523 | 1912.27 |    0.00
  40 | 0.000523 | 1912.27 |    0.00
  50 | 0.000523 | 1912.27 |    0.00
  60 | 0.000523 | 1912.27 |    0.00
  70 | 0.000525 | 1906.06 |    0.00
  80 | 0.000528 | 1894.26 |    0.00
  90 | 0.000530 | 1885.27 |    0.00
 100 | 0.000565 | 1737.26 |   32.29
 110 | 0.000565 | 1737.26 |   32.29
 120 | 0.000565 | 1737.26 |   32.29
 130 | 0.000565 | 1737.26 |   32.29
 140 | 0.000573 | 1745.24 |    0.00
 150 | 0.000596 | 1678.50 |    0.00
 160 | 0.000608 | 1644.59 |    0.00
 170 | 0.000621 | 1609.84 |    0.00
 180 | 0.000622 | 1607.76 |    0.00
 190 | 0.000642 | 1558.60 |    0.00
 200 | 0.000650 | 1538.40 |    0.00
 210 | 0.000656 | 1525.47 |    0.00
 220 | 0.000658 | 1520.13 |    0.00
 230 | 0.000740 | 1351.51 |    0.00
 240 | 0.000793 | 1261.65 |    0.00
 250 | 0.000793 | 1261.65 |    0.00
 260 | 0.000818 | 1221.91 |    0.00
 270 | 0.000818 | 1221.91 |    0.00
 280 | 0.000876 | 1141.64 |    0.00
 290 | 0.000879 | 1137.19 |    0.00
 300 | 0.000933 | 1072.19 |    0.00
 310 | 0.000934 | 1070.57 |    0.00
 320 | 0.000942 | 1061.86 |    0.00
 330 | 0.000952 | 1050.53 |    0.00
 340 | 0.000961 | 1040.25 |    0.00
 350 | 0.000964 | 1037.48 |    0.00
 360 | 0.000973 | 1027.88 |    0.00
 370 | 0.000985 | 1015.41 |    0.00
 380 | 0.001005 |  995.22 |    0.00
 390 | 0.001005 |  995.22 |    0.00
 400 | 0.001023 |  977.81 |    0.00
 410 | 0.001023 |  977.81 |    0.00
 420 | 0.001024 |  976.89 |    0.00
 430 | 0.001032 |  968.69 |    0.00
 440 | 0.001040 |  961.92 |    0.00
