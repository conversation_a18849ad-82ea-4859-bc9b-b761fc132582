NAME : C101-31-2
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 10.0 12.5
 1 11.2 17.0
 2 7.5 12.5
 3 7.5 13.0
 4 7.0 13.0
 5 6.2 12.5
 6 8.8 7.5
 7 8.8 8.0
 8 8.2 8.0
 9 8.2 8.8
 10 8.0 7.5
 11 7.5 7.5
 12 7.5 8.0
 13 7.5 8.8
 14 7.0 7.5
 15 7.0 8.8
 16 6.5 8.0
 17 6.2 7.5
 18 6.2 8.8
 19 11.2 16.2
 20 18.8 13.8
 21 18.0 13.8
 22 17.5 14.5
 23 17.0 15.0
 24 16.5 13.8
 25 16.2 13.8
 26 16.2 15.0
 27 15.8 14.5
 28 15.0 13.8
 29 15.0 15.0
 30 14.5 18.8

DEMAND_SECTION
 0 0 0      
 1 0.0 2.7      // 仅取货需求客户 - light包裹
 2 0.0 1.3      // 仅取货需求客户 - light包裹
 3 9.5 0.0      // 仅送货需求客户 - medium包裹
 4 4.3 6.2      // 送货取货双需求客户 - light包裹
 5 1.5 0.0      // 仅送货需求客户 - light包裹
 6 4.6 8.2      // 送货取货双需求客户 - light包裹
 7 9.5 0.0      // 仅送货需求客户 - medium包裹
 8 2.0 0.0      // 仅送货需求客户 - light包裹
 9 4.1 0.0      // 仅送货需求客户 - light包裹
 10 7.1 0.0      // 仅送货需求客户 - medium包裹
 11 1.6 0.0      // 仅送货需求客户 - light包裹
 12 9.3 9.8      // 送货取货双需求客户 - medium包裹
 13 9.4 1.2      // 送货取货双需求客户 - medium包裹
 14 2.3 0.0      // 仅送货需求客户 - light包裹
 15 0.0 9.0      // 仅取货需求客户 - medium包裹
 16 9.1 0.0      // 仅送货需求客户 - medium包裹
 17 4.1 0.0      // 仅送货需求客户 - light包裹
 18 0.0 4.5      // 仅取货需求客户 - light包裹
 19 6.2 0.0      // 仅送货需求客户 - medium包裹
 20 7.4 0.0      // 仅送货需求客户 - medium包裹
 21 4.2 3.0      // 送货取货双需求客户 - light包裹
 22 1.8 0.0      // 仅送货需求客户 - light包裹
 23 4.5 0.0      // 仅送货需求客户 - light包裹
 24 0.0 12.9      // 仅取货需求客户 - heavy包裹
 25 2.6 9.8      // 送货取货双需求客户 - light包裹
 26 4.8 0.0      // 仅送货需求客户 - light包裹
 27 0.0 4.9      // 仅取货需求客户 - light包裹
 28 0.0 4.9      // 仅取货需求客户 - light包裹
 29 1.4 4.9      // 送货取货双需求客户 - light包裹
 30 2.3 0.0      // 仅送货需求客户 - light包裹
 1 10 0      
 2 10 0      
 3 20 0      
 4 20 0      
 5 10 0      
 6 10 0      
 7 10 0      
 8 20 0      
 9 10 0      
 10 10 0      
 11 10 0      
 12 30 0      
 13 10 0      
 14 10 0      
 15 10 0      
 16 10 0      
 17 10 0      
 18 10 0      
 19 20 0      
 20 20 0      
 21 10 0      
 22 20 0      
 23 30 0      
 24 10 0      
 25 20 0      
 26 30 0      
 27 10 0      
 28 10 0      
 29 10 0      
 30 20 0      

DEPOT_SECTION
 0
-1
EOF