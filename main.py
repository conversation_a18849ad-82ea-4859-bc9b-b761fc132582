import random
import numpy as np
import time
import copy
import math
import matplotlib.pyplot as plt
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import matplotlib as mpl
from data_loader import Problem, Individual
from algorithm import solve_vrpd
from utils import calculate_total_cost, calculate_system_operation_time

def plot_best_fitness_comparison(all_results, save_path='fitness_comparison.png'):
    """
    绘制每轮运行的全局最优解适应度值的对比柱状图
    
    参数:
        all_results: 所有运行结果的列表
        save_path: 保存图像的路径
    """
    # 设置默认字体
    font_path = 'C:/Windows/Fonts/simhei.ttf'
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    
    plt.figure(figsize=(12, 8))
    
    # 提取运行ID和适应度值
    run_ids = [r['run_id'] for r in all_results]
    fitness_values = [r['fitness'] for r in all_results]
    
    # 找出最佳适应度值和对应的运行ID
    best_index = fitness_values.index(max(fitness_values))
    best_run_id = run_ids[best_index]
    
    # 生成柱状图的颜色，最佳结果用不同颜色标识
    colors = ['skyblue' for _ in range(len(run_ids))]
    colors[best_index] = 'red'
    
    # 绘制柱状图
    bars = plt.bar(run_ids, fitness_values, color=colors, width=0.6)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.0005,
                 f'{height:.6f}', ha='center', va='bottom', rotation=45, fontsize=8)
    
    # 标记最佳结果
    plt.text(best_run_id, fitness_values[best_index] - 0.002, 
             f'最佳: {fitness_values[best_index]:.6f}', ha='center', va='top', 
             color='red', fontweight='bold')
    
    plt.xlabel('运行ID')
    plt.ylabel('适应度值')
    plt.title('各次运行全局最优解适应度值对比')
    plt.xticks(run_ids)
    plt.tight_layout()
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

# def plot_solution_routes(problem, solution, save_path='optimal_solution_routes.png'):
#     """
#     绘制最优解的配送路径图，使用Individual.decode解码染色体获取路线信息
    
#     参数:
#         problem: 问题实例，包含位置信息
#         solution: 解决方案，包含染色体信息
#     """
#     # 设置默认字体
#     font_path = 'C:/Windows/Fonts/simhei.ttf'
#     font_prop = fm.FontProperties(fname=font_path)
#     plt.rcParams['font.family'] = font_prop.get_name()
#     plt.rcParams['axes.unicode_minus'] = False  # 添加这一行来解决负号显示问题
    
#     plt.figure(figsize=(12, 10))
    
#     # 获取所有点的坐标
#     locations = problem.locations
    
#     # 使用Individual.decode函数解码染色体获取路径和无人机任务
#     decoded_solution = Individual.decode(solution.chromosomes, problem)
    
#     # 为每条路径分配不同颜色
#     colors = plt.cm.tab10(np.linspace(0, 1, len(decoded_solution)))
    
#     # 绘制卡车路线和无人机路线
#     for i, (route, drone_tasks) in enumerate(decoded_solution):
#         color = colors[i]
        
#         # 绘制卡车路线（实线）
#         route_x = [locations[node][0] for node in route]
#         route_y = [locations[node][1] for node in route]
#         plt.plot(route_x, route_y, color=color, linestyle='-', linewidth=2, 
#                  marker='', label=f'卡车路线 {i+1}')
        
#         # 绘制无人机路线（虚线）- 使用与卡车相同的颜色
#         if drone_tasks:
#             has_drawn_drone_label = False
#             for launch_node, targets in drone_tasks.items():
#                 for target in targets:
#                     # 绘制无人机往返路线
#                     drone_x = [locations[launch_node][0], locations[target][0], locations[launch_node][0]]
#                     drone_y = [locations[launch_node][1], locations[target][1], locations[launch_node][1]]
                    
#                     if not has_drawn_drone_label:
#                         plt.plot(drone_x, drone_y, color=color, linestyle='--', linewidth=1.5,
#                                 marker='', alpha=0.7, label=f'无人机路线 {i+1}')
#                         has_drawn_drone_label = True
#                     else:
#                         plt.plot(drone_x, drone_y, color=color, linestyle='--', linewidth=1.5,
#                                 marker='', alpha=0.7)
    
#     # 绘制配送中心（黑色实心方块）
#     depot_loc = locations[0]
#     plt.scatter(depot_loc[0], depot_loc[1], c='black', marker='s', s=100, label='配送中心')
    
#     # 绘制客户点（空心圆圈）
#     for node, loc in locations.items():
#         if node != 0:  # 不是配送中心
#             plt.scatter(loc[0], loc[1], c='white', edgecolor='black', marker='o', s=80)
    
#     # 为所有点添加标签
#     for node, loc in locations.items():
#         plt.annotate(str(node), (loc[0], loc[1]), xytext=(3, 3), 
#                      textcoords='offset points', fontsize=8)
    
#     # 设置图表属性
#     plt.xlabel('X坐标')
#     plt.ylabel('Y坐标')
#     plt.title('全局最优解的配送路径')
#     plt.grid(True, linestyle='--', alpha=0.7)
    
#     # 确保坐标轴交点在(0,0)
#     plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
#     plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
#     # 保持坐标轴比例一致
#     plt.axis('equal')
    
#     # 确保(0,0)点可见
#     x_min, x_max = plt.xlim()
#     y_min, y_max = plt.ylim()
#     plt.xlim(min(x_min, 0), max(x_max, 0))
#     plt.ylim(min(y_min, 0), max(y_max, 0))
    
#     # 添加图例
#     plt.legend(loc='best')
    
#     # 保存图片
#     plt.savefig(save_path, dpi=300, bbox_inches='tight')
#     plt.show()

def plot_solution_routes(problem, solution, save_path='optimal_solution_routes.png'):
    """
    绘制最优解的配送路径图，使用Individual.decode解码染色体获取路线信息
    
    参数:
        problem: 问题实例，包含位置信息
        solution: 解决方案，包含染色体信息
    """
    # 设置默认字体
    font_path = 'C:/Windows/Fonts/simhei.ttf'
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['axes.unicode_minus'] = False  # 添加这一行来解决负号显示问题
    
    plt.figure(figsize=(12, 10))
    
    # 获取所有点的坐标
    locations = problem.locations
    
    # 使用Individual.decode函数解码染色体获取路径和无人机任务
    decoded_solution = Individual.decode(solution.chromosomes, problem)
    
    # 为每条路径分配不同颜色
    colors = plt.cm.tab10(np.linspace(0, 1, len(decoded_solution)))
    
    # 绘制卡车路线和无人机路线
    for i, (route, drone_tasks) in enumerate(decoded_solution):
        color = colors[i]
        
        # 绘制卡车路线（实线）
        route_x = [locations[node][0] for node in route]
        route_y = [locations[node][1] for node in route]
        plt.plot(route_x, route_y, color=color, linestyle='-', linewidth=2, 
                 marker='', label=f'卡车路线 {i+1}')
        
        # 绘制无人机路线（虚线）- 使用与卡车相同的颜色
        if drone_tasks:
            has_drawn_drone_label = False
            for launch_node, targets in drone_tasks.items():
                for target in targets:
                    # 绘制无人机往返路线
                    drone_x = [locations[launch_node][0], locations[target][0], locations[launch_node][0]]
                    drone_y = [locations[launch_node][1], locations[target][1], locations[launch_node][1]]
                    
                    if not has_drawn_drone_label:
                        plt.plot(drone_x, drone_y, color=color, linestyle='--', linewidth=1.5,
                                marker='', alpha=0.7, label=f'无人机路线 {i+1}')
                        has_drawn_drone_label = True
                    else:
                        plt.plot(drone_x, drone_y, color=color, linestyle='--', linewidth=1.5,
                                marker='', alpha=0.7)
    
    # 绘制配送中心（黑色实心方块）
    depot_loc = locations[0]
    plt.scatter(depot_loc[0], depot_loc[1], c='black', marker='s', s=100, label='配送中心')
    
    # 绘制客户点（空心圆圈）
    for node, loc in locations.items():
        if node != 0:  # 不是配送中心
            plt.scatter(loc[0], loc[1], c='white', edgecolor='black', marker='o', s=80)
    
    # 为所有点添加标签
    for node, loc in locations.items():
        plt.annotate(str(node), (loc[0], loc[1]), xytext=(3, 3), 
                     textcoords='offset points', fontsize=8)
    
    # 设置图表属性
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.title('全局最优解的配送路径')
    # plt.grid(True, linestyle='--', alpha=0.7)  # 移除网格线
    
    # 确保坐标轴交点在(0,0)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    # 保持坐标轴比例一致
    plt.axis('equal')
    
    # 确保(0,0)点可见
    x_min, x_max = plt.xlim()
    y_min, y_max = plt.ylim()
    plt.xlim(min(x_min, 0), max(x_max, 0))
    plt.ylim(min(y_min, 0), max(y_max, 0))
    
    # 添加图例
    plt.legend(loc='best')
    
    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()



# def plot_cost_convergence(history, max_generations, save_path='cost_convergence.png'):
#     """
#     绘制总成本收敛曲线 - 显示所有代数据点，横轴刻度间隔为20
    
#     参数:
#         history: 算法历史记录
#         max_generations: 最大迭代次数
#     """
#     # 设置默认字体
#     font_path = 'C:/Windows/Fonts/simhei.ttf'
#     font_prop = fm.FontProperties(fname=font_path)
#     plt.rcParams['font.family'] = font_prop.get_name()

#     plt.figure(figsize=(10, 6))
    
#     # 使用所有代数的数据点
#     all_generations = list(range(len(history['best_total_cost'])))
#     costs = history['best_total_cost']
    
#     # 绘制所有点的连接线
#     plt.plot(all_generations, costs, 'b-', linewidth=2)
    
#     # 额外标记出每20代的点
#     tick_generations = list(range(0, max_generations + 1, 20))
#     if tick_generations[-1] != max_generations:
#         tick_generations.append(max_generations)
    
#     tick_costs = [costs[min(g, len(costs)-1)] for g in tick_generations]
#     plt.plot([min(g, len(costs)-1) for g in tick_generations], tick_costs, 'ro', markersize=6)
    
#     # 设置x轴刻度只显示每20代
#     plt.xticks(tick_generations, tick_generations)
    
#     plt.xlabel('迭代次数')
#     plt.ylabel('总成本')
#     plt.title('差分进化算法总成本收敛曲线')
#     plt.grid(True)
    
#     # 设置x轴范围，确保从0开始
#     plt.xlim(0, max(all_generations))
    
#     plt.savefig(save_path, dpi=300, bbox_inches='tight')
#     plt.show()

def plot_cost_convergence(history, max_generations, save_path='cost_convergence.png'):
    """
    绘制总成本收敛曲线 - 无网格线和标注点
    """
    # 设置默认字体
    font_path = 'C:/Windows/Fonts/simhei.ttf'
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()

    plt.figure(figsize=(10, 6))
    
    # 使用所有代数的数据点
    all_generations = list(range(len(history['best_total_cost'])))
    costs = history['best_total_cost']
    
    # 绘制纯折线，不添加标注点
    plt.plot(all_generations, costs, 'b-', linewidth=2)
    
    # 设置x轴刻度只显示每20代
    tick_generations = list(range(0, max_generations + 1, 20))
    if tick_generations[-1] != max_generations:
        tick_generations.append(max_generations)
    
    plt.xticks(tick_generations, tick_generations)
    
    plt.xlabel('迭代次数')
    plt.ylabel('总成本')
    plt.title('差分进化算法总成本收敛曲线')
    # 网格线已移除
    
    # 设置x轴范围，确保从0开始
    plt.xlim(0, max(all_generations))
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


# def plot_fitness_inverse_convergence(history, max_generations, save_path='fitness_inverse_convergence.png'):
#     """
#     绘制适应度值倒数收敛曲线 - 显示所有代数据点，横轴刻度间隔为20
    
#     参数:
#         history: 算法历史记录
#         max_generations: 最大迭代次数
#     """
#     # 设置默认字体
#     font_path = 'C:/Windows/Fonts/simhei.ttf'
#     font_prop = fm.FontProperties(fname=font_path)
#     plt.rcParams['font.family'] = font_prop.get_name()
    
#     plt.figure(figsize=(10, 6))
    
#     # 使用所有代数的数据点
#     all_generations = list(range(len(history['best_fitness'])))
#     fitness_inverse = [1.0/f if f > 0 else float('inf') for f in history['best_fitness']]
    
#     # 绘制所有点的连接线
#     plt.plot(all_generations, fitness_inverse, 'r-', linewidth=2)
    
#     # 额外标记出每20代的点
#     tick_generations = list(range(0, max_generations + 1, 20))
#     if tick_generations[-1] != max_generations:
#         tick_generations.append(max_generations)
    
#     tick_values = [fitness_inverse[min(g, len(fitness_inverse)-1)] for g in tick_generations]
#     plt.plot([min(g, len(fitness_inverse)-1) for g in tick_generations], tick_values, 'bo', markersize=6)
    
#     # 设置x轴刻度只显示每20代
#     plt.xticks(tick_generations, tick_generations)
    
#     plt.xlabel('迭代次数')
#     plt.ylabel('适应度值倒数')
#     plt.title('差分进化算法适应度值倒数收敛曲线')
#     plt.grid(True)
    
#     # 设置x轴范围，确保从0开始
#     plt.xlim(0, max(all_generations))
    
#     plt.savefig(save_path, dpi=300, bbox_inches='tight')
#     plt.show()


def plot_fitness_inverse_convergence(history, max_generations, save_path='fitness_inverse_convergence.png'):
    """
    绘制适应度值倒数收敛曲线 - 无网格线和标注点
    """
    # 设置默认字体
    font_path = 'C:/Windows/Fonts/simhei.ttf'
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    
    plt.figure(figsize=(10, 6))
    
    # 使用所有代数的数据点
    all_generations = list(range(len(history['best_fitness'])))
    fitness_inverse = [1.0/f if f > 0 else float('inf') for f in history['best_fitness']]
    
    # 绘制纯折线，不添加标注点
    plt.plot(all_generations, fitness_inverse, 'r-', linewidth=2)
    
    # 设置x轴刻度只显示每20代
    tick_generations = list(range(0, max_generations + 1, 20))
    if tick_generations[-1] != max_generations:
        tick_generations.append(max_generations)
    
    plt.xticks(tick_generations, tick_generations)
    
    plt.xlabel('迭代次数')
    plt.ylabel('适应度值倒数')
    plt.title('差分进化算法适应度值倒数收敛曲线')
    # 网格线已移除
    
    # 设置x轴范围，确保从0开始
    plt.xlim(0, max(all_generations))
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()



def run_multiple_tests(problem, num_runs=10, **kwargs):
    """
    多次运行算法并存储结果
    
    参数:
        problem: 问题实例
        num_runs: 运行次数
        **kwargs: 算法参数，包括：
            - gen_interval: 记录代数间隔
            - time_interval: 记录时间间隔(秒)
    
    返回:
        所有运行结果列表
    """
    all_results = []
    
    # 提取记录间隔参数
    gen_interval = kwargs.pop('gen_interval', 5)  # 默认每5代记录一次
    time_interval = kwargs.pop('time_interval', 1)  # 默认每1秒记录一次
    
    for run_id in range(1, num_runs + 1):
        print(f"\n========== 运行 {run_id}/{num_runs} ==========")
        
        # 每次运行使用不同的随机种子
        current_seed = kwargs.get('random_seed', 0) + run_id   
        current_kwargs = kwargs.copy()
        current_kwargs['random_seed'] = current_seed
        
        # 添加记录间隔参数
        current_kwargs['gen_interval'] = gen_interval
        current_kwargs['time_interval'] = time_interval
        
        # 计时开始
        start_time = time.time()
        
        # 求解问题
        best_individual, history = solve_vrpd(problem=problem, **current_kwargs)
        
        # 计时结束
        end_time = time.time()
        solve_time = end_time - start_time
        
        # 存储结果
        result = {
            'run_id': run_id,
            'seed': current_seed,
            'best_individual': best_individual,
            'fitness': best_individual.fitness,
            'total_cost': best_individual.total_cost,
            'penalty': best_individual.penalty,
            'solve_time': solve_time,
            'problem': problem,
            'history': history
        }
        all_results.append(result)
        
        # 输出当前运行结果摘要
        print(f"运行 {run_id} 完成 - 适应度: {best_individual.fitness:.6f}, 总成本: {best_individual.total_cost:.2f}, 惩罚: {best_individual.penalty:.2f}")
    
    return all_results

def analyze_results(all_results):
    """
    分析多次运行的结果
    
    参数:
        all_results: 多次运行结果列表
    """
    # 按总成本排序
    sorted_results = sorted(all_results, key=lambda x: x['total_cost'])
    
    # 计算基本统计数据
    avg_cost = sum(r['total_cost'] for r in all_results) / len(all_results)
    min_cost = sorted_results[0]['total_cost']
    max_cost = sorted_results[-1]['total_cost']
    std_cost = math.sqrt(sum((r['total_cost'] - avg_cost)**2 for r in all_results) / len(all_results))
    
    # # 计算偏差数据
    # max_deviation = max_cost - min_cost
    # max_deviation_percent = (max_deviation / min_cost) * 100 if min_cost > 0 else 0

    # 计算最大偏差（与平均值的最大绝对差）
    max_deviation = max(abs(r['total_cost'] - avg_cost) for r in all_results)
    max_deviation_percent = (max_deviation / avg_cost) * 100 if avg_cost > 0 else 0
    
    # # 计算每次运行与最优解的偏差
    # deviations = [(r['total_cost'] - min_cost) for r in all_results]
    # deviation_percents = [(dev / min_cost) * 100 if min_cost > 0 else 0 for dev in deviations]
    # avg_deviation = sum(deviations) / len(deviations)
    # avg_deviation_percent = sum(deviation_percents) / len(deviation_percents)

    # # 计算平均偏差（与平均值的平均绝对差）
    # avg_deviation = sum(abs(r['total_cost'] - avg_cost) for r in all_results) / len(all_results)
    # avg_deviation_percent = (avg_deviation / avg_cost) * 100 if avg_cost > 0 else 0

    # 计算平均偏差（平均解与最优解的相对偏差）
    avg_deviation = avg_cost - min_cost  # 平均总成本与最优解的绝对差值
    avg_deviation_percent = (avg_deviation / min_cost) * 100 if min_cost > 0 else 0  # 相对于最优解的百分比

    # === 精确度代理评估 ===（新增核心指标）
    best_result = min(all_results, key=lambda r: r['total_cost'])
    best_individual = best_result['best_individual']
    system_time = calculate_system_operation_time(
        best_result['problem'], 
        best_individual.routes, 
        best_individual.drone_tasks
    )
    
    # 计算所有值对最小值的偏离（gaps是每次运行结果与最优解（所有运行中成本最低的解）之间的差距）
    gaps = [r['total_cost'] - best_result['total_cost'] for r in all_results]
    # 计算平均偏离、最大偏离和命中率
    avg_gap = sum(gaps) / len(all_results)
    max_gap = max(gaps)
    hit_rate = sum(1 for g in gaps if g == 0) / len(all_results) * 100
    
    # 计算平均求解时间
    avg_solve_time = sum(r['solve_time'] for r in all_results) / len(all_results)
    
    # 保存统计数据到结果对象
    stats = {
        'avg_cost': avg_cost,
        'min_cost': min_cost,
        'max_cost': max_cost,
        'std_cost': std_cost,
        'max_deviation': max_deviation,
        'max_deviation_percent': max_deviation_percent,
        'avg_deviation': avg_deviation,
        'avg_deviation_percent': avg_deviation_percent,
        'avg_solve_time': avg_solve_time,
        # 添加新指标
        'avg_gap': avg_gap,
        'max_gap': max_gap,
        'hit_rate': hit_rate,
        'system_operation_time': system_time
    }
    
    # 打印统计数据
    print("\n========== 多次运行结果统计 ==========")
    print(f"运行次数: {len(all_results)}")
    print(f"平均总成本: {avg_cost:.2f}")
    print(f"最小总成本: {min_cost:.2f} (运行 {sorted_results[0]['run_id']})")
    print(f"最大总成本: {max_cost:.2f} (运行 {sorted_results[-1]['run_id']})")
    print(f"总成本标准差: {std_cost:.2f}")
    # 打印系统运行时间
    print(f"\n最佳解的路由系统运行时间: {system_time:.4f} 小时 ({system_time*60:.2f} 分钟)")
    
    print("\n========== 算法精度与稳定性分析 ==========")
    # 1. 计算稳定性评估指标
    stability_message = f"""
    [稳定性评估]
    • 平均偏差: {avg_deviation:.2f} ({avg_deviation/avg_cost*100:.1f}%)
    • 最大偏差: {max_deviation:.2f} ({max_deviation/avg_cost*100:.1f}%)
    """

    # 2. 计算解质量评估指标
    quality_message = f"""
    [解质量评估] (基准: {best_result['total_cost']:.2f})
    • 平均偏离最佳: {avg_gap:.2f} ({avg_gap/best_result['total_cost']*100:.1f}%)
    • 最大偏离最佳: {max_gap:.2f} ({max_gap/best_result['total_cost']*100:.1f}%)
    • 历史最优命中率: {hit_rate:.1f}%
    """

    # 3. 组合并打印最终结果
    print(stability_message + quality_message)

    # print(f"最大偏差: {max_deviation:.2f} ({max_deviation_percent:.2f}%)")
    # print(f"平均偏差: {avg_deviation:.2f} ({avg_deviation_percent:.2f}%)")
    print(f"平均求解时间: {avg_solve_time:.2f}秒")
    
    # 打印每次运行的结果摘要
    print("\n各次运行结果摘要:")
    print("运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间")
    print("-" * 70)
    for result in all_results:
        print(f"{result['run_id']:6d} | {result['seed']:8d} | {result['fitness']:.6f} | {result['total_cost']:7.2f} | {result['penalty']:7.2f} | {result['solve_time']:.2f}s")
    
    # 返回最佳结果和统计数据
    return sorted_results[0], stats


def save_convergence_data(history, save_dir, prefix=''):
    """
    将收敛数据保存为CSV文件
    
    参数:
        history: 算法历史记录
        save_dir: 保存目录
        prefix: 文件名前缀
    """
    import pandas as pd
    import os
    
    # 确保目录存在
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 基于代数的收敛数据
    generations = history['generations']
    gen_data = {
        'Generation': generations,
        'Best_Fitness': history['best_fitness'],
        'Best_Cost': history['best_total_cost'],
        'Best_Penalty': history['best_penalty'],
        'Avg_Fitness': history['avg_fitness'],
        'Elapsed_Time': history['elapsed_time']
    }
    
    gen_df = pd.DataFrame(gen_data)
    gen_csv_path = os.path.join(save_dir, f'{prefix}convergence_by_generation.csv')
    gen_df.to_csv(gen_csv_path, index=False)
    print(f"基于代数的收敛数据已保存到: {gen_csv_path}")
    
    # 2. 基于时间的收敛数据
    # 将时间数据转换为以秒为单位的整数时间点
    time_points = []
    time_indices = []
    
    # 找出每个时间点对应的索引
    for i, time_point in enumerate(history['elapsed_time']):
        # 将浮点时间转换为整数秒
        int_time = int(time_point)
        if not time_points or int_time > time_points[-1]:
            time_points.append(int_time)
            time_indices.append(i)
    
    # 提取对应时间点的数据
    time_data = {
        'Elapsed_Time': [history['elapsed_time'][i] for i in time_indices],
        'Generation': [history['generations'][i] for i in time_indices],
        'Best_Fitness': [history['best_fitness'][i] for i in time_indices],
        'Best_Cost': [history['best_total_cost'][i] for i in time_indices],
        'Best_Penalty': [history['best_penalty'][i] for i in time_indices],
        'Avg_Fitness': [history['avg_fitness'][i] for i in time_indices]
    }
    
    time_df = pd.DataFrame(time_data)
    time_csv_path = os.path.join(save_dir, f'{prefix}convergence_by_time.csv')
    time_df.to_csv(time_csv_path, index=False)
    print(f"基于时间的收敛数据已保存到: {time_csv_path}")
    
    return gen_csv_path, time_csv_path

def main():
    """主函数 - 运行差分进化算法求解VRP-D问题"""
    # 问题实例
    instance_name = 'RC_随机+聚类_数据集/RC101-61-1.vrp'
    
    # 算法参数
    pop_size = 100
    max_generations = 800
    max_runtime = 180
    mutation_factor = 0.9
    crossover_rate = 0.8
    elite_ratio = 0.1  # 10/100
    random_seed = 0
    
    # 收敛数据记录参数
    gen_interval = 1  # 每隔1代记录一次
    time_interval = 1  # 每隔1秒记录一次（秒）
    
    print(f"加载问题: {instance_name}")
    problem = Problem(instance_name)
    print(f"客户点数量: {len(problem.customers)}")
    print(f"车辆数量: {problem.num_vehicles}")
    
    # 设置运行次数
    num_runs = 1
    
    print(f"\n开始使用solve_vrpd函数求解 ({num_runs}次)...")
    
    # 多次运行求解
    all_results = run_multiple_tests(
        problem=problem,
        num_runs=num_runs,
        pop_size=pop_size,
        max_generations=max_generations,
        max_runtime=max_runtime,  
        mutation_factor=mutation_factor,
        crossover_rate=crossover_rate,
        elite_ratio=elite_ratio,
        random_seed=random_seed,
        gen_interval=gen_interval,    # 添加代数间隔参数
        time_interval=time_interval   # 添加时间间隔参数
    )
    
    # 分析结果并获取最佳解
    best_result, stats = analyze_results(all_results)
    best_individual = best_result['best_individual']

    # 将所有结果保存到文本文件
    import pickle
    import os
    import json

    # 提取数据集名称（不含路径）和种子ID
    dataset_basename = os.path.basename(instance_name)
    dataset_name = os.path.splitext(dataset_basename)[0]
    seed_id = random_seed

    # 创建结果目录
    result_dir = f'results_{dataset_name}_seed{seed_id}_{num_runs}runs'
    os.makedirs(result_dir, exist_ok=True)

    # 创建txt文件
    result_file = os.path.join(result_dir, 'results.txt')

    # 保存详细结果到文本文件
    with open(result_file, 'w', encoding='utf-8') as f:
        # 写入标题
        f.write(f"========== {dataset_name} 数据集求解结果 ==========\n\n")
        
        # 写入算法配置信息
        f.write("算法配置:\n")
        f.write(f"数据集: {instance_name}\n")
        f.write(f"种群大小: {pop_size}\n")
        f.write(f"最大迭代次数: {max_generations}\n")
        f.write(f"最大运行时间: {max_runtime}秒\n")
        f.write(f"变异因子: {mutation_factor}\n")
        f.write(f"交叉率: {crossover_rate}\n")
        f.write(f"精英比例: {elite_ratio}\n")
        f.write(f"初始随机种子: {random_seed}\n")
        f.write(f"运行次数: {num_runs}\n\n")
        
        # 写入统计结果摘要
        f.write("========== 多次运行结果统计 ==========\n")
        f.write(f"运行次数: {len(all_results)}\n")
        f.write(f"平均总成本: {stats['avg_cost']:.2f}\n")
        f.write(f"最小总成本: {stats['min_cost']:.2f} (运行 {best_result['run_id']})\n")
        f.write(f"最大总成本: {stats['max_cost']:.2f} (运行 {max(all_results, key=lambda x: x['total_cost'])['run_id']})\n")
        f.write(f"总成本标准差: {stats['std_cost']:.2f}\n\n")
        
        # 写入算法精度与稳定性分析
        f.write("========== 算法精度与稳定性分析 ==========\n")
        # 1. 写入稳定性评估指标
        f.write("[稳定性评估]\n")
        f.write(f"• 平均偏差: {stats['avg_deviation']:.2f} ({stats['avg_deviation']/stats['avg_cost']*100:.1f}%)\n")
        f.write(f"• 最大偏差: {stats['max_deviation']:.2f} ({stats['max_deviation']/stats['avg_cost']*100:.1f}%)\n\n")

        # 2. 写入解质量评估指标
        f.write(f"[解质量评估] (基准: {best_result['total_cost']:.2f})\n")
        f.write(f"• 平均偏离最佳: {stats['avg_gap']:.2f} ({stats['avg_gap']/best_result['total_cost']*100:.1f}%)\n")
        f.write(f"• 最大偏离最佳: {stats['max_gap']:.2f} ({stats['max_gap']/best_result['total_cost']*100:.1f}%)\n")
        f.write(f"• 历史最优命中率: {stats['hit_rate']:.1f}%\n\n")

        # 添加平均求解时间
        f.write(f"平均求解时间: {stats['avg_solve_time']:.2f}秒\n\n")
        
        # 写入每次运行的结果
        f.write("所有运行结果:\n")
        f.write("运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)\n")
        f.write("-" * 70 + "\n")
        for result in all_results:
            f.write(f"{result['run_id']:6d} | {result['seed']:8d} | {result['fitness']:.6f} | "
                    f"{result['total_cost']:7.2f} | {result['penalty']:7.2f} | {result['solve_time']:.2f}\n")
    
        
        # # 写入最佳解详细信息
        # f.write("\n最佳解详细信息:\n")
        # f.write(f"运行ID: {best_result['run_id']}\n")
        # f.write(f"适应度: {best_result['fitness']:.6f}\n")
        # f.write(f"总成本: {best_result['total_cost']:.2f}\n")
        # f.write(f"惩罚值: {best_result['penalty']:.2f}\n")
        # # 在保存最佳解详细信息部分添加
        # f.write(f"系统运行时间: {stats['system_operation_time']:.4f} 小时 ({stats['system_operation_time']*60:.2f} 分钟)\n")
        
        # 写入最佳解详细信息
        f.write("\n最佳解详细信息:\n")
        f.write(f"运行ID: {best_result['run_id']}\n")
        f.write(f"适应度: {best_result['fitness']:.6f}\n")
        f.write(f"总成本: {best_result['total_cost']:.2f}\n")
        f.write(f"惩罚值: {best_result['penalty']:.2f}\n")
        
        # 详细成本分析
        best_individual = best_result['best_individual']
        problem = best_result['problem']
        
        # 获取成本分解
        cost_breakdown = calculate_total_cost(
            problem, 
            best_individual.routes, 
            best_individual.drone_tasks,
            return_breakdown=True
        )
        
        f.write(f"\n========== 成本详细分解 ==========\n")
        f.write(f"• 卡车运输成本: {cost_breakdown['vehicle_cost']:.2f}\n")
        f.write(f"• 无人机运行成本: {cost_breakdown['drone_cost']:.2f}\n")
        f.write(f"• 卡车等待成本: {cost_breakdown['waiting_cost']:.2f}\n")
        f.write(f"• 卡车固定成本: {cost_breakdown['truck_fixed_cost']:.2f}\n")
        f.write(f"• 无人机固定成本: {cost_breakdown['drone_fixed_cost']:.2f}\n")
        f.write(f"• 系统运营时间成本: {cost_breakdown['time_cost']:.2f}\n")
        f.write(f"• 不含运营时间成本的总成本: {cost_breakdown['cost_without_time']:.2f}\n")
        f.write(f"• 包含运营时间成本的总成本: {cost_breakdown['total_cost']:.2f}\n")
        f.write(f"\n系统运营时间: {stats['system_operation_time']:.4f} 小时 ({stats['system_operation_time']*60:.2f} 分钟)\n")
        
        # 保存最佳解的染色体
        f.write("\n最佳解染色体:\n")
        for i, chrom in enumerate(best_individual.chromosomes):
            f.write(f"染色体 {i+1}: {chrom}\n")
        
        # 保存最佳解的路线信息（解码后）
        decoded_solution = Individual.decode(best_individual.chromosomes, problem)
        f.write("\n最佳解路线详情:\n")
        for i, (route, drone_tasks) in enumerate(decoded_solution):
            f.write(f"路线 {i+1}: {route}\n")
            if drone_tasks:
                f.write(f"  无人机任务:\n")
                for launch_node, targets in drone_tasks.items():
                    f.write(f"    从节点 {launch_node} 发射无人机访问: {targets}\n")
        
        # 保存收敛历史数据
        f.write("\n收敛历史数据 (每10代):\n")
        f.write("代数 | 最佳适应度 | 最佳成本 | 惩罚值\n")
        f.write("-" * 50 + "\n")
        history = best_result['history']
        for i in range(0, len(history['best_fitness']), 10):
            if i < len(history['best_fitness']):
                f.write(f"{i:4d} | {history['best_fitness'][i]:.6f} | "
                        f"{history['best_total_cost'][i]:7.2f} | {history['best_penalty'][i]:7.2f}\n")

    print(f"\n所有运行结果已保存到文本文件: {result_file}")

    # 同时保存收敛曲线和路线图到该目录
    convergence_file = os.path.join(result_dir, 'cost_convergence.png')
    fitness_file = os.path.join(result_dir, 'fitness_convergence.png')
    routes_file = os.path.join(result_dir, 'routes.png')

    # 保存原始数据供可能的后续分析
    raw_data_file = os.path.join(result_dir, 'raw_data.json')
    # 创建可序列化的数据结构
    serializable_results = []
    for result in all_results:
        serializable_result = {
            'run_id': result['run_id'],
            'seed': result['seed'],
            'fitness': result['fitness'],
            'total_cost': result['total_cost'],
            'penalty': result['penalty'],
            'solve_time': result['solve_time'],
            'chromosomes': [list(chrom) for chrom in result['best_individual'].chromosomes],
            # 删除无法序列化的对象
            # 'history' 和 'problem' 对象不会被包含
        }
        serializable_results.append(serializable_result)

    with open(raw_data_file, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=2)
    
    # 保存收敛数据到CSV文件
    save_convergence_data(best_result['history'], result_dir, prefix='best_run_')
    
    # 绘制最佳解的收敛曲线和路径图
    print("\n绘制最佳解的收敛曲线...")
    plot_cost_convergence(best_result['history'], max_generations, save_path=convergence_file)
    plot_fitness_inverse_convergence(best_result['history'], max_generations, save_path=fitness_file)

    # 绘制最佳解的配送路径
    print("\n绘制最佳解的配送路径...")
    plot_solution_routes(problem, best_individual, save_path=routes_file)
    
    print("\n程序执行完毕.")

if __name__ == "__main__":
    main()





