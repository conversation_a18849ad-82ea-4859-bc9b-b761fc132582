NAME : R101-31-2
COMMENT : (<PERSON> dataset, Modified for VRPD)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 8.75 8.75
 1 13.75 5.00
 2 3.75 7.50
 3 13.75 15.00
 4 3.75 2.50
 5 11.25 5.00
 6 13.75 1.25
 7 16.25 5.00
 8 11.25 7.50
 9 16.00 10.50
 10 10.00 15.00
 11 7.75 13.00
 12 8.75 17.25
 13 15.75 16.25
 14 0.50 15.00
 15 10.00 6.25
 16 5.75 0.75
 17 1.50 9.50
 18 1.50 17.00
 19 6.75 10.75
 20 9.25 7.75
 21 5.25 6.00
 22 3.00 6.00
 23 11.00 4.25
 24 2.75 7.75
 25 1.00 4.50
 26 6.50 6.75
 27 5.50 6.75
 28 6.25 5.25
 29 4.75 5.25
 30 5.00 6.50

DEMAND_SECTION
 0 0 0      
 1 4.7 0.0      // 仅送货需求客户 - light包裹
 2 0.0 3.9      // 仅取货需求客户 - light包裹
 3 3.7 0.0      // 仅送货需求客户 - light包裹
 4 1.3 1.2      // 送货取货双需求客户 - light包裹
 5 2.3 0.0      // 仅送货需求客户 - light包裹
 6 8.6 3.3      // 送货取货双需求客户 - medium包裹
 7 2.6 0.0      // 仅送货需求客户 - light包裹
 8 0.0 2.2      // 仅取货需求客户 - light包裹
 9 0.0 2.7      // 仅取货需求客户 - light包裹
 10 8.4 0.0      // 仅送货需求客户 - medium包裹
 11 0.0 8.1      // 仅取货需求客户 - medium包裹
 12 0.0 3.2      // 仅取货需求客户 - light包裹
 13 2.1 0.0      // 仅送货需求客户 - light包裹
 14 3.3 2.5      // 送货取货双需求客户 - light包裹
 15 2.8 0.0      // 仅送货需求客户 - light包裹
 16 0.0 2.9      // 仅取货需求客户 - light包裹
 17 9.0 2.4      // 送货取货双需求客户 - medium包裹
 18 0.0 3.1      // 仅取货需求客户 - light包裹
 19 2.3 0.0      // 仅送货需求客户 - light包裹
 20 8.8 6.2      // 送货取货双需求客户 - medium包裹
 21 1.8 0.0      // 仅送货需求客户 - light包裹
 22 2.0 1.5      // 送货取货双需求客户 - light包裹
 23 5.5 2.9      // 送货取货双需求客户 - medium包裹
 24 1.8 0.0      // 仅送货需求客户 - light包裹
 25 3.0 0.0      // 仅送货需求客户 - light包裹
 26 3.6 0.0      // 仅送货需求客户 - light包裹
 27 4.0 0.0      // 仅送货需求客户 - light包裹
 28 1.4 0.0      // 仅送货需求客户 - light包裹
 29 1.7 0.0      // 仅送货需求客户 - light包裹
 30 15.2 0.0      // 仅送货需求客户 - heavy包裹

DEPOT_SECTION
 0
 -1
EOF