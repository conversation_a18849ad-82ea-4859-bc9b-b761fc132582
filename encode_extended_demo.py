#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
_encode_extended 函数完整示例演示
逐步展示编码过程的每个阶段
"""

from data_loader import Individual, DroneTask

def demonstrate_encode_extended():
    """演示_encode_extended函数的完整过程"""
    print("=" * 80)
    print("_encode_extended 函数完整演示")
    print("=" * 80)
    
    # 输入数据
    routes = [
        [0, 6, 12, 18, 0],    # 卡车1路径
        [0, 15, 22, 0]        # 卡车2路径
    ]
    
    drone_tasks = {
        6: {  # 发射点6
            'drone_tasks': [
                DroneTask(1, [4, 7], 6, 12, 89.4),    # 无人机1服务客户4,7
                DroneTask(2, [3], 6, 12, 45.6)        # 无人机2服务客户3
            ],
            'recovery_point': 12
        },
        15: {  # 发射点15
            'drone_tasks': [
                DroneTask(3, [11, 14], 15, 22, 134.5)  # 无人机3服务客户11,14
            ],
            'recovery_point': 22
        }
    }
    
    print("输入数据:")
    print(f"  卡车路径: {routes}")
    print("  无人机任务:")
    for launch_point, task_info in drone_tasks.items():
        print(f"    发射点{launch_point} → 回收点{task_info['recovery_point']}")
        for task in task_info['drone_tasks']:
            print(f"      无人机{task.drone_id}: 服务客户{task.customer_sequence}")
    print()
    
    # 手动模拟编码过程
    print("编码过程详解:")
    print("-" * 50)
    
    # 第一步：格式检查
    print("第一步：格式检查")
    first_value = next(iter(drone_tasks.values()))
    print(f"  第一个值: {first_value}")
    print(f"  是字典: {isinstance(first_value, dict)}")
    print(f"  包含'drone_tasks': {'drone_tasks' in first_value}")
    print("  → 确认为扩展格式，直接使用")
    print()
    
    # 第二步：合并卡车路径
    print("第二步：合并卡车路径")
    path_layer = []
    for i, route in enumerate(routes):
        print(f"  处理路径{i+1}: {route}")
        if i > 0 and len(path_layer) > 0 and path_layer[-1] == 0:
            print(f"    跳过重复配送中心，添加: {route[1:]}")
            path_layer.extend(route[1:])
        else:
            print(f"    直接添加: {route}")
            path_layer.extend(route)
        print(f"    当前path_layer: {path_layer}")
    print(f"  最终path_layer: {path_layer}")
    print()
    
    # 第三步：收集发射点和回收点
    print("第三步：收集发射点和回收点")
    launch_points = set(drone_tasks.keys())
    recovery_points = set()
    for launch_point, task_info in drone_tasks.items():
        if task_info['recovery_point'] is not None:
            recovery_points.add(task_info['recovery_point'])
    print(f"  发射点集合: {launch_points}")
    print(f"  回收点集合: {recovery_points}")
    print()
    
    # 第四步：设置服务方式
    print("第四步：设置服务方式")
    service_layer = [0] * len(path_layer)
    print(f"  初始service_layer: {service_layer}")
    
    for i, node in enumerate(path_layer):
        old_service = service_layer[i]
        if node == 0:
            service_layer[i] = 0
            service_desc = "配送中心"
        elif node in launch_points and node in recovery_points:
            service_layer[i] = 4
            service_desc = "先回收再发射然后卡车服务"
        elif node in launch_points:
            service_layer[i] = 2
            service_desc = "先发射然后卡车服务"
        elif node in recovery_points:
            service_layer[i] = 3
            service_desc = "先回收然后卡车服务"
        else:
            service_layer[i] = 0
            service_desc = "仅卡车服务"
        
        print(f"  节点{node}: {service_desc} → service_layer[{i}] = {service_layer[i]}")
    
    print(f"  最终service_layer: {service_layer}")
    print()
    
    # 第五步：插入无人机客户点
    print("第五步：插入无人机客户点")
    final_path_layer = []
    final_service_layer = []
    
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        print(f"  处理节点{node}(服务方式{service})")
        final_path_layer.append(node)
        final_service_layer.append(service)
        print(f"    添加卡车节点: ({node}, {service})")
        
        if service in [2, 4] and node in drone_tasks:
            print(f"    → 发射点，插入无人机客户:")
            drone_tasks_list = drone_tasks[node]['drone_tasks']
            for j, drone_task in enumerate(drone_tasks_list):
                customer_id = drone_task.customer_id  # 使用兼容属性
                final_path_layer.append(customer_id)
                final_service_layer.append(1)
                print(f"      无人机{drone_task.drone_id}客户{customer_id}: ({customer_id}, 1)")
        
        print(f"    当前结果: path={final_path_layer}, service={final_service_layer}")
        print()
    
    print(f"最终编码结果:")
    print(f"  path_layer:    {tuple(final_path_layer)}")
    print(f"  service_layer: {tuple(final_service_layer)}")
    print()
    
    # 使用实际函数验证
    print("使用实际函数验证:")
    actual_result = Individual._encode_extended(routes, drone_tasks)
    print(f"  实际结果: {actual_result}")
    
    # 比较结果
    expected = (tuple(final_path_layer), tuple(final_service_layer))
    if actual_result == expected:
        print("✅ 手动计算与实际函数结果一致！")
    else:
        print("❌ 结果不一致，需要检查")
        print(f"  期望: {expected}")
        print(f"  实际: {actual_result}")

def demonstrate_service_types():
    """演示不同服务类型的判断逻辑"""
    print("\n" + "=" * 80)
    print("服务类型判断逻辑演示")
    print("=" * 80)
    
    # 创建测试场景
    test_scenarios = [
        {
            'name': '普通客户点',
            'node': 5,
            'launch_points': {6, 15},
            'recovery_points': {12, 22},
            'expected_service': 0,
            'description': '仅卡车服务'
        },
        {
            'name': '配送中心',
            'node': 0,
            'launch_points': {6, 15},
            'recovery_points': {12, 22},
            'expected_service': 0,
            'description': '配送中心'
        },
        {
            'name': '发射点',
            'node': 6,
            'launch_points': {6, 15},
            'recovery_points': {12, 22},
            'expected_service': 2,
            'description': '先发射然后卡车服务'
        },
        {
            'name': '回收点',
            'node': 12,
            'launch_points': {6, 15},
            'recovery_points': {12, 22},
            'expected_service': 3,
            'description': '先回收然后卡车服务'
        },
        {
            'name': '既是发射点又是回收点',
            'node': 10,
            'launch_points': {6, 10, 15},
            'recovery_points': {10, 12, 22},
            'expected_service': 4,
            'description': '先回收再发射然后卡车服务'
        }
    ]
    
    for scenario in test_scenarios:
        node = scenario['node']
        launch_points = scenario['launch_points']
        recovery_points = scenario['recovery_points']
        
        print(f"场景: {scenario['name']}")
        print(f"  节点: {node}")
        print(f"  发射点集合: {launch_points}")
        print(f"  回收点集合: {recovery_points}")
        
        # 判断逻辑
        if node == 0:
            service = 0
            desc = "配送中心"
        elif node in launch_points and node in recovery_points:
            service = 4
            desc = "先回收再发射然后卡车服务"
        elif node in launch_points:
            service = 2
            desc = "先发射然后卡车服务"
        elif node in recovery_points:
            service = 3
            desc = "先回收然后卡车服务"
        else:
            service = 0
            desc = "仅卡车服务"
        
        print(f"  判断结果: 服务方式{service} - {desc}")
        print(f"  期望结果: 服务方式{scenario['expected_service']} - {scenario['description']}")
        
        if service == scenario['expected_service']:
            print("  ✅ 判断正确")
        else:
            print("  ❌ 判断错误")
        print()

if __name__ == "__main__":
    demonstrate_encode_extended()
    demonstrate_service_types()
