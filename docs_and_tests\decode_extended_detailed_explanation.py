#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
_decode_extended 函数逐行详细解释
用具体实例演示每行代码的含义和逻辑
"""

from data_loader import Individual, DroneTask

def explain_decode_extended_step_by_step():
    """逐步解释_decode_extended函数"""
    print("=" * 80)
    print("_decode_extended 函数逐行详细解释")
    print("=" * 80)
    
    # 示例输入数据
    chromosomes = (
        [0, 6, 4, 7, 12, 0],  # path_layer
        [0, 2, 1, 1, 3, 0]    # service_layer
    )
    
    print("📋 示例输入数据:")
    print(f"  path_layer:    {chromosomes[0]}")
    print(f"  service_layer: {chromosomes[1]}")
    print("  解释: 卡车从配送中心出发，在节点6发射无人机服务客户4和7，在节点12回收无人机，返回配送中心")
    print()

def step1_input_processing():
    """第1步：输入处理"""
    print("=" * 80)
    print("第1步：输入处理和验证")
    print("=" * 80)
    
    chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
    
    print("代码行559：")
    print("  path_layer, service_layer = chromosomes if not hasattr(chromosomes, 'chromosomes') else chromosomes.chromosomes")
    print()
    print("逻辑解释：")
    print("  - 检查输入是否为Individual对象（有chromosomes属性）")
    print("  - 如果是普通元组，直接解包")
    print("  - 如果是Individual对象，提取其chromosomes属性")
    print()
    
    # 模拟执行
    path_layer, service_layer = chromosomes
    print("执行结果：")
    print(f"  path_layer = {path_layer}")
    print(f"  service_layer = {service_layer}")
    print()
    
    print("代码行561-562：")
    print("  if len(path_layer) != len(service_layer):")
    print("      raise ValueError('路径层和服务方式层长度不一致')")
    print()
    print("逻辑解释：")
    print("  - 验证两个层的长度必须相等")
    print("  - 每个路径节点都必须有对应的服务方式")
    print()
    print("执行结果：")
    print(f"  path_layer长度: {len(path_layer)}")
    print(f"  service_layer长度: {len(service_layer)}")
    print(f"  长度检查: {'✅ 通过' if len(path_layer) == len(service_layer) else '❌ 失败'}")
    print()

def step2_route_segmentation():
    """第2步：路径分割"""
    print("=" * 80)
    print("第2步：路径分割")
    print("=" * 80)
    
    path_layer = [0, 6, 4, 7, 12, 0]
    service_layer = [0, 2, 1, 1, 3, 0]
    
    print("代码行565：")
    print("  depot_positions = [i for i, node in enumerate(path_layer) if node == 0]")
    print()
    print("逻辑解释：")
    print("  - 找到所有配送中心（节点0）在路径中的位置索引")
    print("  - 用于后续按配送中心分割路径段")
    print()
    
    # 模拟执行
    depot_positions = [i for i, node in enumerate(path_layer) if node == 0]
    print("执行过程：")
    for i, node in enumerate(path_layer):
        print(f"  索引{i}: 节点{node} {'← 配送中心' if node == 0 else ''}")
    print()
    print(f"执行结果：depot_positions = {depot_positions}")
    print()
    
    print("代码行567-574：路径段分割循环")
    print("  routes_segments = []")
    print("  start_idx = 0")
    print("  for i in range(len(depot_positions) - 1):")
    print("      end_idx = depot_positions[i + 1]")
    print("      segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, end_idx + 1)]")
    print("      routes_segments.append(segment)")
    print("      start_idx = depot_positions[i + 1]")
    print()
    
    # 模拟执行
    routes_segments = []
    start_idx = 0
    
    print("执行过程：")
    print(f"  depot_positions = {depot_positions}")
    print(f"  需要处理 {len(depot_positions) - 1} 个路径段")
    
    for i in range(len(depot_positions) - 1):
        end_idx = depot_positions[i + 1]
        segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, end_idx + 1)]
        routes_segments.append(segment)
        print(f"  路径段{i+1}: 从索引{start_idx}到{end_idx}")
        print(f"    segment = {segment}")
        start_idx = depot_positions[i + 1]
    
    print()
    print("代码行577-579：处理最后一段")
    print("  if start_idx < len(path_layer):")
    print("      segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, len(path_layer))]")
    print("      routes_segments.append(segment)")
    print()
    
    # 处理最后一段
    if start_idx < len(path_layer):
        segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, len(path_layer))]
        routes_segments.append(segment)
        print(f"  最后一段: 从索引{start_idx}到{len(path_layer)-1}")
        print(f"    segment = {segment}")
    
    print()
    print(f"最终结果：routes_segments = {routes_segments}")
    print()

def step3_segment_decoding():
    """第3步：路径段解码"""
    print("=" * 80)
    print("第3步：路径段解码")
    print("=" * 80)
    
    # 使用第2步的结果
    routes_segments = [[(0, 0), (6, 2), (4, 1), (7, 1), (12, 3), (0, 0)]]
    
    print("代码行582-590：初始化解码变量")
    print("  decoded_solution = []")
    print("  for segment in routes_segments:")
    print("      truck_route = []")
    print("      drone_tasks_extended = {}")
    print("      active_drone_tasks = {}  # {launch_point: {'customers': [], 'recovery_point': None}}")
    print("      drone_id_counter = 1")
    print()
    
    print("逻辑解释：")
    print("  - decoded_solution: 存储最终解码结果")
    print("  - truck_route: 当前路径段的卡车路径")
    print("  - drone_tasks_extended: 扩展格式的无人机任务")
    print("  - active_drone_tasks: 跟踪当前活跃的无人机任务")
    print("  - drone_id_counter: 无人机ID计数器")
    print()
    
    # 模拟处理第一个路径段
    segment = routes_segments[0]
    print(f"处理路径段: {segment}")
    print()
    
    decoded_solution = []
    truck_route = []
    drone_tasks_extended = {}
    active_drone_tasks = {}
    drone_id_counter = 1
    
    print("代码行592-629：逐节点处理循环")
    print("  i = 0")
    print("  while i < len(segment):")
    print("      node, service = segment[i]")
    print("      # 根据服务方式处理...")
    print()

def step4_node_processing():
    """第4步：逐节点处理"""
    print("=" * 80)
    print("第4步：逐节点处理详解")
    print("=" * 80)
    
    segment = [(0, 0), (6, 2), (4, 1), (7, 1), (12, 3), (0, 0)]
    truck_route = []
    active_drone_tasks = {}
    
    print("逐节点处理过程：")
    print()
    
    for i, (node, service) in enumerate(segment):
        print(f"步骤{i+1}: 处理节点{node}，服务方式{service}")
        
        if service == 0:  # 配送中心或仅卡车服务
            print("  代码行596-597：")
            print("    if service == 0:")
            print("        truck_route.append(node)")
            truck_route.append(node)
            print(f"    → 添加到卡车路径: {node}")
            
        elif service == 1:  # 无人机客户点
            print("  代码行599-604：")
            print("    elif service == 1:")
            print("        if active_drone_tasks:")
            print("            latest_launch = max(active_drone_tasks.keys())")
            print("            active_drone_tasks[latest_launch]['customers'].append(node)")
            if active_drone_tasks:
                latest_launch = max(active_drone_tasks.keys())
                active_drone_tasks[latest_launch]['customers'].append(node)
                print(f"    → 添加客户{node}到发射点{latest_launch}的任务")
            
        elif service == 2:  # 发射+卡车服务
            print("  代码行606-609：")
            print("    elif service == 2:")
            print("        truck_route.append(node)")
            print("        active_drone_tasks[node] = {'customers': [], 'recovery_point': None}")
            truck_route.append(node)
            active_drone_tasks[node] = {'customers': [], 'recovery_point': None}
            print(f"    → 添加到卡车路径: {node}")
            print(f"    → 开始新的无人机任务，发射点: {node}")
            
        elif service == 3:  # 回收+卡车服务
            print("  代码行611-617：")
            print("    elif service == 3:")
            print("        truck_route.append(node)")
            print("        for launch_point in sorted(active_drone_tasks.keys()):")
            print("            if active_drone_tasks[launch_point]['recovery_point'] is None:")
            print("                active_drone_tasks[launch_point]['recovery_point'] = node")
            print("                break")
            truck_route.append(node)
            for launch_point in sorted(active_drone_tasks.keys()):
                if active_drone_tasks[launch_point]['recovery_point'] is None:
                    active_drone_tasks[launch_point]['recovery_point'] = node
                    print(f"    → 添加到卡车路径: {node}")
                    print(f"    → 设置发射点{launch_point}的回收点为: {node}")
                    break
        
        print(f"  当前状态:")
        print(f"    truck_route = {truck_route}")
        print(f"    active_drone_tasks = {active_drone_tasks}")
        print()
    
    return truck_route, active_drone_tasks

def step5_drone_task_creation():
    """第5步：DroneTask对象创建"""
    print("=" * 80)
    print("第5步：DroneTask对象创建")
    print("=" * 80)

    # 使用第4步的结果
    active_drone_tasks = {6: {'customers': [4, 7], 'recovery_point': 12}}
    drone_id_counter = 1
    drone_tasks_extended = {}

    print("代码行632-651：转换为DroneTask对象")
    print("  for launch_point, task_info in active_drone_tasks.items():")
    print("      if task_info['customers']:  # 只处理有客户的任务")
    print("          if task_info['customers']:")
    print("              drone_task = DroneTask(")
    print("                  drone_id=drone_id_counter,")
    print("                  customer_sequence=task_info['customers'],")
    print("                  launch_point=launch_point,")
    print("                  recovery_point=task_info['recovery_point'],")
    print("                  total_energy=0.0")
    print("              )")
    print("              drone_tasks_list = [drone_task]")
    print("              drone_id_counter += 1")
    print("          else:")
    print("              drone_tasks_list = []")
    print()

    print("执行过程：")
    for launch_point, task_info in active_drone_tasks.items():
        print(f"  处理发射点{launch_point}:")
        print(f"    客户列表: {task_info['customers']}")
        print(f"    回收点: {task_info['recovery_point']}")

        if task_info['customers']:
            print(f"    → 创建DroneTask对象:")
            drone_task = DroneTask(
                drone_id=drone_id_counter,
                customer_sequence=task_info['customers'],
                launch_point=launch_point,
                recovery_point=task_info['recovery_point'],
                total_energy=0.0
            )
            drone_tasks_list = [drone_task]
            print(f"      drone_id: {drone_task.drone_id}")
            print(f"      customer_sequence: {drone_task.customer_sequence}")
            print(f"      launch_point: {drone_task.launch_point}")
            print(f"      recovery_point: {drone_task.recovery_point}")
            print(f"      total_energy: {drone_task.total_energy}")
            drone_id_counter += 1
        else:
            drone_tasks_list = []
            print(f"    → 无客户，创建空任务列表")

        print()
        print("代码行648-651：构建扩展格式")
        print("  drone_tasks_extended[launch_point] = {")
        print("      'drone_tasks': drone_tasks_list,")
        print("      'recovery_point': task_info['recovery_point']")
        print("  }")

        drone_tasks_extended[launch_point] = {
            'drone_tasks': drone_tasks_list,
            'recovery_point': task_info['recovery_point']
        }
        print(f"    → 添加到扩展格式: {drone_tasks_extended[launch_point]}")
        print()

    return drone_tasks_extended

def step6_final_assembly():
    """第6步：最终结果组装"""
    print("=" * 80)
    print("第6步：最终结果组装")
    print("=" * 80)

    truck_route = [0, 6, 12, 0]
    drone_tasks_extended = {
        6: {
            'drone_tasks': [DroneTask(1, [4, 7], 6, 12, 0.0)],
            'recovery_point': 12
        }
    }
    decoded_solution = []

    print("代码行653：")
    print("  decoded_solution.append((truck_route, drone_tasks_extended))")
    print()
    print("逻辑解释：")
    print("  - 将卡车路径和扩展无人机任务组成元组")
    print("  - 添加到解码结果列表中")
    print()

    decoded_solution.append((truck_route, drone_tasks_extended))

    print("执行结果：")
    print(f"  truck_route = {truck_route}")
    print(f"  drone_tasks_extended = {drone_tasks_extended}")
    print(f"  decoded_solution = [")
    print(f"    ({truck_route}, {drone_tasks_extended})")
    print(f"  ]")
    print()

    print("代码行655：")
    print("  return decoded_solution")
    print()
    print("最终返回格式：")
    print("  [")
    print("    (truck_route, drone_tasks_extended),")
    print("    ...")
    print("  ]")
    print()

    return decoded_solution

def complete_example_walkthrough():
    """完整示例演练"""
    print("=" * 80)
    print("完整示例演练")
    print("=" * 80)

    chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])

    print("输入:")
    print(f"  chromosomes = {chromosomes}")
    print()

    print("调用函数:")
    result = Individual._decode_extended(chromosomes)

    print("输出:")
    print(f"  result = {result}")
    print()

    print("结果解析:")
    if result:
        truck_route, drone_tasks_extended = result[0]
        print(f"  卡车路径: {truck_route}")
        print(f"  无人机任务:")
        for launch_point, task_info in drone_tasks_extended.items():
            print(f"    发射点{launch_point}:")
            print(f"      回收点: {task_info['recovery_point']}")
            print(f"      任务数量: {len(task_info['drone_tasks'])}")
            for i, drone_task in enumerate(task_info['drone_tasks']):
                print(f"      任务{i+1}: DroneTask(id={drone_task.drone_id}, customers={drone_task.customer_sequence})")

if __name__ == "__main__":
    explain_decode_extended_step_by_step()
    step1_input_processing()
    step2_route_segmentation()
    step3_segment_decoding()
    truck_route, active_drone_tasks = step4_node_processing()
    drone_tasks_extended = step5_drone_task_creation()
    decoded_solution = step6_final_assembly()
    complete_example_walkthrough()
