#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异步VRP-D编码测试脚本
测试扩展编码方案的正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_loader import Problem, Individual
from utils import calculate_total_cost
import numpy as np

def demonstrate_drone_tasks_formats():
    """演示drone_tasks的多种存储格式"""
    print("=" * 60)
    print("drone_tasks 存储格式演示 - 支持多客户服务链")
    print("=" * 60)

    from data_loader import DroneTask

    # 场景：2辆卡车，无人机可以服务多个客户
    routes = [
        [0, 6, 12, 16, 0],      # 卡车1
        [0, 3, 9, 15, 22, 0]    # 卡车2
    ]

    print("配送场景:")
    print("  卡车1路径: [0, 6, 12, 16, 0]")
    print("  卡车2路径: [0, 3, 9, 15, 22, 0]")
    print("  无人机任务: 每架无人机可以服务多个客户")
    print()

    # 1. 传统格式（不支持多客户链）
    print("1. 传统格式 (不支持多客户服务链):")
    drone_tasks_traditional = {
        6: [4, 8, 11],        # 发射点6，但不知道是1架还是3架无人机
        15: [14, 17, 20]      # 发射点15，客户分配不明确
    }
    print("drone_tasks_traditional = {")
    for launch_point, customers in drone_tasks_traditional.items():
        print(f"    {launch_point}: {customers},  # 发射点{launch_point}，但不知道无人机如何分配客户")
    print("}")
    print("问题: 无法表示'无人机1服务客户4→8→11'这种多客户链！")
    print()

    # 2. 多客户服务链格式
    print("2. 多客户服务链格式 (推荐):")
    drone_tasks_multi_customer = {
        6: {  # 发射点6
            'drone_tasks': [
                DroneTask(
                    drone_id=1,
                    customer_sequence=[4, 8, 11],     # 无人机1服务3个客户
                    launch_point=6,
                    recovery_point=12,
                    total_energy=180.5
                ),
                DroneTask(
                    drone_id=2,
                    customer_sequence=[7],            # 无人机2服务1个客户
                    launch_point=6,
                    recovery_point=12,
                    total_energy=95.3
                )
            ],
            'recovery_point': 12
        },
        15: {  # 发射点15
            'drone_tasks': [
                DroneTask(
                    drone_id=3,
                    customer_sequence=[14, 17, 20, 25],  # 无人机3服务4个客户
                    launch_point=15,
                    recovery_point=22,
                    total_energy=220.7
                )
            ],
            'recovery_point': 22
        }
    }

    print("drone_tasks_multi_customer = {")
    for launch_point, task_info in drone_tasks_multi_customer.items():
        print(f"    {launch_point}: {{")
        print(f"        'drone_tasks': [")
        for task in task_info['drone_tasks']:
            route_seq = task.get_route_sequence()
            print(f"            # 无人机{task.drone_id}: {' → '.join(map(str, route_seq))}")
            print(f"            DroneTask(id={task.drone_id}, 客户序列={task.customer_sequence}, 发射={task.launch_point}, 回收={task.recovery_point}, 总能耗={task.total_energy}),")
        print(f"        ],")
        print(f"        'recovery_point': {task_info['recovery_point']}")
        print(f"    }},")
    print("}")
    print("优势: 完美支持'一架无人机服务多个客户'的场景！")
    print()

    # 3. 展示飞行路径
    print("3. 无人机飞行路径详解:")
    for launch_point, task_info in drone_tasks_multi_customer.items():
        print(f"  发射点{launch_point}:")
        for task in task_info['drone_tasks']:
            route = task.get_route_sequence()
            print(f"    无人机{task.drone_id}: {' → '.join(map(str, route))} (服务{task.num_customers}个客户)")
    print()

    return routes, drone_tasks_traditional, drone_tasks_multi_customer

def test_new_encoding_system():
    """测试新的编码解码系统"""
    print("=" * 60)
    print("新的异步VRP-D编码解码系统测试")
    print("=" * 60)

    # 加载测试数据集
    problem = Problem("Vrp-Set-Solomon/A-n32-k2-d4.vrp")
    print(f"数据集: {problem.name}")
    print(f"节点数: {problem.num_customers + 1}")
    print(f"卡车数: {problem.num_vehicles}")
    print(f"无人机数: {problem.num_drones}")
    print()

    # 创建测试路径
    test_routes = [
        [0, 6, 12, 16, 0],      # 卡车1
        [0, 3, 9, 15, 22, 0]    # 卡车2
    ]

    # 创建完整的无人机任务信息（确保发射点和回收点都在对应的卡车路径中）
    from data_loader import DroneTask

    test_drone_tasks_extended = {
        6: {  # 发射点6（卡车1路径中）
            'drone_tasks': [
                DroneTask(drone_id=1, customer_sequence=[4], launch_point=6, recovery_point=12, total_energy=120.5),
                DroneTask(drone_id=2, customer_sequence=[8], launch_point=6, recovery_point=12, total_energy=98.3)
            ],
            'recovery_point': 12  # 回收点改为12（在卡车1路径中）
        },
        15: {  # 发射点15（卡车2路径中）
            'drone_tasks': [
                DroneTask(drone_id=3, customer_sequence=[14], launch_point=15, recovery_point=22, total_energy=156.7),
                DroneTask(drone_id=4, customer_sequence=[17], launch_point=15, recovery_point=22, total_energy=134.2)
            ],
            'recovery_point': 22  # 回收点22（在卡车2路径中）
        }
    }
    
    print("输入路径:")
    for i, route in enumerate(test_routes):
        print(f"  卡车{i+1}: {route}")
    print()

    print("输入无人机任务:")
    for launch_point, task_info in test_drone_tasks_extended.items():
        print(f"  发射点{launch_point} → 回收点{task_info['recovery_point']}")
        for drone_task in task_info['drone_tasks']:
            print(f"    无人机{drone_task.drone_id}: 客户{drone_task.customer_id}, 能耗{drone_task.energy_consumption}")
    print()

    # 使用扩展编码函数
    print("执行扩展编码...")
    chromosomes = Individual._encode_extended(test_routes, test_drone_tasks_extended, problem)

    print("编码结果:")
    path_layer, service_layer = chromosomes
    print(f"  路径层: {path_layer}")
    print(f"  服务层: {service_layer}")
    print(f"  路径长度: {len(path_layer)}, 服务层长度: {len(service_layer)}")
    print()
    
    # 服务方式解释
    service_meanings = {0: "卡车服务", 1: "无人机客户", 2: "发射点", 3: "回收点", 4: "回收+发射"}
    print("服务方式解释:")
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        print(f"  位置{i}: 节点{node} - {service_meanings.get(service, '未知')}")
    print()
    
    # 解码验证
    print("执行新的解码...")

    # 先手动分析路径分割
    print("路径分割分析:")
    depot_indices = [i for i, node in enumerate(path_layer) if node == 0]
    print(f"  配送中心位置: {depot_indices}")

    decoded_solution = Individual.decode_extended(chromosomes, problem)

    print("解码结果:")
    for i, (truck_route, drone_tasks_extended) in enumerate(decoded_solution):
        print(f"  卡车{i+1}路径: {truck_route}")
        print(f"  无人机任务扩展信息:")

        # 详细分析无人机任务
        if drone_tasks_extended:
            for launch_point, task_info in drone_tasks_extended.items():
                recovery_point = task_info['recovery_point']
                drone_tasks_list = task_info['drone_tasks']
                print(f"    发射点{launch_point} → 回收点{recovery_point}")
                for drone_task in drone_tasks_list:
                    print(f"      无人机{drone_task.drone_id}: 客户{drone_task.customer_id}")
        else:
            print(f"    无无人机任务")
    print()
    
    # 成本计算 - 需要转换为传统格式
    print("成本计算:")
    all_routes = [solution[0] for solution in decoded_solution]
    all_drone_tasks = {}

    # 转换扩展格式为传统格式
    for _, drone_tasks_extended in decoded_solution:
        for launch_point, task_info in drone_tasks_extended.items():
            customer_list = [task.customer_id for task in task_info['drone_tasks']]
            all_drone_tasks[launch_point] = customer_list
    
    try:
        cost_breakdown = calculate_total_cost(problem, all_routes, all_drone_tasks, return_breakdown=True)
        
        print(f"  总成本: {cost_breakdown['total_cost']:.2f}元")
        print(f"  卡车成本: {cost_breakdown['vehicle_cost']:.2f}元")
        print(f"  无人机成本: {cost_breakdown['drone_cost']:.2f}元")
        print(f"  等待成本: {cost_breakdown['waiting_cost']:.2f}元")
        print(f"  时间成本: {cost_breakdown['time_cost']:.2f}元")
        print(f"  系统运行时间: {cost_breakdown['operation_time_hours']:.2f}小时")
        print()
        
        # 分析异步模式优势
        print("异步模式分析:")
        total_drone_customers = sum(len(targets) for targets in all_drone_tasks.values())
        print(f"  无人机服务客户数: {total_drone_customers}")
        print(f"  平均任务链长度: {total_drone_customers/len(all_drone_tasks):.1f}")
        print(f"  等待成本占比: {cost_breakdown['waiting_cost']/cost_breakdown['total_cost']*100:.1f}%")
        
    except Exception as e:
        print(f"  成本计算出错: {e}")
        print("  这可能是由于新的成本计算函数需要进一步调试")
    
    print()
    print("测试完成！")
    return True

if __name__ == "__main__":
    # 首先演示两种格式
    demonstrate_drone_tasks_formats()
    print("\n" + "="*60 + "\n")

    # 然后测试编码解码系统
    test_new_encoding_system()
