#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异步VRP-D编码测试脚本
测试扩展编码方案的正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_loader import Problem, Individual
from utils import calculate_total_cost
import numpy as np

def test_async_encoding():
    """测试异步编码方案"""
    print("=" * 60)
    print("异步VRP-D编码测试")
    print("=" * 60)
    
    # 加载测试数据集
    problem = Problem("Vrp-Set-Solomon/A-n32-k2-d4.vrp")
    print(f"数据集: {problem.name}")
    print(f"节点数: {problem.num_customers + 1}")
    print(f"卡车数: {problem.num_vehicles}")
    print(f"无人机数: {problem.num_drones}")
    print()
    
    # 创建测试路径
    test_routes = [
        [0, 6, 12, 16, 0],      # 卡车1
        [0, 3, 9, 15, 22, 0]    # 卡车2
    ]
    test_drone_tasks = {}  # 空的无人机任务，将通过编码生成
    
    print("输入路径:")
    for i, route in enumerate(test_routes):
        print(f"  卡车{i+1}: {route}")
    print()
    
    # 创建Individual并进行扩展编码
    individual = Individual()
    individual.routes = test_routes
    individual.drone_tasks = test_drone_tasks
    
    print("执行扩展编码...")
    chromosomes = individual._encode_extended(test_routes, test_drone_tasks, problem)

    print("编码结果:")
    path_layer, service_layer = chromosomes
    print(f"  路径层: {path_layer}")
    print(f"  服务层: {service_layer}")
    print(f"  路径长度: {len(path_layer)}, 服务层长度: {len(service_layer)}")
    print()
    
    # 服务方式解释
    service_meanings = {0: "卡车服务", 1: "无人机客户", 2: "发射点", 3: "回收点", 4: "回收+发射"}
    print("服务方式解释:")
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        print(f"  位置{i}: 节点{node} - {service_meanings.get(service, '未知')}")
    print()
    
    # 解码验证
    print("执行解码...")

    # 先手动分析路径分割
    print("路径分割分析:")
    depot_indices = [i for i, node in enumerate(path_layer) if node == 0]
    print(f"  配送中心位置: {depot_indices}")

    decoded_solution = Individual.decode(chromosomes, problem)

    print("解码结果:")
    for i, (truck_route, drone_tasks) in enumerate(decoded_solution):
        print(f"  卡车{i+1}路径: {truck_route}")
        print(f"  无人机任务: {drone_tasks}")

        # 详细分析无人机任务
        if drone_tasks:
            for launch_point, targets in drone_tasks.items():
                if len(targets) > 1:
                    print(f"    异步任务链: 发射点{launch_point} → 客户{targets}")
                else:
                    print(f"    传统任务: 发射点{launch_point} → 客户{targets[0]}")
        else:
            print(f"    无无人机任务")
    print()
    
    # 成本计算
    print("成本计算:")
    all_routes = [solution[0] for solution in decoded_solution]
    all_drone_tasks = {}
    for _, drone_tasks in decoded_solution:
        all_drone_tasks.update(drone_tasks)
    
    try:
        cost_breakdown = calculate_total_cost(problem, all_routes, all_drone_tasks, return_breakdown=True)
        
        print(f"  总成本: {cost_breakdown['total_cost']:.2f}元")
        print(f"  卡车成本: {cost_breakdown['vehicle_cost']:.2f}元")
        print(f"  无人机成本: {cost_breakdown['drone_cost']:.2f}元")
        print(f"  等待成本: {cost_breakdown['waiting_cost']:.2f}元")
        print(f"  时间成本: {cost_breakdown['time_cost']:.2f}元")
        print(f"  系统运行时间: {cost_breakdown['operation_time_hours']:.2f}小时")
        print()
        
        # 分析异步模式优势
        print("异步模式分析:")
        total_drone_customers = sum(len(targets) for targets in all_drone_tasks.values())
        print(f"  无人机服务客户数: {total_drone_customers}")
        print(f"  平均任务链长度: {total_drone_customers/len(all_drone_tasks):.1f}")
        print(f"  等待成本占比: {cost_breakdown['waiting_cost']/cost_breakdown['total_cost']*100:.1f}%")
        
    except Exception as e:
        print(f"  成本计算出错: {e}")
        print("  这可能是由于新的成本计算函数需要进一步调试")
    
    print()
    print("测试完成！")
    return True

if __name__ == "__main__":
    test_async_encoding()
