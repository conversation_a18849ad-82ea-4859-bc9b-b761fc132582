/福建理工/毕业论文/简化模型/DE_改进/read_test.py                                                  正在读取文件: results_A-n80-k2-d4.vrp_10runs.pkl...
成功读取了10次运行的结果。

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 726.13
最小总成本: 680.70 (运行 3)
最大总成本: 807.72 (运行 1)
总成本标准差: 33.13

========== 算法精度与稳定性分析 ==========
最大偏差: 127.02 (18.66%)
平均偏差: 24.61 (3.62%)
平均求解时间: 169.50秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |         11 | 0.001238 |   807.72 |        0 | 170.51秒   |
+----------+------------+----------+----------+----------+------------+
|        2 |         12 | 0.001391 |   718.77 |        0 | 179.74秒   |
+----------+------------+----------+----------+----------+------------+
|        3 |         13 | 0.001469 |   680.7  |        0 | 166.08秒   |
+----------+------------+----------+----------+----------+------------+
|        4 |         14 | 0.00138  |   724.53 |        0 | 177.35秒   |
+----------+------------+----------+----------+----------+------------+
|        5 |         15 | 0.001351 |   739.97 |        0 | 169.42秒   |
+----------+------------+----------+----------+----------+------------+
|        6 |         16 | 0.001405 |   711.96 |        0 | 158.69秒   |
+----------+------------+----------+----------+----------+------------+
|        7 |         17 | 0.001327 |   753.77 |        0 | 165.93秒   |
+----------+------------+----------+----------+----------+------------+
|        8 |         18 | 0.001421 |   703.88 |        0 | 169.05秒   |
+----------+------------+----------+----------+----------+------------+
|        9 |         19 | 0.001416 |   706.41 |        0 | 163.78秒   |
+----------+------------+----------+----------+----------+------------+
|       10 |         20 | 0.001401 |   713.59 |        0 | 174.44秒   |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 3) ==========
适应度: 0.001469
总成本: 680.70
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 42, 67, 66, 70, 58, 38, 50, 32, 4, 22, 45, 72, 76, 64, 33, 54, 9, 15, 55, 41, 25, 46
, 60, 39, 31, 3, 77, 51, 53, 36, 73, 0, 13, 21, 29, 17, 74, 5, 44, 23, 62, 12, 7, 63, 14, 28, 34, 52, 79, 48, 18, 71, 11, 49, 0, 40, 2, 37, 8, 68, 78, 47, 75, 20, 57, 43, 61, 16, 65, 35, 19, 26, 69, 56, 59, 27, 30, 6, 24, 10, 1, 0)                                                                 服务方式层: (0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0,
 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0)                               
染色体解码结果:
  车辆 1 路径: [0, 42, 67, 66, 70, 58, 50, 32, 4, 22, 72, 64, 33, 15, 41, 25, 46, 60, 3, 77, 53, 3
6, 73, 0]                                                                                             无人机任务:
      发射点 58 → 任务: [38]
      发射点 22 → 任务: [45]
      发射点 72 → 任务: [76]
      发射点 33 → 任务: [54, 9]
      发射点 15 → 任务: [55]
      发射点 60 → 任务: [39, 31]
      发射点 77 → 任务: [51]
  车辆 2 路径: [0, 13, 21, 29, 5, 44, 23, 62, 63, 14, 28, 79, 48, 11, 49, 0]
    无人机任务:
      发射点 29 → 任务: [17, 74]
      发射点 62 → 任务: [12, 7]
      发射点 28 → 任务: [34, 52]
      发射点 48 → 任务: [18, 71]
  车辆 3 路径: [0, 40, 2, 37, 8, 68, 47, 75, 57, 61, 16, 65, 19, 26, 59, 30, 6, 24, 10, 1, 0]     
    无人机任务:
      发射点 68 → 任务: [78]
      发射点 75 → 任务: [20]
      发射点 57 → 任务: [43]
      发射点 65 → 任务: [35]
      发射点 26 → 任务: [69, 56]
      发射点 59 → 任务: [27]

程序执行完毕。
