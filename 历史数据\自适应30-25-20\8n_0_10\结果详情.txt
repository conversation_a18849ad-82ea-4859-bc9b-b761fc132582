80n_1_10
30\25\20

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 747.74
最小总成本: 659.08 (运行 1)
最大总成本: 826.03 (运行 6)
总成本标准差: 50.51

========== 算法精度与稳定性分析 ==========
最大偏差: 166.95 (25.33%)
平均偏差: 42.02 (6.38%)
平均求解时间: 186.32秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 | 0.001517 |   659.08 |        0 | 186.19秒   |
+----------+------------+----------+----------+----------+------------+
|        2 |          2 | 0.001396 |   716.35 |        0 | 175.39秒   |
+----------+------------+----------+----------+----------+------------+
|        3 |          3 | 0.001388 |   720.39 |        0 | 183.95秒   |
+----------+------------+----------+----------+----------+------------+
|        4 |          4 | 0.001225 |   816.14 |        0 | 192.94秒   |
+----------+------------+----------+----------+----------+------------+
|        5 |          5 | 0.001272 |   786.05 |        0 | 202.60秒   |
+----------+------------+----------+----------+----------+------------+
|        6 |          6 | 0.001211 |   826.03 |        0 | 188.92秒   |
+----------+------------+----------+----------+----------+------------+
|        7 |          7 | 0.001349 |   741.35 |        0 | 186.51秒   |
+----------+------------+----------+----------+----------+------------+
|        8 |          8 | 0.001446 |   691.46 |        0 | 172.86秒   |
+----------+------------+----------+----------+----------+------------+
|        9 |          9 | 0.001302 |   767.91 |        0 | 172.88秒   |
+----------+------------+----------+----------+----------+------------+
|       10 |         10 | 0.001329 |   752.69 |        0 | 200.94秒   |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 1) ==========
适应度: 0.001517
总成本: 659.08
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 42, 51, 77, 39, 46, 41, 25, 61, 57, 20, 19, 75, 26, 47, 69, 35, 65, 56, 55, 33, 9, 15, 54, 72, 50, 45, 22, 4, 32, 58, 38, 76, 53, 67, 70, 36, 66, 0, 49, 73,
 3, 60, 31, 64, 29, 6, 23, 12, 44, 62, 63, 11, 52, 28, 48, 18, 14, 71, 10, 7, 1, 0, 40, 21, 5, 59, 27, 30, 24, 34, 79, 37, 8, 43, 16, 2, 78, 68, 17, 74, 13, 0)             服务方式层: (0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0,
 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0)                                                                             
染色体解码结果:
  车辆 1 路径: [0, 42, 51, 77, 39, 46, 41, 25, 61, 19, 47, 65, 56, 55, 15, 54, 72, 50, 45, 22, 4, 32, 58, 76, 53, 67, 66, 0]
    无人机任务:
      发射点 61 → 任务: [57, 20]
      发射点 19 → 任务: [75, 26]
      发射点 47 → 任务: [69, 35]
      发射点 55 → 任务: [33, 9]
      发射点 58 → 任务: [38]
      发射点 67 → 任务: [70, 36]
  车辆 2 路径: [0, 49, 73, 3, 60, 31, 6, 23, 12, 44, 62, 63, 11, 52, 28, 48, 14, 71, 10, 7, 1, 0]
    无人机任务:
      发射点 31 → 任务: [64, 29]
      发射点 48 → 任务: [18]
  车辆 3 路径: [0, 40, 21, 5, 59, 27, 30, 24, 34, 79, 37, 8, 2, 78, 17, 74, 13, 0]
    无人机任务:
      发射点 8 → 任务: [43, 16]
      发射点 78 → 任务: [68]

程序执行完毕。
