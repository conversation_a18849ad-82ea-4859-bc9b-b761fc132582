# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成

# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)

if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.6:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期

========== RC101-81-1 数据集求解结果 ==========

算法配置:
数据集: RC_随机+聚类_数据集/RC101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1165.40
最小总成本: 1080.39 (运行 4)
最大总成本: 1240.74 (运行 8)
总成本标准差: 48.54

========== 算法精度与稳定性分析 ==========
最大偏差: 160.35 (14.84%)
平均偏差: 85.01 (7.87%)
平均求解时间: 303.24秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000853 | 1172.24 |    0.00 | 302.14
     2 |        2 | 0.000831 | 1203.20 |    0.00 | 302.22
     3 |        3 | 0.000832 | 1202.48 |    0.00 | 302.92
     4 |        4 | 0.000926 | 1080.39 |    0.00 | 302.29
     5 |        5 | 0.000887 | 1127.73 |    0.00 | 302.10
     6 |        6 | 0.000896 | 1116.60 |    0.00 | 302.16
     7 |        7 | 0.000829 | 1201.83 |    4.07 | 302.00
     8 |        8 | 0.000806 | 1240.74 |    0.00 | 303.49
     9 |        9 | 0.000841 | 1189.36 |    0.00 | 302.11
    10 |       10 | 0.000893 | 1119.45 |    0.00 | 311.01

最佳解详细信息:
运行ID: 4
适应度: 0.000926
总成本: 1080.39
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 72, 70, 47, 78, 60, 46, 59, 64, 77, 57, 29, 30, 31, 32, 33, 36, 34, 35, 49, 0, 43, 80, 1, 2, 4, 5, 3, 37, 7, 6, 63, 62, 48, 58, 13, 11, 12, 38, 10, 9, 8, 41, 79, 55, 0, 53, 54, 66, 45, 69, 19, 17, 14, 39, 15, 18, 16, 0, 73, 44, 74, 76, 67, 52, 61, 68, 71, 51, 27, 20, 22, 25, 21, 24, 28, 23, 26, 40, 50, 75, 56, 42, 65, 0)
染色体 2: (0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 72, 70, 47, 78, 60, 46, 59, 64, 77, 57, 29, 31, 32, 36, 34, 49, 0]
  无人机任务:
    从节点 29 发射无人机访问: [30]
    从节点 32 发射无人机访问: [33]
    从节点 34 发射无人机访问: [35]
路线 2: [0, 43, 80, 1, 2, 3, 6, 63, 62, 13, 38, 9, 8, 41, 79, 55, 0]
  无人机任务:
    从节点 2 发射无人机访问: [4, 5]
    从节点 3 发射无人机访问: [37, 7]
    从节点 62 发射无人机访问: [48, 58]
    从节点 13 发射无人机访问: [11, 12]
    从节点 38 发射无人机访问: [10]
路线 3: [0, 53, 66, 45, 69, 19, 17, 14, 39, 15, 0]
  无人机任务:
    从节点 53 发射无人机访问: [54]
    从节点 15 发射无人机访问: [18, 16]
路线 4: [0, 73, 44, 67, 68, 27, 20, 22, 24, 26, 40, 75, 42, 65, 0]
  无人机任务:
    从节点 44 发射无人机访问: [74, 76]
    从节点 67 发射无人机访问: [52, 61]
    从节点 68 发射无人机访问: [71, 51]
    从节点 22 发射无人机访问: [25, 21]
    从节点 24 发射无人机访问: [28, 23]
    从节点 40 发射无人机访问: [50]
    从节点 75 发射无人机访问: [56]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000438 | 2284.71 |    0.00
  10 | 0.000438 | 2282.91 |    0.00
  20 | 0.000438 | 2282.91 |    0.00
  30 | 0.000444 | 2237.65 |   12.72
  40 | 0.000444 | 2237.65 |   12.72
  50 | 0.000447 | 2235.62 |    0.00
  60 | 0.000492 | 2034.28 |    0.00
  70 | 0.000492 | 2034.28 |    0.00
  80 | 0.000492 | 2034.28 |    0.00
  90 | 0.000492 | 2034.28 |    0.00
 100 | 0.000513 | 1948.88 |    0.00
 110 | 0.000519 | 1928.07 |    0.00
 120 | 0.000537 | 1863.13 |    0.00
 130 | 0.000572 | 1748.58 |    0.00
 140 | 0.000574 | 1743.16 |    0.00
 150 | 0.000575 | 1739.87 |    0.00
 160 | 0.000585 | 1709.95 |    0.00
 170 | 0.000589 | 1697.05 |    0.00
 180 | 0.000601 | 1664.10 |    0.00
 190 | 0.000641 | 1558.94 |    0.00
 200 | 0.000675 | 1481.26 |    0.00
 210 | 0.000691 | 1447.00 |    0.00
 220 | 0.000691 | 1447.00 |    0.00
 230 | 0.000744 | 1343.85 |    0.00
 240 | 0.000749 | 1335.50 |    0.00
 250 | 0.000751 | 1331.88 |    0.00
 260 | 0.000751 | 1330.95 |    0.00
 270 | 0.000751 | 1330.95 |    0.00
 280 | 0.000751 | 1330.95 |    0.00
 290 | 0.000751 | 1330.95 |    0.00
 300 | 0.000751 | 1330.95 |    0.00
 310 | 0.000751 | 1330.95 |    0.00
 320 | 0.000768 | 1302.41 |    0.00
 330 | 0.000768 | 1302.41 |    0.00
 340 | 0.000815 | 1227.38 |    0.00
 350 | 0.000816 | 1225.72 |    0.00
 360 | 0.000816 | 1225.66 |    0.00
 370 | 0.000817 | 1224.34 |    0.00
 380 | 0.000817 | 1224.34 |    0.00
 390 | 0.000817 | 1224.34 |    0.00
 400 | 0.000836 | 1195.76 |    0.00
 410 | 0.000836 | 1195.76 |    0.00
 420 | 0.000922 | 1085.04 |    0.00
 430 | 0.000922 | 1085.04 |    0.00
 440 | 0.000922 | 1085.04 |    0.00
