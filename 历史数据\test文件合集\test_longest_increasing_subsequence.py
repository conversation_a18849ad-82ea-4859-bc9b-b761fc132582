import unittest
from operators import longest_increasing_subsequence

class TestLongestIncreasingSubsequence(unittest.TestCase):
    """测试最长递增子序列算法的单元测试类"""
    
    def test_given_example(self):
        """测试指定的示例数组 [2,7,11,12,6,1,3,8,5,13,4,9,14,10]"""
        arr = [2, 11, 7, 12, 6, 1, 3, 8, 5, 13, 4, 9, 14, 10]
        result = longest_increasing_subsequence(arr)
        
        # 验证结果是否为列表
        self.assertIsInstance(result, list)
        
        # 验证索引是否在有效范围内
        self.assertTrue(all(0 <= idx < len(arr) for idx in result))
        
        # 验证这些索引对应的值是否真的形成递增子序列
        values = [arr[idx] for idx in result]
        self.assertTrue(all(values[i] < values[i+1] for i in range(len(values)-1)))
        
        # 输出结果便于分析
        print("\n=== 测试指定示例数组 ===")
        print(f"原始数组: {arr}")
        print(f"最长递增子序列索引: {result}")
        print(f"对应的值: {[arr[idx] for idx in result]}")
        print(f"LIS长度: {len(result)}")
        
        # 已知此样例的最长递增子序列长度为6
        self.assertEqual(len(result), 6)
    
    def test_verify_lis_correctness(self):
        """验证LIS结果的正确性 - 确保是真正的递增序列且是最长的"""
        arr = [2, 11, 7, 12, 6, 1, 3, 8, 5, 13, 4, 9, 14, 10]
        result = longest_increasing_subsequence(arr)
        values = [arr[idx] for idx in result]
        
        # 验证是否为严格递增序列
        self.assertTrue(all(values[i] < values[i+1] for i in range(len(values)-1)))
        
        # 验证结果是否包含最优解中的某一个
        # 一个可能的LIS是[2,7,11,12,13,14]，索引为[0,1,2,3,9,12]
        optimal_length = 6
        self.assertEqual(len(result), optimal_length)
        
        # 打印找到的LIS与预期的比较
        print("\n=== 验证LIS正确性 ===")
        print(f"找到的LIS: {values}")
        print(f"一个可能的最优解: [2,7,11,12,13,14]")
    
    def test_empty_array(self):
        """测试空数组"""
        self.assertEqual(longest_increasing_subsequence([]), [])
    
    def test_sorted_array(self):
        """测试已排序数组"""
        arr = [1, 2, 3, 4, 5]
        result = longest_increasing_subsequence(arr)
        self.assertEqual(len(result), len(arr))
        self.assertEqual([arr[idx] for idx in result], arr)
    
    def test_reverse_sorted_array(self):
        """测试逆序数组"""
        arr = [5, 4, 3, 2, 1]
        result = longest_increasing_subsequence(arr)
        self.assertEqual(len(result), 1)  # 最长递增子序列长度为1
    
    def test_array_with_duplicates(self):
        """测试包含重复元素的数组"""
        arr = [3, 3, 3, 3]
        result = longest_increasing_subsequence(arr)
        self.assertEqual(len(result), 1)  # 最长递增子序列长度为1
    
    def test_manual_calculation(self):
        """手动检验复杂示例"""
        arr = [2, 11, 7, 12, 6, 1, 3, 8, 5, 13, 4, 9, 14, 10]
        result = longest_increasing_subsequence(arr)
        values = [arr[idx] for idx in result]
        
        print("\n=== 手动验证计算步骤 ===")
        # 分析DP数组值
        n = len(arr)
        dp = [1] * n
        prev = [-1] * n
        
        for i in range(1, n):
            for j in range(i):
                if arr[i] > arr[j] and dp[i] < dp[j] + 1:
                    dp[i] = dp[j] + 1
                    prev[i] = j
        
        print("DP数组:", dp)
        print("前驱数组:", prev)
        print("计算得到的最长LIS长度:", max(dp))
        
        # 验证函数结果与手动计算一致
        self.assertEqual(len(result), max(dp))

if __name__ == '__main__':
    unittest.main()