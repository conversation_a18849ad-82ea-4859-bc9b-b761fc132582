#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的解码函数输入验证
"""

from data_loader import Individual

def test_fixed_validation():
    """测试修复后的输入验证"""
    print("=" * 80)
    print("测试修复后的解码函数输入验证")
    print("=" * 80)
    
    # 测试情况1: 正常的元组输入
    print("测试1: 正常的元组输入")
    try:
        chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况2: Individual对象输入
    print("测试2: Individual对象输入")
    try:
        chromosomes_tuple = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        individual = Individual(chromosomes=chromosomes_tuple)  # 修复：使用关键字参数
        result = Individual._decode_extended(individual)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况3: 错误的输入类型 - 单个列表
    print("测试3: 错误输入 - 单个列表")
    try:
        chromosomes = [0, 6, 4, 7, 12, 0]  # 只有一个列表，不是元组
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况4: 错误的输入类型 - 字符串
    print("测试4: 错误输入 - 字符串")
    try:
        chromosomes = "invalid_input"
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况5: 错误的输入类型 - None
    print("测试5: 错误输入 - None")
    try:
        chromosomes = None
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况6: 有chromosomes属性但值为None的对象
    print("测试6: 有chromosomes属性但值为None的对象")
    try:
        class FakeIndividual:
            def __init__(self):
                self.chromosomes = None
        
        fake_obj = FakeIndividual()
        result = Individual._decode_extended(fake_obj)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况7: 有chromosomes属性但不是元组的对象
    print("测试7: 有chromosomes属性但不是元组的对象")
    try:
        class FakeIndividual2:
            def __init__(self):
                self.chromosomes = [1, 2, 3]  # 不是元组
        
        fake_obj = FakeIndividual2()
        result = Individual._decode_extended(fake_obj)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况8: 元组但不是两个元素
    print("测试8: 元组但不是两个元素")
    try:
        chromosomes = ([0, 6, 4], [0, 2, 1], [1, 2, 3])  # 三个元素
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况9: 元组但包含非列表元素
    print("测试9: 元组但包含非列表元素")
    try:
        chromosomes = ("not_a_list", [0, 2, 1, 1, 3, 0])
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()

def test_traditional_validation():
    """测试传统解码函数的输入验证"""
    print("=" * 80)
    print("测试传统解码函数的输入验证")
    print("=" * 80)
    
    # 测试正常输入
    print("测试1: 正常输入")
    try:
        chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        result = Individual._decode_traditional(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试错误输入
    print("测试2: 错误输入 - None")
    try:
        chromosomes = None
        result = Individual._decode_traditional(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()

def show_validation_summary():
    """显示验证总结"""
    print("=" * 80)
    print("输入验证修复总结")
    print("=" * 80)
    
    print("✅ 已修复的问题:")
    print("1. None输入检查")
    print("2. Individual对象的chromosomes属性验证")
    print("3. 元组/列表格式验证")
    print("4. 元素数量验证（必须是2个）")
    print("5. path_layer和service_layer类型验证（必须是列表）")
    print("6. 提供清晰的错误信息")
    print()
    
    print("🔧 修复后的验证流程:")
    print("1. 检查输入是否为None")
    print("2. 提取染色体数据（Individual对象或直接输入）")
    print("3. 验证数据格式（元组/列表，包含2个元素）")
    print("4. 安全解包染色体数据")
    print("5. 验证path_layer和service_layer是否为列表")
    print("6. 验证两个层的长度是否一致")
    print()
    
    print("💡 现在函数更加健壮，能够:")
    print("- 提供清晰的错误信息")
    print("- 防止运行时崩溃")
    print("- 支持多种输入格式验证")
    print("- 保持向后兼容性")

if __name__ == "__main__":
    test_fixed_validation()
    test_traditional_validation()
    show_validation_summary()
