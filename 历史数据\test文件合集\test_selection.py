import random
import numpy as np
from data_loader import Problem, Individual
from initialization import initialize_population
from operators import selection, select_parents
from utils import evaluate_individual_with_penalty, update_population_fitness

# 设置随机种子以确保结果可重现
random.seed(42)
np.random.seed(42)

def test_selection():
    # 创建Problem对象并加载数据
    problem = Problem("A-n32-k2-d4.vrp")
    
    # 使用initialize_population函数创建规模为10的初始种群
    print("正在生成初始种群...")
    population = initialize_population(problem, 10)
    
    # 评估种群中所有个体的适应度
    current_gen = 0
    max_gen = 100
    print("评估种群适应度...")
    update_population_fitness(problem, population, current_gen, max_gen)
    
    # 输出种群信息
    print("\n初始种群信息:")
    for i, ind in enumerate(population):
        print(f"个体 {i+1}: 适应度 = {ind.fitness:.6f}, 惩罚值 = {ind.penalty:.2f}")
    
    # 应用selection函数
    print("\n执行轮盘赌选择...")
    # selected_individual = selection(population, current_gen, max_gen)
    parent1, parent2 = select_parents(population, current_gen, max_gen)

    # 输出selected_individual的完整信息
    # print("\n被选中个体的详细信息:")
    # print(f"适应度值: {selected_individual.fitness:.6f}")
    # print(f"惩罚值: {selected_individual.penalty:.2f}")
    # print(f"路径层长度: {len(selected_individual.chromosomes[0])}")
    # print(f"服务方式层长度: {len(selected_individual.chromosomes[1])}")
    
    # print("\n路径层: {0}".format(selected_individual.chromosomes[0]))
    # print("\n服务方式层: {0}".format(selected_individual.chromosomes[1]))
    
    # print("\n解码后的路径:")
    # for i, route in enumerate(selected_individual.routes):
    #     print(f"路径 {i+1}: {route}")
    
    # print("\n无人机任务:")
    # if not selected_individual.drone_tasks:
    #     print("无无人机任务")
    # else:
    #     for launch_point, tasks in selected_individual.drone_tasks.items():
    #         print(f"发射点 {launch_point}: 服务客户点 {tasks}")

    print("\n被选中的第一个父代个体详细信息:")
    print(f"适应度值: {parent1.fitness:.6f}")
    print(f"惩罚值: {parent1.penalty:.2f}")
    print(f"路径层长度: {len(parent1.chromosomes[0])}")
    print(f"服务方式层长度: {len(parent1.chromosomes[1])}")

    print("\n路径层: {0}".format(parent1.chromosomes[0]))
    print("\n服务方式层: {0}".format(parent1.chromosomes[1]))

    print("\n解码后的路径:")
    for i, route in enumerate(parent1.routes):
        print(f"路径 {i+1}: {route}")

    print("\n无人机任务:")
    if not parent1.drone_tasks:
        print("无无人机任务")
    else:
        for launch_point, tasks in parent1.drone_tasks.items():
            print(f"发射点 {launch_point}: 服务客户点 {tasks}")

    # 可选：如果还想查看第二个父代个体的信息，可以添加类似的代码
    print("\n被选中的第二个父代个体详细信息:")
    print(f"适应度值: {parent2.fitness:.6f}")
    print(f"惩罚值: {parent2.penalty:.2f}")

if __name__ == "__main__":
    test_selection()