#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试initialize_population函数的可行性
使用RC101-61-1.vrp数据集和默认参数值
"""

import sys
import os
from data_loader import Problem, Individual
from initialization import initialize_population

def test_initialize_population_functionality():
    """测试initialize_population函数的功能"""
    print("=" * 80)
    print("测试initialize_population函数可行性")
    print("=" * 80)
    
    # 1. 加载数据集
    data_path = "RC_随机+聚类_数据集/RC101-61-1.vrp"
    
    try:
        print("🔄 加载数据集...")
        problem = Problem(data_path)
        print(f"✅ 数据集加载成功: {problem.name}")
        print(f"   节点数量: {problem.dimension}")
        print(f"   客户点数量: {problem.num_customers}")
        print(f"   卡车数量: {problem.num_vehicles}")
        print(f"   每辆卡车配备无人机数量: {problem.num_drones}")
        print()
        
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        return
    
    # 2. 显示默认参数
    print("📋 使用的默认参数:")
    print(f"   卡车数量: {problem.num_vehicles}")
    print(f"   无人机数量: {problem.num_drones}")
    print(f"   卡车载重: {problem.vehicle_capacity} kg")
    print(f"   卡车速度: {problem.vehicle_speed} km/h")
    print(f"   无人机载重: {problem.drone_max_payload} kg")
    print(f"   无人机速度: {problem.drone_speed} km/h")
    print(f"   电池容量: {problem.battery_energy} Wh")
    print(f"   服务时间: {problem.service_time} min")
    print()
    
    # 3. 测试传统编码模式初始化
    print("🧪 测试传统编码模式初始化...")
    try:
        traditional_population = initialize_population(
            problem=problem, 
            pop_size=10, 
            use_extended_encoding=False
        )
        print(f"✅ 传统模式种群生成成功，大小: {len(traditional_population)}")
        
        # 打印第一个个体的详细信息
        if traditional_population:
            individual = traditional_population[0]
            print(f"\n📊 传统模式个体示例 (个体1):")
            print(f"   chromosomes: {individual.chromosomes}")
            print(f"   routes: {individual.routes}")
            print(f"   drone_tasks: {individual.drone_tasks}")
            print(f"   fitness: {individual.fitness}")
            print(f"   penalty: {individual.penalty}")
            print(f"   total_cost: {individual.total_cost}")
            
            # 检查服务方式
            if hasattr(individual, 'chromosomes') and individual.chromosomes:
                service_types = set(individual.chromosomes[1])
                print(f"   使用的服务方式: {service_types}")
        print()
        
    except Exception as e:
        print(f"❌ 传统模式初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 4. 测试扩展编码模式初始化
    print("🧪 测试扩展编码模式初始化...")
    try:
        extended_population = initialize_population(
            problem=problem, 
            pop_size=10, 
            use_extended_encoding=True
        )
        print(f"✅ 扩展模式种群生成成功，大小: {len(extended_population)}")
        
        # 打印第一个个体的详细信息
        if extended_population:
            individual = extended_population[0]
            print(f"\n📊 扩展模式个体示例 (个体1):")
            print(f"   chromosomes: {individual.chromosomes}")
            print(f"   routes: {individual.routes}")
            print(f"   drone_tasks类型: {type(individual.drone_tasks)}")
            print(f"   fitness: {individual.fitness}")
            print(f"   penalty: {individual.penalty}")
            print(f"   total_cost: {individual.total_cost}")
            
            # 检查服务方式
            if hasattr(individual, 'chromosomes') and individual.chromosomes:
                service_types = set(individual.chromosomes[1])
                print(f"   使用的服务方式: {service_types}")
                
            # 检查无人机任务格式
            if individual.drone_tasks:
                print(f"   无人机任务详情:")
                for launch_point, task_data in individual.drone_tasks.items():
                    if isinstance(task_data, dict) and 'drone_tasks' in task_data:
                        print(f"     发射点{launch_point}: 扩展格式")
                        print(f"       回收点: {task_data.get('recovery_point', 'N/A')}")
                        print(f"       任务数量: {len(task_data.get('drone_tasks', []))}")
                    else:
                        print(f"     发射点{launch_point}: 传统格式 {task_data}")
        print()
        
    except Exception as e:
        print(f"❌ 扩展模式初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 5. 详细打印所有个体的属性
    print("=" * 80)
    print("详细个体属性信息 (扩展编码模式)")
    print("=" * 80)
    
    for i, individual in enumerate(extended_population):
        print(f"\n🔍 个体 {i+1}:")
        print(f"   fitness: {individual.fitness}")
        print(f"   penalty: {individual.penalty}")
        print(f"   total_cost: {individual.total_cost}")
        
        # 染色体信息
        if hasattr(individual, 'chromosomes') and individual.chromosomes:
            path_layer, service_layer = individual.chromosomes
            print(f"   chromosomes长度: 路径层{len(path_layer)}, 服务层{len(service_layer)}")
            print(f"   服务方式: {set(service_layer)}")
        
        # 路径信息
        print(f"   routes数量: {len(individual.routes)}")
        for j, route in enumerate(individual.routes):
            if len(route) > 2:  # 只显示非空路径
                print(f"     路径{j+1}: {route} (长度: {len(route)})")
        
        # 无人机任务信息
        print(f"   drone_tasks数量: {len(individual.drone_tasks)}")
        for launch_point, task_data in individual.drone_tasks.items():
            if isinstance(task_data, dict) and 'drone_tasks' in task_data:
                drone_tasks = task_data['drone_tasks']
                recovery_point = task_data.get('recovery_point', 'N/A')
                print(f"     发射点{launch_point} -> 回收点{recovery_point}: {len(drone_tasks)}个任务")
                for k, drone_task in enumerate(drone_tasks):
                    if hasattr(drone_task, 'customer_sequence'):
                        print(f"       任务{k+1}: 客户序列{drone_task.customer_sequence}")
            else:
                print(f"     发射点{launch_point}: {task_data}")
        
        print(f"   _decode_cache: {'已缓存' if individual._decode_cache else '未缓存'}")
    
    # 6. 统计信息
    print("\n" + "=" * 80)
    print("种群统计信息")
    print("=" * 80)
    
    # 服务方式统计
    all_service_types = set()
    extended_encoding_count = 0
    
    for individual in extended_population:
        if hasattr(individual, 'chromosomes') and individual.chromosomes:
            service_types = set(individual.chromosomes[1])
            all_service_types.update(service_types)
            if any(s in [2, 3, 4] for s in service_types):
                extended_encoding_count += 1
    
    print(f"📊 种群大小: {len(extended_population)}")
    print(f"📊 使用扩展编码的个体数量: {extended_encoding_count}/{len(extended_population)}")
    print(f"📊 所有使用的服务方式: {sorted(all_service_types)}")
    
    # 路径长度统计
    route_lengths = []
    drone_task_counts = []
    
    for individual in extended_population:
        total_route_length = sum(len(route) for route in individual.routes)
        route_lengths.append(total_route_length)
        drone_task_counts.append(len(individual.drone_tasks))
    
    print(f"📊 平均路径总长度: {sum(route_lengths)/len(route_lengths):.1f}")
    print(f"📊 平均无人机任务数量: {sum(drone_task_counts)/len(drone_task_counts):.1f}")
    
    print("\n✅ initialize_population函数测试完成！")

if __name__ == "__main__":
    test_initialize_population_functionality()
