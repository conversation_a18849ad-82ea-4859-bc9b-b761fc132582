========== 运行 1/1 ==========
初始化种群...
初始化完成，种群大小: 100
初始最佳适应度: 0.000053

========== 第20代最优解详情 ==========
适应度: 0.000851
总成本: 1174.45
惩罚值: 0.00

========== 多次运行结果统计 ==========
运行次数: 1
平均总成本: 704.12
最小总成本: 704.12 (运行 1)
最大总成本: 704.12 (运行 1)
总成本标准差: 0.00

========== 算法精度与稳定性分析 ==========
最大偏差: 0.00 (0.00%)
平均偏差: 0.00 (0.00%)
平均求解时间: 204.59秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 |  0.00142 |   704.12 |        0 | 204.59秒   |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 1) ==========
适应度: 0.001420
总成本: 704.12
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 40, 21, 62, 34, 37, 11, 2, 8, 68, 43, 16, 57, 35, 61, 75, 20, 65, 15, 41, 25, 46, 39, 60, 17, 31, 29, 74, 0, 73
, 1, 7, 10, 52, 28, 79, 48, 18, 14, 63, 23, 24, 6, 30, 78, 19, 26, 69, 55, 9, 64, 77, 51, 0, 13, 42, 33, 56, 47, 27, 59, 5, 44, 12, 3, 71, 53, 70, 49, 66, 67, 58, 38, 32, 22, 45, 4, 54, 72, 76, 50, 36, 0)                                                服务方式层: (0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0)                                                                                                                
染色体解码结果:
  车辆 1 路径: [0, 40, 21, 62, 34, 2, 8, 68, 43, 16, 61, 75, 65, 15, 41, 25, 46, 39, 60, 17, 74, 0]
    无人机任务:
      发射点 34 → 任务: [37, 11]
      发射点 16 → 任务: [57, 35]
      发射点 75 → 任务: [20]
      发射点 17 → 任务: [31, 29]
  车辆 2 路径: [0, 73, 1, 7, 10, 52, 28, 79, 48, 63, 23, 24, 6, 78, 19, 55, 64, 77, 51, 0]
    无人机任务:
      发射点 48 → 任务: [18, 14]
      发射点 6 → 任务: [30]
      发射点 19 → 任务: [26, 69]
      发射点 55 → 任务: [9]
  车辆 3 路径: [0, 13, 42, 33, 27, 59, 5, 44, 12, 53, 66, 58, 32, 22, 4, 54, 72, 76, 36, 0]
    无人机任务:
      发射点 33 → 任务: [56, 47]
      发射点 12 → 任务: [3, 71]
      发射点 53 → 任务: [70, 49]
      发射点 66 → 任务: [67]
      发射点 58 → 任务: [38]
      发射点 22 → 任务: [45]
      发射点 76 → 任务: [50]

程序执行完毕。
