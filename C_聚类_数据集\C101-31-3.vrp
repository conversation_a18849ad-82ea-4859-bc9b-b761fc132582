NAME : C101-31-3
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 10.0 12.5
 1 11.2 17.0
 2 11.2 17.5
 3 10.5 16.5
 4 10.5 17.0
 5 10.0 17.2
 6 10.0 16.5
 7 9.5 17.0
 8 9.5 17.5
 9 8.8 17.2
 10 7.5 12.5
 11 8.8 7.5
 12 8.8 8.0
 13 8.2 8.0
 14 8.2 8.8
 15 8.0 7.5
 16 7.5 7.5
 17 7.5 8.0
 18 7.5 8.8
 19 7.0 7.5
 20 7.0 8.8
 21 6.5 8.0
 22 6.2 7.5
 23 6.2 8.8
 24 11.2 7.5
 25 11.2 8.8
 26 22.0 8.8
 27 21.2 8.8
 28 18.8 13.8
 29 18.0 13.8
 30 17.5 14.5

DEMAND_SECTION
 0 0 0      
 1 2.5 0.0      // 仅送货需求客户 - light包裹
 2 1.7 0.0      // 仅送货需求客户 - light包裹
 3 0.0 2.6      // 仅取货需求客户 - light包裹
 4 0.0 6.6      // 仅取货需求客户 - medium包裹
 5 4.1 0.0      // 仅送货需求客户 - light包裹
 6 2.3 5.0      // 送货取货双需求客户 - light包裹
 7 3.0 0.0      // 仅送货需求客户 - light包裹
 8 4.8 0.0      // 仅送货需求客户 - light包裹
 9 3.6 0.0      // 仅送货需求客户 - light包裹
 10 0.0 3.7      // 仅取货需求客户 - light包裹
 11 1.4 9.6      // 送货取货双需求客户 - light包裹
 12 0.0 2.2      // 仅取货需求客户 - light包裹
 13 5.8 0.0      // 仅送货需求客户 - medium包裹
 14 4.9 6.5      // 送货取货双需求客户 - light包裹
 15 2.8 0.0      // 仅送货需求客户 - light包裹
 16 7.9 0.0      // 仅送货需求客户 - medium包裹
 17 2.5 4.6      // 送货取货双需求客户 - light包裹
 18 0.0 3.8      // 仅取货需求客户 - light包裹
 19 9.3 0.0      // 仅送货需求客户 - medium包裹
 20 0.0 8.9      // 仅取货需求客户 - medium包裹
 21 4.2 0.0      // 仅送货需求客户 - light包裹
 22 2.9 0.0      // 仅送货需求客户 - light包裹
 23 4.7 2.1      // 送货取货双需求客户 - light包裹
 24 2.9 0.0      // 仅送货需求客户 - light包裹
 25 3.2 0.0      // 仅送货需求客户 - light包裹
 26 4.0 5.0      // 送货取货双需求客户 - light包裹
 27 9.5 0.0      // 仅送货需求客户 - medium包裹
 28 4.6 5.0      // 送货取货双需求客户 - light包裹
 29 4.4 0.0      // 仅送货需求客户 - light包裹
 30 0.0 4.0      // 仅取货需求客户 - light包裹
 1 10 0      
 2 30 0      
 3 10 0      
 4 10 0      
 5 20 0      
 6 20 0      
 7 20 0      
 8 10 0      
 9 10 0      
 10 10 0      
 11 10 0      
 12 10 0      
 13 20 0      
 14 10 0      
 15 10 0      
 16 10 0      
 17 30 0      
 18 10 0      
 19 10 0      
 20 10 0      
 21 10 0      
 22 10 0      
 23 10 0      
 24 10 0      
 25 10 0      
 26 20 0      
 27 30 0      
 28 20 0      
 29 10 0      
 30 20 0      

DEPOT_SECTION
 0
-1
EOF