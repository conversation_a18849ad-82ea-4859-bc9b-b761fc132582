import copy
import random
import numpy as np
from data_loader import Individual, Problem
from repair import check_and_repair_path_separation

def test_check_and_repair_path_separation():
    """测试check_and_repair_path_separation函数的空路径修复功能"""
    print("开始测试路径分隔修复函数...")
    
    # 创建一个简单的模拟问题实例 - 设置2辆车
    mock_problem = type('MockProblem', (), {
        'num_vehicles': 2,  # 2辆卡车
        'num_drones': 2,    # 每辆卡车2架无人机
        'dimension': 11,    # 1个配送中心 + 10个客户点
        'locations': {
            0: (0, 0),    # 配送中心
            1: (1, 1),    
            2: (2, 2),
            3: (3, 3),
            4: (4, 4),
            5: (5, 5),
            6: (6, 6),
            7: (7, 7),
            8: (8, 8),
            9: (9, 9),
            10: (10, 10)
        }
    })()
    
    # 使用指定的染色体，其中包含空路径问题
    path_layer    = [0, 0, 2, 8, 7, 5, 1, 3, 9, 6, 10, 4, 0]
    service_layer = [0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0]
    
    # 创建个体
    individual = Individual(chromosomes=(path_layer, service_layer))
    original_chromosomes = copy.deepcopy(individual.chromosomes)
    
    # 分析初始状态，解码染色体，验证存在空路径
    print("\n修复前:")
    print("路径层:", path_layer)
    print("服务方式层:", service_layer)
    
    # 解码染色体
    decoded = individual.decode((path_layer, service_layer), None)
    routes_before = [route for route, _ in decoded]
    drone_tasks_before = {}
    for _, tasks in decoded:
        for launch_node, drone_nodes in tasks.items():
            if launch_node not in drone_tasks_before:
                drone_tasks_before[launch_node] = []
            drone_tasks_before[launch_node].extend(drone_nodes)
    
    print("解码后路径:", routes_before)
    print("解码后无人机任务:", drone_tasks_before)
    
    # 验证是否存在空路径
    has_empty_path = any(len(route) <= 2 for route in routes_before)
    print(f"存在空路径: {'是' if has_empty_path else '否'}")
    print(f"路径数量: {len(routes_before)}, 需要: {mock_problem.num_vehicles}")
    
    # 调用修复函数
    print("\n调用修复函数...")
    result = check_and_repair_path_separation(mock_problem, individual)
    
    # 打印修复结果
    path_after, service_after = individual.chromosomes
    print("\n修复后:")
    print("路径层:", path_after)
    print("服务方式层:", service_after)
    print("修复结果:", "原本可行" if result else "已修复不可行解")
    
    # 解码修复后的染色体
    decoded_after = individual.decode((path_after, service_after), None)
    routes_after = [route for route, _ in decoded_after]
    drone_tasks_after = {}
    for _, tasks in decoded_after:
        for launch_node, drone_nodes in tasks.items():
            if launch_node not in drone_tasks_after:
                drone_tasks_after[launch_node] = []
            drone_tasks_after[launch_node].extend(drone_nodes)
    
    print("修复后路径:", routes_after)
    print("修复后无人机任务:", drone_tasks_after)
    
    # 验证修复后是否满足路径要求
    has_empty_path_after = any(len(route) <= 2 for route in routes_after)
    
    # 分析修复后的路径长度分布
    if len(routes_after) >= 2:
        route_lengths = [len(route) for route in routes_after]
        route_distances = [calculate_route_distance(route, mock_problem) for route in routes_after]
        
        print("\n路径长度分布:")
        for i, (length, distance) in enumerate(zip(route_lengths, route_distances)):
            print(f"  路径{i+1}: {length-2}个客户点, 距离: {distance:.2f}")
        
        # 计算路径长度的方差（越小表示越平衡）
        length_variance = np.var(route_lengths)
        distance_variance = np.var(route_distances)
        print(f"  路径长度方差: {length_variance:.2f}")
        print(f"  路径距离方差: {distance_variance:.2f}")
    
    # 检查修复后的染色体是否有连续的0
    has_consecutive_zeros = False
    for i in range(len(path_after) - 1):
        if path_after[i] == 0 and path_after[i+1] == 0:
            has_consecutive_zeros = True
            break

    # 最终验证
    is_feasible = not has_empty_path_after and len(routes_after) == mock_problem.num_vehicles and not has_consecutive_zeros
    print("\n最终验证:")
    print(f"  无空路径: {'是' if not has_empty_path_after else '否'}")
    print(f"  路径数量正确: {'是' if len(routes_after) == mock_problem.num_vehicles else '否'}")
    print(f"  无连续0: {'是' if not has_consecutive_zeros else '否'}")
    print(f"  总体结果: {'可行' if is_feasible else '不可行'}")

    # 添加染色体分析代码
    print("\n染色体分析:")
    path_after, service_after = individual.chromosomes
    # 检查是否存在连续的0
    consecutive_zeros = []
    for i in range(len(path_after) - 1):
        if path_after[i] == 0 and path_after[i+1] == 0:
            consecutive_zeros.append(i)
    
    if consecutive_zeros:
        print("  发现连续的配送中心标记(0):")
        for idx in consecutive_zeros:
            print(f"  位置 {idx} 和 {idx+1} 均为0")
        print("  这会导致decode函数生成空路径[0,0]")
    else:
        print("  染色体中没有连续的配送中心标记")
    
    # 显示修复建议
    print("\n修复建议:")
    print("  1. 修改check_and_repair_path_separation函数，在生成新染色体时确保不产生连续的0")
    print("  2. 或修改Individual.encode函数，处理连续0的情况")
    print("  3. 或修改Individual.decode函数，在解码时忽略空路径[0,0]")

def calculate_route_distance(route, problem):
    """计算路径的总距离"""
    # 如果路径为空或只有一个节点，返回0
    if len(route) <= 1:
        return 0
    
    total_distance = 0
    
    # 遍历路径中的每对相邻节点
    for i in range(len(route) - 1):
        # 获取两个相邻节点
        from_node = route[i]
        to_node = route[i + 1]
        
        # 获取节点坐标
        x1, y1 = problem.locations[from_node]
        x2, y2 = problem.locations[to_node]
        
        # 计算欧几里得距离
        distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
        
        # 累加距离
        total_distance += distance
    
    return total_distance

if __name__ == "__main__":
    # 设置随机种子以便结果可重现
    random.seed(42)
    np.random.seed(42)
    test_check_and_repair_path_separation()
