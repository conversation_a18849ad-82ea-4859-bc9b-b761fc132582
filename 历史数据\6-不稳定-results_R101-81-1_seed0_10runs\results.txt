  # 自适应变异因子 - 随着迭代进行而减小
  self.mutation_factor = self.mutation_factor * (1 - 0.6 * elapsed_time / self.max_runtime)

  if progress_ratio <= 0.2:
  ls_frequency = 30  # 前期
  elif progress_ratio <= 0.5:
  ls_frequency = 25  # 中期
  else:
  ls_frequency = 20  # 后期


  # 确定局部搜索概率（随着进化进行逐渐增加）
  base_probability = 0.1
  adaptive_factor = elapsed_time / self.max_runtime 
  search_probability = base_probability + (0.9 * adaptive_factor)

  # 确定应用局部搜索的个体数量，采用自适应策略
  max_elite_percentage = 0.2
  min_elite_percentage = 0.05
  elite_percentage = min_elite_percentage + (max_elite_percentage - min_elite_percentage) * adaptive_factor
  elite_count = max(1, int(len(population) * elite_percentage))


  # 计算各方法生成的数量
  savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
  random_count = pop_size - savings_count  # 50%使用随机生成

========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1028.49
最小总成本: 937.03 (运行 6)
最大总成本: 1183.29 (运行 5)
总成本标准差: 68.11

========== 算法精度与稳定性分析 ==========
最大偏差: 154.79 (15.05%)
平均偏差: 52.01 (5.06%)
平均求解时间: 305.05秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.001020 |  980.81 |    0.00 | 302.86
     2 |        2 | 0.000992 | 1007.92 |    0.00 | 310.48
     3 |        3 | 0.000987 | 1012.72 |    0.00 | 307.16
     4 |        4 | 0.000898 | 1113.87 |    0.00 | 302.13
     5 |        5 | 0.000845 | 1183.29 |    0.00 | 302.42
     6 |        6 | 0.001067 |  937.03 |    0.00 | 302.41
     7 |        7 | 0.000958 | 1043.40 |    0.00 | 310.45
     8 |        8 | 0.001010 |  984.19 |    6.35 | 307.77
     9 |        9 | 0.001012 |  988.27 |    0.00 | 302.60
    10 |       10 | 0.000968 | 1033.45 |    0.00 | 302.25

最佳解详细信息:
运行ID: 6
适应度: 0.001067
总成本: 937.03
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 49, 11, 73, 48, 21, 65, 3, 67, 28, 66, 68, 24, 4, 57, 10, 46, 69, 9, 60, 29, 54, 55, 25, 59, 17, 1, 43, 23, 58, 0, 27, 75, 22, 44, 15, 74, 26, 53, 7, 41, 70, 16, 30, 40, 42, 39, 38, 8, 14, 72, 37, 31, 32, 78, 50, 52, 76, 79, 77, 0, 45, 33, 18, 62, 61, 63, 64, 47, 20, 56, 19, 34, 13, 36, 35, 80, 12, 2, 0, 6, 5, 51, 71, 0)
染色体 2: (0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0)

最佳解路线详情:
路线 1: [0, 49, 11, 21, 65, 3, 67, 68, 57, 69, 9, 60, 55, 25, 1, 58, 0]
  无人机任务:
    从节点 11 发射无人机访问: [73, 48]
    从节点 67 发射无人机访问: [28, 66]
    从节点 68 发射无人机访问: [24, 4]
    从节点 57 发射无人机访问: [10, 46]
    从节点 60 发射无人机访问: [29, 54]
    从节点 25 发射无人机访问: [59, 17]
    从节点 1 发射无人机访问: [43, 23]
路线 2: [0, 27, 22, 44, 74, 7, 41, 30, 39, 38, 14, 72, 37, 78, 76, 77, 0]
  无人机任务:
    从节点 27 发射无人机访问: [75]
    从节点 44 发射无人机访问: [15]
    从节点 74 发射无人机访问: [26, 53]
    从节点 41 发射无人机访问: [70, 16]
    从节点 30 发射无人机访问: [40, 42]
    从节点 38 发射无人机访问: [8]
    从节点 37 发射无人机访问: [31, 32]
    从节点 78 发射无人机访问: [50, 52]
    从节点 76 发射无人机访问: [79]
路线 3: [0, 45, 33, 18, 61, 63, 64, 47, 20, 34, 13, 36, 35, 2, 0]
  无人机任务:
    从节点 18 发射无人机访问: [62]
    从节点 20 发射无人机访问: [56, 19]
    从节点 35 发射无人机访问: [80, 12]
路线 4: [0, 6, 5, 0]
  无人机任务:
    从节点 5 发射无人机访问: [51, 71]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000557 | 1794.41 |    0.00
  10 | 0.000557 | 1794.41 |    0.00
  20 | 0.000557 | 1794.41 |    0.00
  30 | 0.000564 | 1773.27 |    0.00
  40 | 0.000564 | 1773.27 |    0.00
  50 | 0.000576 | 1736.17 |    0.00
  60 | 0.000580 | 1722.90 |    0.00
  70 | 0.000580 | 1722.90 |    0.00
  80 | 0.000596 | 1677.30 |    0.00
  90 | 0.000627 | 1596.13 |    0.00
 100 | 0.000640 | 1563.34 |    0.00
 110 | 0.000646 | 1547.25 |    0.00
 120 | 0.000662 | 1510.76 |    0.00
 130 | 0.000686 | 1458.03 |    0.00
 140 | 0.000709 | 1410.77 |    0.00
 150 | 0.000726 | 1376.78 |    0.00
 160 | 0.000734 | 1362.13 |    0.00
 170 | 0.000738 | 1354.99 |    0.00
 180 | 0.000743 | 1345.35 |    0.00
 190 | 0.000744 | 1344.82 |    0.00
 200 | 0.000794 | 1258.98 |    0.00
 210 | 0.000800 | 1250.23 |    0.00
 220 | 0.000829 | 1206.48 |    0.00
 230 | 0.000830 | 1205.34 |    0.00
 240 | 0.000858 | 1165.76 |    0.00
 250 | 0.000890 | 1124.11 |    0.00
 260 | 0.000895 | 1116.92 |    0.00
 270 | 0.000897 | 1115.07 |    0.00
 280 | 0.000897 | 1115.07 |    0.00
 290 | 0.000897 | 1115.07 |    0.00
 300 | 0.000913 | 1095.44 |    0.00
 310 | 0.000918 | 1089.89 |    0.00
 320 | 0.000983 | 1017.53 |    0.00
 330 | 0.000983 | 1017.53 |    0.00
 340 | 0.001032 |  969.40 |    0.00
 350 | 0.001041 |  960.71 |    0.00
 360 | 0.001042 |  959.33 |    0.00
 370 | 0.001044 |  957.56 |    0.00
 380 | 0.001063 |  940.49 |    0.00
 390 | 0.001063 |  940.49 |    0.00
