#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步模式与主算法的集成状态
"""

import random
import numpy as np
from data_loader import Problem, Individual
from initialization import initialize_population
from algorithm import solve_vrpd

def test_current_integration_status():
    """测试当前集成状态"""
    print("=" * 80)
    print("测试当前算法集成状态")
    print("=" * 80)

    # 设置随机种子
    random.seed(42)
    np.random.seed(42)

    # 加载问题
    problem = Problem("Vrp-Set-Solomon/A-n32-k2-d4.vrp")
    print(f"问题加载成功: {len(problem.customers)} 个客户")

    # 测试1: 检查传统初始化系统
    print("\n1. 测试传统初始化系统")
    print("-" * 40)

    population_traditional = initialize_population(problem, 3, use_extended_encoding=False)
    print(f"传统模式种群大小: {len(population_traditional)}")

    for i, individual in enumerate(population_traditional[:1]):  # 只检查第1个
        print(f"\n传统模式个体 {i+1}:")
        print(f"  路径: {individual.routes}")
        print(f"  无人机任务: {individual.drone_tasks}")

        # 检查服务方式
        service_layer = individual.chromosomes[1]
        service_types = set(service_layer)
        print(f"  使用的服务方式: {service_types}")

        # 判断编码模式
        if any(s in [2, 3, 4] for s in service_types):
            print(f"  ✅ 使用扩展编码模式")
        else:
            print(f"  ❌ 使用传统编码模式")

    # 测试2: 检查扩展初始化系统
    print("\n2. 测试扩展初始化系统")
    print("-" * 40)

    population_extended = initialize_population(problem, 3, use_extended_encoding=True)
    print(f"扩展模式种群大小: {len(population_extended)}")

    for i, individual in enumerate(population_extended[:1]):  # 只检查第1个
        print(f"\n扩展模式个体 {i+1}:")
        print(f"  路径: {individual.routes}")
        print(f"  无人机任务: {individual.drone_tasks}")

        # 检查服务方式
        service_layer = individual.chromosomes[1]
        service_types = set(service_layer)
        print(f"  使用的服务方式: {service_types}")

        # 判断编码模式
        if any(s in [2, 3, 4] for s in service_types):
            print(f"  ✅ 使用扩展编码模式")
        else:
            print(f"  ❌ 使用传统编码模式")

def test_extended_encoding_support():
    """测试扩展编码支持"""
    print("\n" + "=" * 80)
    print("测试扩展编码支持")
    print("=" * 80)
    
    # 创建测试数据
    test_routes = [[0, 6, 12, 0], [0, 15, 22, 0]]
    
    # 创建扩展格式的无人机任务
    from data_loader import DroneTask
    
    drone_task1 = DroneTask(
        drone_id=1,
        customer_sequence=[4, 7],
        launch_point=6,
        recovery_point=12,
        total_energy=150.0
    )
    
    test_drone_tasks_extended = {
        6: {
            'drone_tasks': [drone_task1],
            'recovery_point': 12
        }
    }
    
    print("测试数据:")
    print(f"  卡车路径: {test_routes}")
    print(f"  扩展无人机任务: {test_drone_tasks_extended}")
    
    # 测试扩展编码
    print("\n测试扩展编码:")
    try:
        chromosomes = Individual.encode(test_routes, test_drone_tasks_extended, None, use_extended_encoding=True)
        print(f"  ✅ 扩展编码成功")
        print(f"  路径层: {chromosomes[0]}")
        print(f"  服务层: {chromosomes[1]}")
        
        # 检查服务方式
        service_types = set(chromosomes[1])
        print(f"  服务方式: {service_types}")
        
        if any(s in [2, 3, 4] for s in service_types):
            print(f"  ✅ 包含异步服务方式")
        else:
            print(f"  ❌ 仅包含传统服务方式")
            
    except Exception as e:
        print(f"  ❌ 扩展编码失败: {e}")

def test_algorithm_with_extended_encoding():
    """测试算法是否支持扩展编码"""
    print("\n" + "=" * 80)
    print("测试算法对扩展编码的支持")
    print("=" * 80)
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 加载问题
    problem = Problem("Vrp-Set-Solomon/A-n32-k2-d4.vrp")
    
    # 手动创建一个使用扩展编码的个体
    test_routes = [[0, 6, 12, 0], [0, 15, 22, 0]]
    
    # 创建扩展格式的无人机任务
    from data_loader import DroneTask
    
    drone_task1 = DroneTask(
        drone_id=1,
        customer_sequence=[4, 7],
        launch_point=6,
        recovery_point=12,
        total_energy=150.0
    )
    
    test_drone_tasks_extended = {
        6: {
            'drone_tasks': [drone_task1],
            'recovery_point': 12
        }
    }
    
    print("创建扩展编码个体:")
    try:
        # 使用扩展编码创建个体
        chromosomes = Individual.encode(test_routes, test_drone_tasks_extended, problem, use_extended_encoding=True)
        individual = Individual(chromosomes=chromosomes)
        
        print(f"  ✅ 个体创建成功")
        print(f"  染色体: {individual.chromosomes}")
        print(f"  路径: {individual.routes}")
        print(f"  无人机任务: {individual.drone_tasks}")
        
        # 测试适应度评估
        from utils import evaluate_individual_with_penalty
        fitness = evaluate_individual_with_penalty(problem, individual, 0, 100)
        print(f"  ✅ 适应度评估成功: {fitness:.6f}")
        print(f"  总成本: {individual.total_cost:.2f}")
        print(f"  惩罚值: {individual.penalty:.2f}")
        
    except Exception as e:
        print(f"  ❌ 扩展编码个体测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_complete_algorithm_flow():
    """测试完整的算法流程"""
    print("\n" + "=" * 80)
    print("测试完整算法流程")
    print("=" * 80)

    # 设置随机种子
    random.seed(42)
    np.random.seed(42)

    # 加载问题
    problem = Problem("Vrp-Set-Solomon/A-n32-k2-d4.vrp")
    print(f"问题加载成功: {len(problem.customers)} 个客户")

    # 测试扩展编码模式的完整算法流程
    print("\n测试扩展编码模式算法:")
    try:
        from algorithm import DifferentialEvolution

        # 创建算法实例
        de = DifferentialEvolution(
            problem=problem,
            pop_size=10,  # 小种群快速测试
            max_generations=5,
            max_runtime=10,
            use_extended_encoding=True
        )

        print("  ✅ 算法实例创建成功")

        # 初始化种群
        de.initialize()
        print(f"  ✅ 种群初始化成功，大小: {len(de.population)}")

        # 检查初始种群的编码模式
        extended_count = 0
        for individual in de.population:
            service_types = set(individual.chromosomes[1])
            if any(s in [2, 3, 4] for s in service_types):
                extended_count += 1

        print(f"  ✅ 使用扩展编码的个体数量: {extended_count}/{len(de.population)}")

        # 运行几代进化
        print("  🔄 运行算法...")
        best_individual, history = de.evolve()

        print(f"  ✅ 算法运行成功")
        print(f"  最佳适应度: {de.best_fitness:.6f}")
        print(f"  最佳成本: {de.best_individual.total_cost:.2f}")
        print(f"  运行代数: {len(history['generation'])}")

        # 检查最佳个体的编码模式
        best_service_types = set(de.best_individual.chromosomes[1])
        if any(s in [2, 3, 4] for s in best_service_types):
            print(f"  ✅ 最佳个体使用扩展编码模式")
        else:
            print(f"  ❌ 最佳个体使用传统编码模式")

    except Exception as e:
        print(f"  ❌ 算法流程测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_integration_recommendations():
    """提供集成建议"""
    print("\n" + "=" * 80)
    print("集成状态分析和建议")
    print("=" * 80)

    print("📋 当前状态:")
    print("  ✅ 扩展编码解码系统已完成")
    print("  ✅ 成本计算系统已扩展")
    print("  ✅ DroneTask类已实现")
    print("  ✅ 初始化系统已支持扩展编码")
    print("  ✅ 算法参数已配置扩展模式")

    print("\n🔧 已完成的集成:")
    print("  1. ✅ initialization.py - 支持扩展编码初始化")
    print("  2. ✅ main.py - 添加扩展编码模式选择")
    print("  3. ✅ algorithm.py - 确保支持扩展编码个体")
    print("  4. ⚠️  operators.py - 需要验证变异交叉操作兼容性")

    print("\n📝 下一步行动计划:")
    print("  1. ✅ 修改初始化系统支持扩展编码 - 已完成")
    print("  2. ✅ 在主程序中添加编码模式选择参数 - 已完成")
    print("  3. 🔄 测试完整的算法流程 - 进行中")
    print("  4. 📊 进行性能对比实验 - 待进行")
    print("  5. 🔧 验证变异交叉操作兼容性 - 待进行")

if __name__ == "__main__":
    test_current_integration_status()
    test_extended_encoding_support()
    test_algorithm_with_extended_encoding()
    test_complete_algorithm_flow()
    test_integration_recommendations()
