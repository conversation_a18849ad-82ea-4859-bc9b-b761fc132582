#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
improved_savings_algorithm 实例推演演示
展示算法的详细执行过程和中间结果
"""

import math
import random
from typing import List, Tuple, Dict

def euclidean_distance(a: <PERSON><PERSON>[float, float], b: <PERSON><PERSON>[float, float]) -> float:
    """计算欧氏距离"""
    return math.sqrt((a[0]-b[0])**2 + (a[1]-b[1])**2)

class MockProblem:
    """模拟问题实例"""
    def __init__(self):
        # 节点坐标
        self.locations = {
            0: (0, 0),    # 配送中心
            1: (10, 0),   # 客户1
            2: (0, 10),   # 客户2  
            3: (10, 10),  # 客户3
            4: (5, 5),    # 客户4
            5: (15, 5),   # 客户5
        }
        
        # 客户需求 (配送需求, 取货需求)
        self.demands = {
            0: (0, 0),    # 配送中心无需求
            1: (20, 0),   # 客户1需要配送20kg
            2: (15, 5),   # 客户2需要配送15kg，取货5kg
            3: (25, 0),   # 客户3需要配送25kg
            4: (10, 10),  # 客户4需要配送10kg，取货10kg
            5: (30, 0),   # 客户5需要配送30kg
        }
        
        # 卡车参数
        self.truck_params = {
            'max_load': 100,      # 最大载重100kg
            'num_vehicles': 2,    # 2辆车
            'speed': 30.0,        # 速度30km/h
            'cost_per_km': 3.25   # 每公里成本3.25元
        }

def demonstrate_savings_algorithm():
    """演示改进节约算法的完整执行过程"""
    print("=" * 80)
    print("improved_savings_algorithm 实例推演演示")
    print("=" * 80)
    
    # 创建问题实例
    problem = MockProblem()
    
    print("📋 问题设置:")
    print(f"  配送中心: 节点0 {problem.locations[0]}")
    print("  客户点信息:")
    for i in range(1, 6):
        coord = problem.locations[i]
        demand = problem.demands[i]
        print(f"    客户{i}: 坐标{coord}, 需求(配送{demand[0]}kg, 取货{demand[1]}kg)")
    print(f"  车辆数量: {problem.truck_params['num_vehicles']}")
    print(f"  车辆载重: {problem.truck_params['max_load']}kg")
    print()
    
    # 步骤1: 初始化
    print("🔧 步骤1: 初始化路径")
    customers = [1, 2, 3, 4, 5]
    routes = [[0, i, 0] for i in customers]
    print("  初始路径:")
    for i, route in enumerate(routes):
        print(f"    路径{i+1}: {route}")
    print()
    
    # 步骤2: 计算距离矩阵
    print("📏 步骤2: 计算距离矩阵")
    print("  距离矩阵:")
    print("     ", end="")
    for j in range(6):
        print(f"{j:>8}", end="")
    print()
    
    for i in range(6):
        print(f"  {i}: ", end="")
        for j in range(6):
            if i == j:
                print(f"{'0.00':>8}", end="")
            else:
                dist = euclidean_distance(problem.locations[i], problem.locations[j])
                print(f"{dist:>8.2f}", end="")
        print()
    print()
    
    # 步骤3: 计算节约值
    print("💰 步骤3: 计算节约值")
    savings = []
    print("  客户对的节约值计算:")
    
    for i in customers:
        for j in customers:
            if i >= j:
                continue
            
            d_0i = euclidean_distance(problem.locations[0], problem.locations[i])
            d_0j = euclidean_distance(problem.locations[0], problem.locations[j])
            d_ij = euclidean_distance(problem.locations[i], problem.locations[j])
            saving = d_0i + d_0j - d_ij
            
            print(f"    客户对({i},{j}): d(0,{i})={d_0i:.2f} + d(0,{j})={d_0j:.2f} - d({i},{j})={d_ij:.2f} = {saving:.2f}")
            
            # 添加随机扰动（为了演示，这里固定随机种子）
            random.seed(42 + i * 10 + j)
            perturbation = random.uniform(-0.2, 0.2)
            saving_with_noise = saving * (1 + perturbation)
            print(f"      添加扰动({perturbation:+.1%}): {saving_with_noise:.2f}")
            
            savings.append((saving_with_noise, i, j))
    print()
    
    # 步骤4: 排序
    print("📊 步骤4: 节约值排序")
    savings.sort(reverse=True)
    print("  排序后的节约值:")
    for i, (s, customer_i, customer_j) in enumerate(savings):
        print(f"    {i+1}. 客户对({customer_i},{customer_j}): {s:.2f}")
    print()
    
    # 步骤5: 路径合并过程
    print("🔄 步骤5: 路径合并过程")
    truck_capacity = problem.truck_params['max_load']
    num_vehicles = problem.truck_params['num_vehicles']
    
    merge_count = 0
    while len(routes) > num_vehicles and savings:
        merge_count += 1
        print(f"  第{merge_count}次合并尝试:")
        
        # 选择节约值最大的
        s, i, j = savings.pop(0)
        print(f"    选择客户对({i},{j}), 节约值: {s:.2f}")
        
        # 查找包含这两个客户的路径
        route_i = None
        route_j = None
        
        for r in routes:
            if len(r) > 2:
                if r[-2] == i:  # 客户i在路径末尾
                    route_i = r
                if r[1] == j:   # 客户j在路径开头
                    route_j = r
        
        print(f"    查找路径: 客户{i}在路径末尾的路径: {route_i}")
        print(f"    查找路径: 客户{j}在路径开头的路径: {route_j}")
        
        # 如果找到两条符合条件的不同路径，尝试合并
        if route_i and route_j and route_i != route_j:
            # 创建合并路径
            new_route = route_i[:-1] + route_j[1:]
            print(f"    合并路径: {route_i} + {route_j} = {new_route}")
            
            # 检查合并后的路径是否满足载重约束
            total_demand = 0
            demand_details = []
            for node in new_route:
                if node != 0:
                    delivery, pickup = problem.demands.get(node, (0, 0))
                    total_demand += delivery + pickup
                    demand_details.append(f"客户{node}({delivery}+{pickup})")
            
            print(f"    载重检查: {' + '.join(demand_details)} = {total_demand}kg")
            
            if total_demand <= truck_capacity:
                print(f"    ✅ 载重约束满足({total_demand} ≤ {truck_capacity}), 执行合并")
                routes.remove(route_i)
                routes.remove(route_j)
                routes.append(new_route)
                
                print("    当前路径状态:")
                for idx, route in enumerate(routes):
                    route_demand = sum(problem.demands[node][0] + problem.demands[node][1] 
                                     for node in route if node != 0)
                    print(f"      路径{idx+1}: {route} (载重: {route_demand}kg)")
            else:
                print(f"    ❌ 载重约束违反({total_demand} > {truck_capacity}), 跳过合并")
        else:
            print("    ❌ 未找到符合条件的路径对，跳过")
        
        print()
        
        if len(routes) <= num_vehicles:
            break
    
    # 最终结果
    print("🎯 最终结果:")
    print(f"  目标车辆数量: {num_vehicles}")
    print(f"  实际路径数量: {len(routes)}")
    print("  最终路径方案:")
    
    total_distance = 0
    for idx, route in enumerate(routes):
        # 计算路径距离
        route_distance = 0
        for k in range(len(route)-1):
            route_distance += euclidean_distance(problem.locations[route[k]], 
                                               problem.locations[route[k+1]])
        
        # 计算路径载重
        route_demand = sum(problem.demands[node][0] + problem.demands[node][1] 
                          for node in route if node != 0)
        
        print(f"    路径{idx+1}: {route}")
        print(f"      距离: {route_distance:.2f}km")
        print(f"      载重: {route_demand}kg / {truck_capacity}kg")
        print(f"      利用率: {route_demand/truck_capacity*100:.1f}%")
        
        total_distance += route_distance
    
    print(f"  总距离: {total_distance:.2f}km")
    print(f"  总成本: {total_distance * problem.truck_params['cost_per_km']:.2f}元")
    
    # 验证所有客户是否被分配
    assigned_customers = set()
    for route in routes:
        for node in route:
            if node != 0:
                assigned_customers.add(node)
    
    unassigned = set(customers) - assigned_customers
    if unassigned:
        print(f"  ⚠️  未分配客户: {unassigned}")
    else:
        print("  ✅ 所有客户已分配")

if __name__ == "__main__":
    # 设置随机种子以获得可重现的结果
    random.seed(42)
    demonstrate_savings_algorithm()
