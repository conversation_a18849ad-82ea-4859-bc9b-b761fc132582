# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)

# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成

if progress_ratio <= 0.2:
    ls_frequency = 30  # 前期
elif progress_ratio <= 0.6:
    ls_frequency = 25  # 中期
else:
    ls_frequency = 20  # 后期
========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1059.69
最小总成本: 976.93 (运行 2)
最大总成本: 1156.72 (运行 10)
总成本标准差: 50.33

========== 算法精度与稳定性分析 ==========
最大偏差: 179.79 (18.40%)
平均偏差: 82.76 (8.47%)
平均求解时间: 305.06秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000941 | 1062.97 |    0.00 | 302.54
     2 |        2 | 0.001024 |  976.93 |    0.00 | 305.31
     3 |        3 | 0.000894 | 1118.96 |    0.00 | 305.45
     4 |        4 | 0.000981 | 1019.19 |    0.00 | 302.40
     5 |        5 | 0.000936 | 1068.21 |    0.00 | 302.73
     6 |        6 | 0.000924 | 1082.06 |    0.00 | 302.28
     7 |        7 | 0.000963 | 1038.72 |    0.00 | 315.01
     8 |        8 | 0.000994 | 1006.42 |    0.00 | 310.54
     9 |        9 | 0.000937 | 1066.75 |    0.00 | 301.99
    10 |       10 | 0.000865 | 1156.72 |    0.00 | 302.32

最佳解详细信息:
运行ID: 2
适应度: 0.001024
总成本: 976.93
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 22, 58, 74, 26, 1, 28, 3, 24, 43, 0, 45, 49, 33, 2, 73, 11, 48, 35, 13, 36, 12, 37, 52, 14, 15, 39, 5, 51, 0, 44, 7, 70, 41, 53, 16, 40, 42, 30, 8, 71, 38, 80, 72, 32, 78, 31, 76, 77, 50, 79, 6, 0, 21, 4, 10, 18, 62, 19, 20, 34, 64, 47, 63, 61, 46, 56, 68, 57, 69, 29, 66, 60, 54, 55, 17, 27, 59, 25, 9, 75, 67, 65, 23, 0)
染色体 2: (0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 22, 58, 1, 28, 43, 0]
  无人机任务:
    从节点 58 发射无人机访问: [74, 26]
    从节点 28 发射无人机访问: [3, 24]
路线 2: [0, 45, 49, 2, 73, 35, 13, 36, 12, 37, 52, 14, 5, 51, 0]
  无人机任务:
    从节点 49 发射无人机访问: [33]
    从节点 73 发射无人机访问: [11, 48]
    从节点 14 发射无人机访问: [15, 39]
路线 3: [0, 44, 7, 70, 41, 40, 8, 38, 80, 78, 76, 50, 79, 6, 0]
  无人机任务:
    从节点 41 发射无人机访问: [53, 16]
    从节点 40 发射无人机访问: [42, 30]
    从节点 8 发射无人机访问: [71]
    从节点 80 发射无人机访问: [72, 32]
    从节点 78 发射无人机访问: [31]
    从节点 76 发射无人机访问: [77]
路线 4: [0, 21, 18, 62, 19, 64, 61, 68, 57, 69, 60, 55, 17, 25, 67, 65, 23, 0]
  无人机任务:
    从节点 21 发射无人机访问: [4, 10]
    从节点 19 发射无人机访问: [20, 34]
    从节点 64 发射无人机访问: [47, 63]
    从节点 61 发射无人机访问: [46, 56]
    从节点 69 发射无人机访问: [29, 66]
    从节点 60 发射无人机访问: [54]
    从节点 17 发射无人机访问: [27, 59]
    从节点 25 发射无人机访问: [9, 75]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000572 | 1749.15 |    0.00
  10 | 0.000581 | 1721.45 |    0.00
  20 | 0.000581 | 1721.45 |    0.00
  30 | 0.000593 | 1686.39 |    0.00
  40 | 0.000593 | 1686.39 |    0.00
  50 | 0.000595 | 1679.89 |    0.00
  60 | 0.000623 | 1605.00 |    0.00
  70 | 0.000635 | 1574.87 |    0.00
  80 | 0.000642 | 1558.83 |    0.00
  90 | 0.000657 | 1521.98 |    0.00
 100 | 0.000674 | 1484.14 |    0.00
 110 | 0.000685 | 1458.94 |    0.00
 120 | 0.000698 | 1432.62 |    0.00
 130 | 0.000731 | 1367.72 |    0.00
 140 | 0.000742 | 1347.16 |    0.00
 150 | 0.000748 | 1337.05 |    0.00
 160 | 0.000748 | 1336.90 |    0.00
 170 | 0.000748 | 1336.90 |    0.00
 180 | 0.000833 | 1200.38 |    0.00
 190 | 0.000837 | 1195.27 |    0.00
 200 | 0.000838 | 1193.81 |    0.00
 210 | 0.000839 | 1191.65 |    0.00
 220 | 0.000843 | 1186.07 |    0.00
 230 | 0.000849 | 1178.48 |    0.00
 240 | 0.000851 | 1174.56 |    0.00
 250 | 0.000851 | 1174.56 |    0.00
 260 | 0.000854 | 1171.46 |    0.00
 270 | 0.000854 | 1170.53 |    0.00
 280 | 0.000854 | 1170.53 |    0.00
 290 | 0.000854 | 1170.53 |    0.00
 300 | 0.000854 | 1170.53 |    0.00
 310 | 0.000854 | 1170.53 |    0.00
 320 | 0.000854 | 1170.53 |    0.00
 330 | 0.000854 | 1170.53 |    0.00
 340 | 0.000925 | 1081.13 |    0.00
 350 | 0.000925 | 1081.13 |    0.00
 360 | 0.000957 | 1045.23 |    0.00
 370 | 0.000960 | 1041.48 |    0.00
 380 | 0.001020 |  980.50 |    0.00
 390 | 0.001020 |  980.50 |    0.00
 400 | 0.001020 |  980.50 |    0.00
 410 | 0.001020 |  980.50 |    0.00
