  if progress_ratio <= 0.2:
  ls_frequency = 30  # 前期
  elif progress_ratio <= 0.6:
  ls_frequency = 25  # 中期
  else:
  ls_frequency = 20  # 后期


  # 自适应变异因子 - 随着迭代进行而减小
  self.mutation_factor = self.mutation_factor * (1 - 0.9 * elapsed_time / self.max_runtime)


  # 基础惩罚系数和参数
  namuda2 = 5.0  # 元/Wh
  gamma = 3.0
  alpha = 1.5


  # 计算各方法生成的数量
  savings_count = int(pop_size * 0.4)  # 50%使用改进的节约算法
  random_count = pop_size - savings_count  # 50%使用随机生成

========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1052.53
最小总成本: 933.41 (运行 9)
最大总成本: 1224.56 (运行 4)
总成本标准差: 79.40

========== 算法精度与稳定性分析 ==========
最大偏差: 291.15 (31.19%)
平均偏差: 119.12 (12.76%)
平均求解时间: 302.96秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000891 | 1122.84 |    0.00 | 304.64
     2 |        2 | 0.001048 |  954.47 |    0.00 | 304.10
     3 |        3 | 0.000963 | 1038.43 |    0.00 | 301.77
     4 |        4 | 0.000817 | 1224.56 |    0.00 | 301.60
     5 |        5 | 0.000930 | 1075.28 |    0.00 | 301.16
     6 |        6 | 0.000964 | 1036.98 |    0.00 | 303.93
     7 |        7 | 0.000949 | 1054.02 |    0.00 | 301.44
     8 |        8 | 0.000922 | 1084.80 |    0.00 | 307.92
     9 |        9 | 0.001071 |  933.41 |    0.00 | 301.38
    10 |       10 | 0.000999 | 1000.54 |    0.00 | 301.62

最佳解详细信息:
运行ID: 9
适应度: 0.001071
总成本: 933.41
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 16, 17, 77, 20, 21, 23, 25, 31, 30, 26, 29, 28, 27, 41, 40, 38, 36, 37, 39, 0, 34, 33, 32, 35, 46, 45, 47, 42, 43, 44, 54, 51, 48, 53, 55, 49, 50, 52, 80, 59, 63, 61, 58, 62, 56, 57, 64, 65, 66, 69, 67, 68, 70, 71, 72, 0, 76, 75, 74, 79, 78, 0, 18, 19, 22, 24, 11, 15, 14, 13, 12, 10, 9, 8, 7, 73, 5, 1, 60, 2, 4, 3, 6, 0)
染色体 2: (0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 16, 20, 23, 25, 26, 27, 41, 40, 38, 37, 39, 0]
  无人机任务:
    从节点 16 发射无人机访问: [17, 77]
    从节点 20 发射无人机访问: [21]
    从节点 25 发射无人机访问: [31, 30]
    从节点 26 发射无人机访问: [29, 28]
    从节点 38 发射无人机访问: [36]
路线 2: [0, 34, 33, 32, 46, 45, 43, 44, 54, 53, 50, 59, 63, 61, 62, 64, 65, 66, 69, 70, 71, 72, 0]
  无人机任务:
    从节点 32 发射无人机访问: [35]
    从节点 45 发射无人机访问: [47, 42]
    从节点 54 发射无人机访问: [51, 48]
    从节点 53 发射无人机访问: [55, 49]
    从节点 50 发射无人机访问: [52, 80]
    从节点 61 发射无人机访问: [58]
    从节点 62 发射无人机访问: [56, 57]
    从节点 69 发射无人机访问: [67, 68]
路线 3: [0, 76, 79, 78, 0]
  无人机任务:
    从节点 76 发射无人机访问: [75, 74]
路线 4: [0, 18, 19, 22, 24, 11, 13, 12, 10, 9, 8, 5, 1, 60, 3, 6, 0]
  无人机任务:
    从节点 11 发射无人机访问: [15, 14]
    从节点 8 发射无人机访问: [7, 73]
    从节点 60 发射无人机访问: [2, 4]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000526 | 1900.07 |    0.00
  10 | 0.000526 | 1900.07 |    0.00
  20 | 0.000526 | 1900.07 |    0.00
  30 | 0.000526 | 1900.07 |    0.00
  40 | 0.000526 | 1900.07 |    0.00
  50 | 0.000526 | 1900.07 |    0.00
  60 | 0.000526 | 1900.07 |    0.00
  70 | 0.000527 | 1897.48 |    0.00
  80 | 0.000533 | 1874.51 |    0.00
  90 | 0.000597 | 1674.74 |    0.00
 100 | 0.000597 | 1674.74 |    0.00
 110 | 0.000607 | 1646.28 |    0.00
 120 | 0.000607 | 1646.28 |    0.00
 130 | 0.000609 | 1641.82 |    0.00
 140 | 0.000616 | 1622.93 |    0.00
 150 | 0.000673 | 1486.55 |    0.00
 160 | 0.000673 | 1486.55 |    0.00
 170 | 0.000673 | 1485.81 |    0.00
 180 | 0.000679 | 1473.66 |    0.00
 190 | 0.000682 | 1467.17 |    0.00
 200 | 0.000688 | 1454.09 |    0.00
 210 | 0.000693 | 1442.38 |    0.00
 220 | 0.000697 | 1434.64 |    0.00
 230 | 0.000728 | 1373.37 |    0.00
 240 | 0.000728 | 1373.37 |    0.00
 250 | 0.000794 | 1260.02 |    0.00
 260 | 0.000794 | 1260.02 |    0.00
 270 | 0.000808 | 1237.14 |    0.00
 280 | 0.000809 | 1236.13 |    0.00
 290 | 0.000815 | 1226.81 |    0.00
 300 | 0.000922 | 1084.30 |    0.00
 310 | 0.000924 | 1082.08 |    0.00
 320 | 0.000924 | 1082.08 |    0.00
 330 | 0.000924 | 1082.08 |    0.00
 340 | 0.000924 | 1082.08 |    0.00
 350 | 0.000924 | 1082.08 |    0.00
 360 | 0.000960 | 1042.10 |    0.00
 370 | 0.000960 | 1042.10 |    0.00
 380 | 0.001003 |  996.66 |    0.00
 390 | 0.001003 |  996.66 |    0.00
 400 | 0.001018 |  982.26 |    0.00
 410 | 0.001023 |  977.55 |    0.00
 420 | 0.001056 |  946.74 |    0.00
 430 | 0.001065 |  939.00 |    0.00
 440 | 0.001071 |  933.41 |    0.00
 450 | 0.001071 |  933.41 |    0.00
