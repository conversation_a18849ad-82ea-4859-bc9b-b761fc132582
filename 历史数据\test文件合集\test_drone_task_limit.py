import os
import copy
import traceback
import time
import random
from data_loader import Problem, Individual
from repair import check_and_repair_drone_task_limit
import utils

def create_infeasible_solution(problem=None):
    """
    创建一个不可行解，其中某个发射点绑定的无人机任务数超过限制
    使用A-n32-k2-d4.vrp中的所有31个客户点
    
    参数:
    - problem: 问题实例（可选）
    
    返回:
    - 不可行解的Individual对象
    """
    print("创建包含所有31个客户点的不可行解...")
    
    # 创建路径层和服务方式层 - 避免连续的配送中心标记
    # 路径层结构: [0, ..., 0, ..., 0] 每个0代表配送中心，相邻0之间代表一条卡车路径
    path_layer = [0]  # 第一条路径的起始配送中心
    service_layer = [0]  # 配送中心的服务方式为0
    
    # 第一条路径：1是发射点，控制2,3,4,5（超过限制2个）
    path_layer.extend([1, 2, 3, 4, 5])  # 发射点1及无人机客户点
    service_layer.extend([0, 1, 1, 1, 1])  # 0=卡车服务(发射点)，1=无人机服务
    
    # 添加其他普通客户点到第一条路径
    path_layer.extend([6, 7, 8, 9, 10, 11, 12, 13, 14, 15]) 
    service_layer.extend([0, 0, 0, 0, 0, 0, 0, 0, 0, 0])  # 都是卡车服务
    
    # 第一条路径结束
    path_layer.append(0)  # 返回配送中心
    service_layer.append(0)  # 配送中心的服务方式为0
    
    # 第二条路径开始
    # 16是发射点，控制17,18,19,20（超过限制2个）
    path_layer.extend([16, 17, 18, 19, 20])
    service_layer.extend([0, 1, 1, 1, 1])
    
    # 21是发射点，控制22,23,24（超过限制2个）
    path_layer.extend([21, 22, 23, 24])
    service_layer.extend([0, 1, 1, 1])
    
    # 添加其他普通客户点到第二条路径
    path_layer.extend([25, 26, 27, 28, 29, 30, 31])
    service_layer.extend([0, 0, 0, 0, 0, 0, 0])
    
    # 第二条路径结束
    path_layer.append(0)  # 返回配送中心
    service_layer.append(0)  # 配送中心的服务方式为0
    
    # 将染色体封装成Individual对象
    individual = Individual(chromosomes=(path_layer, service_layer))
    
    # 输出信息验证不可行解的构造情况
    print("\n创建的不可行解:")
    print("路径层:", path_layer)
    print("服务方式层:", service_layer)
    print("发射点1控制的无人机客户点: 2, 3, 4, 5 (超过限制2)")
    print("发射点16控制的无人机客户点: 17, 18, 19, 20 (超过限制2)")
    print("发射点21控制的无人机客户点: 22, 23, 24 (超过限制2)")
    
    # 验证无人机任务分配是否正确
    print("\n验证无人机任务分配:")
    current_launch = None
    drone_tasks = {}
    
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        if service == 0 and node != 0:  # 卡车服务点(发射点)
            current_launch = node
            if current_launch not in drone_tasks:
                drone_tasks[current_launch] = []
        elif service == 1 and node != 0 and current_launch is not None:  # 无人机服务点
            drone_tasks[current_launch].append(node)
    
    for launch, tasks in drone_tasks.items():
        # print(f"发射点 {launch} 控制的无人机客户点: {tasks}")
    
        return individual

def test_repair_drone_task_limit():
    """测试check_and_repair_drone_task_limit函数的修复能力"""
    try:
        print("开始测试修复函数...")
        
        # 构建更细致的模拟问题实例，确保energy计算准确
        mock_problem = type('MockProblem', (), {
            'num_drones': 2,  # 设置每个发射点最多控制2个无人机
            'drone_params': {
                'max_load': 5.0,
                'mass': 4.0,
                'speed': 50.0,
                'energy_consumption_rate': 50.0,
                'battery': 200.0
            },
            'locations': {},
            'demands': {},
            'name': 'MockProblem with Better Energy Calc'
        })()
        
        # 使用固定种子生成坐标，使结果可重现
        random.seed(42)
        
        # 生成客户点坐标，使相邻客户点的距离不同，确保能耗计算有区分度
        mock_problem.locations[0] = (5.0, 5.0)  # 配送中心
        
        # 设置第一条路径的客户点坐标
        mock_problem.locations[1] = (3.0, 4.0)   # 发射点
        mock_problem.locations[2] = (10.0, 3.0)  # 高能耗点
        mock_problem.locations[3] = (9.0, 5.0)   # 中高能耗点
        mock_problem.locations[4] = (4.0, 3.0)   # 低能耗点 - 应保留
        mock_problem.locations[5] = (7.0, 7.0)   # 中能耗点 - 应保留
        
        # 设置其他客户点的随机坐标
        for i in range(6, 32):
            mock_problem.locations[i] = (random.uniform(0, 10), random.uniform(0, 10))
        
        # 设置第二条路径的重点客户点坐标
        mock_problem.locations[16] = (8.0, 9.0)   # 发射点
        mock_problem.locations[17] = (9.0, 9.5)   # 低能耗点 - 应保留
        mock_problem.locations[18] = (7.0, 8.0)   # 低能耗点 - 应保留
        mock_problem.locations[19] = (2.0, 2.0)   # 高能耗点
        mock_problem.locations[20] = (1.0, 9.0)   # 高能耗点
        
        mock_problem.locations[21] = (6.0, 2.0)   # 发射点
        mock_problem.locations[22] = (6.5, 2.5)   # 低能耗点 - 应保留
        mock_problem.locations[23] = (5.5, 1.5)   # 低能耗点 - 应保留
        mock_problem.locations[24] = (9.0, 9.0)   # 高能耗点
        
        # 生成客户点需求
        for i in range(32):
            mock_problem.demands[i] = (1.0, 1.0)  # 统一设置简单需求
        
        # 定义更准确的能耗计算函数
        def reliable_calc_drone_energy(problem, launch_node, target_node):
            """准确的能耗计算函数"""
            if launch_node in problem.locations and target_node in problem.locations:
                x1, y1 = problem.locations[launch_node]
                x2, y2 = problem.locations[target_node]
                distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
                
                # 基于距离计算能耗，确保不同客户点有明显的能耗差异
                drone_mass = problem.drone_params['mass']
                energy_rate = problem.drone_params['energy_consumption_rate']
                speed = problem.drone_params['speed']
                
                # 去程+返程能耗
                energy = 2 * (energy_rate * drone_mass * distance / speed)
                return energy
            return 100.0  # 默认高能耗
        
        # 修改utils.calc_drone_energy的实现
        original_calc_energy = utils.calc_drone_energy if hasattr(utils, 'calc_drone_energy') else None
        utils.calc_drone_energy = lambda prob, launch, target: reliable_calc_drone_energy(mock_problem, launch, target)
        
        problem = mock_problem
        print(f"模拟问题实例已创建: {problem.name}")
        print(f"每辆车的无人机数量限制: {problem.num_drones}")
        
        # 创建不可行解
        individual = create_infeasible_solution(problem)
        
        # 详细计算原始能耗，用于验证能耗修复逻辑
        print("\n原始无人机任务能耗计算:")
        compute_and_print_task_energies(problem, individual)
        
        # 分析不可行解中的无人机任务分配
        print("\n分析修复前的任务分配...")
        analyze_drone_tasks(individual, "修复前")
        
        # 保存原始染色体
        original_chromosomes = copy.deepcopy(individual.chromosomes)
        
        # 调用修复函数
        print("\n调用修复函数...")
        try:
            result = check_and_repair_drone_task_limit(problem, individual)
            print("修复函数调用成功")
            print(individual.chromosomes)
        except Exception as e:
            print(f"修复函数调用失败: {e}")
            traceback.print_exc()
            print("尝试继续测试...")
        
        # 打印修复结果
        print("\n修复结果:", "可行" if result else "已修复不可行解")
        
        # 分析修复后的无人机任务分配
        analyze_drone_tasks(individual, "修复后")
        
        # 比较修复前后的差异
        path_layer_before, service_layer_before = original_chromosomes
        path_layer_after, service_layer_after = individual.chromosomes
        
        # 检查是否有任何变化
        if path_layer_before == path_layer_after and service_layer_before == service_layer_after:
            print("\n警告: 染色体在修复前后完全相同，修复可能未生效!")
        
        # 由于修复可能会改变服务方式层，我们找出哪些客户点的服务方式发生了变化
        changes = []
        for i, (before, after) in enumerate(zip(service_layer_before, service_layer_after)):
            if before != after:
                node = path_layer_after[i]
                changes.append((i, node, before, after))
        
        # print("\n服务方式变化:")
        # if changes:
        #     for idx, node, before, after in changes:
        #         print(f"  位置 {idx}: 客户点 {node} 从 {'无人机服务' if before==1 else '卡车服务'} 变为 {'无人机服务' if after==1 else '卡车服务'}")
        # else:
        #     print("  无服务方式变化，分析发射点的无人机任务分配是否变化...")
        #     analyze_task_reallocation(problem, individual, original_chromosomes)
        
        # # 详细计算修复后能耗
        # print("\n修复后无人机任务能耗:")
        # compute_and_print_task_energies(problem, individual)
        
        # # 恢复原始的能耗计算函数
        # if original_calc_energy:
        #     utils.calc_drone_energy = original_calc_energy
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        traceback.print_exc()

def compute_and_print_task_energies(problem, individual):
    """计算并打印每个发射点的无人机任务能耗"""
    path_layer, service_layer = individual.chromosomes
    
    # 提取发射点及其控制的无人机客户点
    launch_points = {}
    current_launch = None
    
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        if service == 0 and node != 0:  # 卡车服务点(发射点)
            current_launch = node
            launch_points[current_launch] = []
        elif service == 1 and node != 0 and current_launch is not None:  # 无人机服务点
            launch_points[current_launch].append(node)
    
    # 计算每个发射点的任务能耗
    # for launch_node, tasks in launch_points.items():
    #     if tasks:  # 只打印有任务的发射点
    #         print(f"发射点 {launch_node} 控制的无人机任务能耗:")
    #         task_energies = []
            
    #         for drone_node in tasks:
    #             try:
    #                 energy = utils.calc_drone_energy(problem, launch_node, drone_node)
    #                 task_energies.append((drone_node, energy))
    #                 print(f"  客户点 {drone_node}: 能耗 {energy:.2f} Wh")
    #             except Exception as e:
    #                 print(f"  计算客户点 {drone_node} 能耗时出错: {e}")
            
    #         # 按能耗排序
    #         if task_energies:
    #             task_energies.sort(key=lambda x: x[1])
    #             print(f"  按能耗排序: {[(n, round(e, 2)) for n, e in task_energies]}")
                
    #             # 如果发射点超过限制，明确指出哪些应该保留/移除
    #             if len(tasks) > problem.num_drones:
    #                 kept = [n for n, _ in task_energies[:problem.num_drones]]
    #                 removed = [n for n, _ in task_energies[problem.num_drones:]]
    #                 print(f"  超出限制(最多{problem.num_drones}个): 应保留 {kept}，应移除 {removed}")

def analyze_drone_tasks(individual, label):
    """分析个体中的无人机任务分配情况"""
    path_layer, service_layer = individual.chromosomes
    
    # 提取发射点及其控制的无人机客户点
    launch_points = {}
    current_launch = None
    
    for i, (node, service) in enumerate(zip(path_layer, service_layer)):
        if service == 0 and node != 0:  # 卡车服务点(发射点)
            current_launch = node
            launch_points[current_launch] = []
        elif service == 1 and node != 0 and current_launch is not None:  # 无人机服务点
            launch_points[current_launch].append(node)
    
    # print(f"\n{label}的无人机任务分配:")
    # over_limit = 0
    
    # for launch_node, tasks in launch_points.items():
    #     status = ""
    #     if len(tasks) > 2:  # 假设最大无人机数为2
    #         status = " (超过限制!)"
    #         over_limit += 1
    #     print(f"  发射点 {launch_node} 控制的无人机客户点: {tasks} (共{len(tasks)}个){status}")
    
    # if over_limit > 0:
    #     print(f"  警告: 有 {over_limit} 个发射点的无人机数量超过限制!")
    # else:
    #     print("  所有发射点的无人机数量均在限制内")

def analyze_task_reallocation(problem, individual, original_chromosomes):
    """分析无人机任务是否在发射点之间重新分配了"""
    orig_path, orig_service = original_chromosomes
    curr_path, curr_service = individual.chromosomes
    
    # 提取修复前的发射点及其任务
    orig_tasks = {}
    current_launch = None
    
    for i, (node, service) in enumerate(zip(orig_path, orig_service)):
        if service == 0 and node != 0:  # 卡车服务点(发射点)
            current_launch = node
            orig_tasks[current_launch] = []
        elif service == 1 and node != 0 and current_launch is not None:  # 无人机服务点
            orig_tasks[current_launch].append(node)
    
    # 提取修复后的发射点及其任务
    curr_tasks = {}
    current_launch = None
    
    for i, (node, service) in enumerate(zip(curr_path, curr_service)):
        if service == 0 and node != 0:  # 卡车服务点(发射点)
            current_launch = node
            curr_tasks[current_launch] = []
        elif service == 1 and node != 0 and current_launch is not None:  # 无人机服务点
            curr_tasks[current_launch].append(node)
    
    # 比较任务分配
    print("\n任务重分配分析:")
    changes_found = False
    
    # 检查每个发射点的任务变化
    all_launch_points = set(orig_tasks.keys()) | set(curr_tasks.keys())
    
    for launch in all_launch_points:
        orig_list = sorted(orig_tasks.get(launch, []))
        curr_list = sorted(curr_tasks.get(launch, []))
        
        if orig_list != curr_list:
            changes_found = True
            lost_tasks = [t for t in orig_list if t not in curr_list]
            new_tasks = [t for t in curr_list if t not in orig_list]
            
            print(f"  发射点 {launch} 的任务变化:")
            print(f"    原始任务: {orig_list}")
            print(f"    当前任务: {curr_list}")
            if lost_tasks:
                print(f"    失去的任务: {lost_tasks}")
            if new_tasks:
                print(f"    新增的任务: {new_tasks}")
    
    if not changes_found:
        print("  未检测到任务重分配")
        
        # 检查是否有超限发射点
        over_limit = [launch for launch, tasks in curr_tasks.items() if len(tasks) > problem.num_drones]
        if over_limit:
            print(f"  警告: 仍有 {len(over_limit)} 个发射点超过限制: {over_limit}")
            print("  修复可能未正确执行或函数存在问题")

if __name__ == "__main__":
    test_repair_drone_task_limit()

