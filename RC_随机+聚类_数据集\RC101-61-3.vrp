NAME : RC101-61-3
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 61
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 60

NODE_COORD_SECTION
 0 8.0 10.0
 1 4.0 16.0
 2 4.0 17.0
 3 3.6 15.0
 4 3.0 15.0
 5 3.0 16.0
 6 2.0 7.0
 7 2.0 8.0
 8 1.6 8.0
 9 1.6 9.0
 10 1.0 7.0
 11 1.0 9.0
 12 0.4 8.0
 13 0.0 8.0
 14 8.4 3.0
 15 8.0 3.0
 16 7.6 1.0
 17 7.6 3.0
 18 7.0 1.0
 19 19.0 6.0
 20 19.0 7.0
 21 18.4 6.0
 22 17.6 6.0
 23 17.0 7.0
 24 13.4 17.0
 25 13.0 16.4
 26 11.6 15.0
 27 11.0 16.0
 28 11.0 17.0
 29 4.0 16.4
 30 3.6 16.0
 31 8.4 1.0
 32 8.4 2.4
 33 5.0 6.0
 34 4.0 10.0
 35 6.0 12.0
 36 10.0 7.0
 37 6.0 5.0
 38 3.0 2.0
 39 3.0 12.0
 40 7.0 8.0
 41 8.2 7.4
 42 8.0 12.0
 43 7.0 13.8
 44 12.6 13.0
 45 4.0 4.0
 46 1.0 1.0
 47 12.0 2.4
 48 4.6 0.6
 49 1.2 13.6
 50 9.4 9.4
 51 7.4 6.2
 52 12.6 4.6
 53 4.2 4.8
 54 4.8 11.6
 55 13.4 1.0
 56 10.6 8.6
 57 11.4 9.6
 58 11.2 7.4
 59 11.0 10.8
 60 5.2 7.0

DEMAND_SECTION
 0 0 0      
 1 2.4 0.0      // 仅送货需求客户 - light包裹
 2 3.2 3.0      // 送货取货双需求客户 - light包裹
 3 1.2 1.2      // 送货取货双需求客户 - light包裹
 4 9.2 0.0      // 仅送货需求客户 - medium包裹
 5 4.7 0.0      // 仅送货需求客户 - light包裹
 6 0.0 3.9      // 仅取货需求客户 - light包裹
 7 19.3 6.2      // 送货取货双需求客户 - heavy包裹
 8 19.8 0.0      // 仅送货需求客户 - heavy包裹
 9 1.5 0.0      // 仅送货需求客户 - light包裹
 10 4.5 1.2      // 送货取货双需求客户 - light包裹
 11 4.1 4.0      // 送货取货双需求客户 - light包裹
 12 2.3 1.8      // 送货取货双需求客户 - light包裹
 13 0.0 7.9      // 仅取货需求客户 - medium包裹
 14 0.0 1.4      // 仅取货需求客户 - light包裹
 15 0.0 3.3      // 仅取货需求客户 - light包裹
 16 3.2 0.0      // 仅送货需求客户 - light包裹
 17 3.5 2.4      // 送货取货双需求客户 - light包裹
 18 2.9 0.0      // 仅送货需求客户 - light包裹
 19 4.2 0.0      // 仅送货需求客户 - light包裹
 20 0.0 3.8      // 仅取货需求客户 - light包裹
 21 4.5 3.8      // 送货取货双需求客户 - light包裹
 22 3.7 0.0      // 仅送货需求客户 - light包裹
 23 4.7 3.5      // 送货取货双需求客户 - light包裹
 24 8.6 0.0      // 仅送货需求客户 - medium包裹
 25 4.9 0.0      // 仅送货需求客户 - light包裹
 26 3.5 0.0      // 仅送货需求客户 - light包裹
 27 4.2 0.0      // 仅送货需求客户 - light包裹
 28 3.9 0.0      // 仅送货需求客户 - light包裹
 29 0.0 15.7      // 仅取货需求客户 - heavy包裹
 30 3.3 2.8      // 送货取货双需求客户 - light包裹
 31 2.2 0.0      // 仅送货需求客户 - light包裹
 32 1.3 0.0      // 仅送货需求客户 - light包裹
 33 2.9 2.4      // 送货取货双需求客户 - light包裹
 34 2.9 0.0      // 仅送货需求客户 - light包裹
 35 2.1 0.0      // 仅送货需求客户 - light包裹
 36 2.8 1.6      // 送货取货双需求客户 - light包裹
 37 3.5 2.5      // 送货取货双需求客户 - light包裹
 38 6.3 0.0      // 仅送货需求客户 - medium包裹
 39 5.0 0.0      // 仅送货需求客户 - light包裹
 40 0.0 19.2      // 仅取货需求客户 - heavy包裹
 41 3.0 0.0      // 仅送货需求客户 - light包裹
 42 3.8 0.0      // 仅送货需求客户 - light包裹
 43 9.0 0.0      // 仅送货需求客户 - medium包裹
 44 2.9 0.0      // 仅送货需求客户 - light包裹
 45 3.9 0.0      // 仅送货需求客户 - light包裹
 46 2.7 0.0      // 仅送货需求客户 - light包裹
 47 0.0 1.7      // 仅取货需求客户 - light包裹
 48 0.0 4.2      // 仅取货需求客户 - light包裹
 49 1.8 1.1      // 送货取货双需求客户 - light包裹
 50 5.0 0.0      // 仅送货需求客户 - light包裹
 51 0.0 5.0      // 仅取货需求客户 - light包裹
 52 0.0 3.8      // 仅取货需求客户 - light包裹
 53 0.0 4.5      // 仅取货需求客户 - light包裹
 54 1.5 1.4      // 送货取货双需求客户 - light包裹
 55 19.6 0.0      // 仅送货需求客户 - heavy包裹
 56 3.0 0.0      // 仅送货需求客户 - light包裹
 57 2.8 0.0      // 仅送货需求客户 - light包裹
 58 3.1 0.0      // 仅送货需求客户 - light包裹
 59 15.5 0.0      // 仅送货需求客户 - heavy包裹
 60 4.4 0.0      // 仅送货需求客户 - light包裹

DEPOT_SECTION
 0
-1
EOF