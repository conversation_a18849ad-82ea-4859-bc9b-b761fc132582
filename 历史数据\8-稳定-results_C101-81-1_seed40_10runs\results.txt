# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.4 * elapsed_time / self.max_runtime)



if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.5:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期


# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成


# 基础惩罚系数和参数
namuda2 = 5.0  # 元/Wh
gamma = 3.0
alpha = 1.5


# 确定局部搜索概率（随着进化进行逐渐增加）
base_probability = 0.1
adaptive_factor = current_gen / max_gen
search_probability = base_probability + (0.6 * adaptive_factor)

# 确定应用局部搜索的个体数量，采用自适应策略
max_elite_percentage = 0.2
min_elite_percentage = 0.05
elite_percentage = min_elite_percentage + (max_elite_percentage - min_elite_percentage) * adaptive_factor
elite_count = max(1, int(len(population) * elite_percentage))

========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 40
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1044.71
最小总成本: 1011.00 (运行 9)
最大总成本: 1147.42 (运行 10)
总成本标准差: 37.11

========== 算法精度与稳定性分析 ==========
最大偏差: 136.42 (13.49%)
平均偏差: 33.71 (3.33%)
平均求解时间: 302.66秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |       41 | 0.000966 | 1035.13 |    0.00 | 302.14
     2 |       42 | 0.000946 | 1057.04 |    0.00 | 302.17
     3 |       43 | 0.000946 | 1056.69 |    0.00 | 302.05
     4 |       44 | 0.000971 | 1029.67 |    0.00 | 302.15
     5 |       45 | 0.000965 | 1036.27 |    0.00 | 301.65
     6 |       46 | 0.000980 | 1020.19 |    0.00 | 302.03
     7 |       47 | 0.000966 | 1035.71 |    0.00 | 301.84
     8 |       48 | 0.000982 | 1017.96 |    0.00 | 301.82
     9 |       49 | 0.000989 | 1011.00 |    0.00 | 308.68
    10 |       50 | 0.000872 | 1147.42 |    0.00 | 302.02

最佳解详细信息:
运行ID: 9
适应度: 0.000989
总成本: 1011.00
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 17, 24, 31, 30, 29, 27, 26, 25, 28, 41, 39, 34, 54, 51, 48, 61, 63, 58, 56, 57, 64, 62, 0, 50, 52, 49, 55, 53, 59, 70, 69, 66, 65, 67, 68, 71, 72, 0, 19, 22, 11, 15, 14, 10, 13, 12, 76, 75, 73, 74, 77, 79, 80, 78, 2, 1, 60, 6, 4, 3, 5, 7, 9, 8, 0, 16, 18, 20, 21, 23, 37, 36, 40, 38, 35, 46, 44, 43, 45, 47, 42, 32, 33, 0)
染色体 2: (0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 17, 24, 31, 29, 27, 26, 25, 41, 39, 34, 54, 51, 48, 61, 63, 58, 56, 64, 0]
  无人机任务:
    从节点 31 发射无人机访问: [30]
    从节点 25 发射无人机访问: [28]
    从节点 56 发射无人机访问: [57]
    从节点 64 发射无人机访问: [62]
路线 2: [0, 50, 52, 49, 59, 70, 69, 66, 68, 71, 72, 0]
  无人机任务:
    从节点 49 发射无人机访问: [55, 53]
    从节点 66 发射无人机访问: [65, 67]
路线 3: [0, 19, 11, 10, 76, 75, 73, 74, 77, 78, 2, 60, 6, 5, 7, 0]
  无人机任务:
    从节点 19 发射无人机访问: [22]
    从节点 11 发射无人机访问: [15, 14]
    从节点 10 发射无人机访问: [13, 12]
    从节点 77 发射无人机访问: [79, 80]
    从节点 2 发射无人机访问: [1]
    从节点 6 发射无人机访问: [4, 3]
    从节点 7 发射无人机访问: [9, 8]
路线 4: [0, 16, 18, 20, 37, 36, 35, 46, 43, 45, 32, 33, 0]
  无人机任务:
    从节点 20 发射无人机访问: [21, 23]
    从节点 36 发射无人机访问: [40, 38]
    从节点 46 发射无人机访问: [44]
    从节点 45 发射无人机访问: [47, 42]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000522 | 1915.52 |    0.00
  10 | 0.000522 | 1915.52 |    0.00
  20 | 0.000522 | 1915.52 |    0.00
  30 | 0.000522 | 1915.52 |    0.00
  40 | 0.000522 | 1915.52 |    0.00
  50 | 0.000522 | 1915.52 |    0.00
  60 | 0.000543 | 1841.42 |    0.00
  70 | 0.000543 | 1841.42 |    0.00
  80 | 0.000551 | 1815.42 |    0.00
  90 | 0.000551 | 1815.42 |    0.00
 100 | 0.000551 | 1815.42 |    0.00
 110 | 0.000576 | 1735.43 |    0.00
 120 | 0.000576 | 1735.43 |    0.00
 130 | 0.000635 | 1575.76 |    0.00
 140 | 0.000648 | 1543.46 |    0.00
 150 | 0.000668 | 1497.84 |    0.00
 160 | 0.000668 | 1497.84 |    0.00
 170 | 0.000668 | 1496.53 |    0.00
 180 | 0.000748 | 1336.60 |    0.00
 190 | 0.000748 | 1336.60 |    0.00
 200 | 0.000759 | 1317.30 |    0.00
 210 | 0.000759 | 1317.30 |    0.00
 220 | 0.000764 | 1308.83 |    0.00
 230 | 0.000769 | 1299.58 |    0.00
 240 | 0.000796 | 1255.83 |    0.00
 250 | 0.000796 | 1255.83 |    0.00
 260 | 0.000803 | 1245.59 |    0.00
 270 | 0.000803 | 1245.59 |    0.00
 280 | 0.000803 | 1244.95 |    0.00
 290 | 0.000804 | 1244.47 |    0.00
 300 | 0.000839 | 1192.32 |    0.00
 310 | 0.000839 | 1192.32 |    0.00
 320 | 0.000839 | 1192.32 |    0.00
 330 | 0.000839 | 1192.32 |    0.00
 340 | 0.000839 | 1192.32 |    0.00
 350 | 0.000839 | 1192.32 |    0.00
 360 | 0.000894 | 1118.97 |    0.00
 370 | 0.000894 | 1118.97 |    0.00
 380 | 0.000922 | 1085.17 |    0.00
 390 | 0.000922 | 1085.17 |    0.00
