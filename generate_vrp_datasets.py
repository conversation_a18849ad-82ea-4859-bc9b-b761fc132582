import random
import os
import re
from typing import List, Dict, Tu<PERSON>, Set

def extract_data_from_vrp(file_path: str) -> Tu<PERSON>[List[str], List[List[str]], List[List[str]]]:
    """
    从VRP文件中提取头部信息、节点坐标和需求数据
    
    参数:
        file_path: VRP文件路径
    
    返回:
        (header_lines, node_coords, demand_data): 头部信息行、节点坐标数据、需求数据的元组
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 去除每行末尾的空白字符
    lines = [line.rstrip() for line in lines]
    
    # 提取头部信息，节点坐标和需求数据
    header_lines = []
    node_coords = []
    demand_data = []
    
    section = "header"
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        if "NODE_COORD_SECTION" in line:
            section = "node_coords"
            header_lines.append(line)
            continue
        elif "DEMAND_SECTION" in line:
            section = "demand"
            continue
        elif "DEPOT_SECTION" in line:
            section = "depot"
            continue
        
        if section == "header":
            header_lines.append(line)
        elif section == "node_coords":
            parts = re.split(r'\s+', line.strip())
            if len(parts) >= 3 and parts[0].isdigit():
                node_coords.append(parts)
        elif section == "demand":
            parts = line.split("//", 1)
            data = parts[0].strip()
            comment = parts[1].strip() if len(parts) > 1 else ""
            demand_parts = re.split(r'\s+', data.strip())
            
            if len(demand_parts) >= 3 and demand_parts[0].isdigit():
                # 保存需求数据和注释
                if comment:
                    demand_parts.append("//" + comment)
                demand_data.append(demand_parts)
    
    return header_lines, node_coords, demand_data

def generate_dataset(source_file: str, output_file: str, num_nodes: int, depot_id: int = 0):
    """
    从源文件生成特定节点数量的新数据集
    
    参数:
        source_file: 源VRP文件路径
        output_file: 输出文件路径
        num_nodes: 需要的节点数量（包括配送中心）
        depot_id: 配送中心的ID，默认为0
    """
    # 提取源文件数据
    header_lines, node_coords, demand_data = extract_data_from_vrp(source_file)
    
    # 分离配送中心和客户点
    depot_coords = None
    depot_demand = None
    customer_coords = []
    customer_demands = []
    
    for node in node_coords:
        if int(node[0]) == depot_id:
            depot_coords = node
        else:
            customer_coords.append(node)
    
    for demand in demand_data:
        if int(demand[0]) == depot_id:
            depot_demand = demand
        else:
            customer_demands.append(demand)
    
    # 确保客户点坐标和需求数据一一对应
    customer_data = list(zip(customer_coords, customer_demands))
    
    # 随机选择指定数量的客户点
    num_customers = num_nodes - 1  # 减去配送中心
    selected_customers = random.sample(customer_data, num_customers)
    
    # 重新编号
    selected_node_coords = [depot_coords]
    selected_demand_data = [depot_demand]
    
    for i, (coord, demand) in enumerate(selected_customers):
        new_id = i + 1  # 客户编号从1开始
        
        # 更新坐标ID
        new_coord = [str(new_id)] + coord[1:]
        selected_node_coords.append(new_coord)
        
        # 更新需求ID
        comment = None
        for j, value in enumerate(demand):
            if value.startswith("//"):
                comment = value
                demand = demand[:j]
                break
        
        new_demand = [str(new_id)] + demand[1:]
        if comment:
            new_demand.append(comment)
        selected_demand_data.append(new_demand)
    
    # 更新头部信息
    new_header_lines = []
    for line in header_lines:
        if "NAME" in line:
            # 使用新的输出文件名作为数据集名称
            base_name = os.path.basename(output_file).replace(".vrp", "")
            new_header_lines.append(f"NAME : {base_name}")
        elif "DIMENSION" in line:
            new_header_lines.append(f"DIMENSION : {num_nodes}")
        elif "NUM_CUSTOMERS" in line:
            new_header_lines.append(f"NUM_CUSTOMERS : {num_customers}")
        else:
            new_header_lines.append(line)
    
    # 写入新文件
    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入头部
        for line in new_header_lines:
            f.write(line + "\n")
        
        # 写入节点坐标
        f.write("\nNODE_COORD_SECTION\n")
        for node in selected_node_coords:
            f.write(" " + " ".join(node) + "\n")
        
        # 写入需求数据
        f.write("\nDEMAND_SECTION\n")
        for demand in selected_demand_data:
            # 检查是否有注释
            comment_part = ""
            for i, part in enumerate(demand):
                if part.startswith("//"):
                    comment_part = demand[i:]
                    demand = demand[:i]
                    break
            
            # 写入需求数据
            if comment_part:
                # 保持原始格式对齐
                demand_str = " " + " ".join(demand)
                comment_str = "      " + " ".join(comment_part)
                f.write(f"{demand_str}{comment_str}\n")
            else:
                f.write(" " + " ".join(demand) + "\n")
        
        # 写入配送中心和文件结束标记
        f.write("\nDEPOT_SECTION\n")
        f.write(" 0\n")
        f.write(" 0\n")
        f.write("EOF\n")
    
    print(f"已生成数据集: {output_file}")

def main():
    # 设置随机种子以保证可重复性（如果需要）
    random.seed(42)
    
    source_file = "1_Solomon_R101-converted.vrp"
    
    # 要生成的节点数量列表 (包含配送中心)
    node_counts = [21, 26, 41, 51, 81]
    
    # 为每个节点数量生成3个不同的数据集
    for num_nodes in node_counts:
        for i in range(1, 4):  # 1到3，生成3个不同数据集
            # 改变随机种子以获得不同的选择
            random.seed(42 + (num_nodes * 10) + i)
            
            # 构建输出文件名
            output_file = f"R101-{num_nodes}-{i}.vrp"
            
            # 生成数据集
            generate_dataset(source_file, output_file, num_nodes)

if __name__ == "__main__":
    main()
