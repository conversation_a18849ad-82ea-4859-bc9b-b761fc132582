import random
import copy
from typing import List, Tuple, Dict, Any, Optional
from data_loader import Individual, Problem
from utils import calculate_total_cost, euclidean_distance
from repair import (
    check_and_repair_drone_launch_point,
    check_and_repair_drone_task_limit,
    check_and_repair_service_type,
    check_and_repair_drone_payload,
    check_and_repair_path_separation
)

def max_route_destroy(individual: Individual, problem: Problem, beta: Optional[int] = None) -> Tuple[List[int], List[int], List[int]]:
    """
    最大路径删除算子：直接操作包含配送中心的路径层
    
    Args:
        individual: 染色体对象，包含chromosomes属性
        problem: 问题实例，用于解码和评估
        beta: 允许移除的最大客户点数量
        
    Returns:
        tuple: (removed_customers, remaining_customers, remaining_services)
            - removed_customers: 被移除的客户点列表
            - remaining_customers: 剩余的客户点列表（保持原有顺序，包含配送中心）
            - remaining_services: 剩余客户点对应的服务方式列表
    """
    # 获取路径层信息 
    path_layer, service_layer = individual.chromosomes
    
    # 解码染色体，获取无人机任务信息
    decoded_solution = Individual.decode(individual.chromosomes, problem)
    
    # 合并所有无人机任务到一个字典
    drone_tasks = {}
    for _, tasks in decoded_solution:
        for launch_node, targets in tasks.items():
            if launch_node in drone_tasks:
                drone_tasks[launch_node].extend(targets)
            else:
                drone_tasks[launch_node] = targets
    
    # 创建可以移除的客户点索引列表（排除配送中心）
    removable_indices = [i for i, node in enumerate(path_layer) if node != 0]
    
    # 统计非配送中心节点数量，用于计算beta
    customer_count = len(removable_indices)
    
    # 若beta未指定，使用默认值20%的客户点
    if beta is None:
        beta = max(1, int(customer_count * 0.2))
    
    # 如果没有客户点，直接返回原始路径
    if not removable_indices:
        return [], list(path_layer), list(service_layer)
    
    # 初始化结果列表
    removed_customers = []
    removed_indices = []
    
    # 在removable_indices中选择连续段
    # 子路径长度α需小于β
    alpha = random.randint(1, min(beta, len(removable_indices)))
    
    # 随机选择子路径的起始位置
    if alpha >= len(removable_indices):
        # 如果子路径长度等于或超过总客户数，全部移除
        removed_indices = removable_indices.copy()
        removed_customers = [path_layer[idx] for idx in removed_indices]
    else:
        # 随机选择起始位置
        start_pos = random.randint(0, len(removable_indices) - alpha)
        # 获取连续的索引段
        segment_indices = removable_indices[start_pos:start_pos + alpha]
        removed_indices = segment_indices
        removed_customers = [path_layer[idx] for idx in removed_indices]
    
    # 处理无人机任务依赖关系
    i = 0
    while i < len(removable_indices):
        if removable_indices[i] in removed_indices:
            i += 1
            continue
            
        idx = removable_indices[i]
        customer = path_layer[idx]
        should_remove = False
        
        # 检查是否是被移除发射点的无人机客户
        for launch_node in removed_customers:
            if launch_node in drone_tasks and customer in drone_tasks[launch_node]:
                should_remove = True
                break
        
        if should_remove:
            removed_indices.append(idx)
            removed_customers.append(customer)
            i += 1
        else:
            i += 1
    
    # 构建remaining_customers和remaining_services
    remaining_customers = []
    remaining_services = []
    
    for i, (customer, service) in enumerate(zip(path_layer, service_layer)):
        if i not in removed_indices:
            remaining_customers.append(customer)
            remaining_services.append(service)
    
    return removed_customers, remaining_customers, remaining_services

def cluster_destroy(individual: Individual, problem: Problem, beta: Optional[int] = None) -> Tuple[List[int], List[int], List[int]]:
    """
    群集破坏算子：直接操作包含配送中心的路径层
    
    Args:
        individual: 染色体对象，包含chromosomes属性
        problem: 问题实例，用于解码和评估
        beta: 需要移除的客户点数量，若为None则通过轮盘赌自动计算
        
    Returns:
        tuple: (removed_customers, remaining_customers, remaining_services)
            - removed_customers: 被移除的客户点列表
            - remaining_customers: 剩余的客户点列表（保持原有顺序，包含配送中心）
            - remaining_services: 剩余客户点对应的服务方式列表
    """
    # 获取路径层信息 
    path_layer, service_layer = individual.chromosomes
    
    # 解码染色体，获取无人机任务信息
    decoded_solution = Individual.decode(individual.chromosomes, problem)
    
    # 合并所有无人机任务到一个字典
    drone_tasks = {}
    for _, tasks in decoded_solution:
        for launch_node, targets in tasks.items():
            if launch_node in drone_tasks:
                drone_tasks[launch_node].extend(targets)
            else:
                drone_tasks[launch_node] = targets
    
    # 创建可以移除的客户点索引列表和值映射（排除配送中心）
    removable_indices = [i for i, node in enumerate(path_layer) if node != 0]
    index_to_node = {i: path_layer[i] for i in removable_indices}
    node_to_index = {path_layer[i]: i for i in removable_indices}
    
    # 统计非配送中心节点数量，用于计算beta
    customer_count = len(removable_indices)
    
    # 若beta未指定，使用轮盘赌策略自动计算beta值
    if beta is None:
        # 定义四个移除比率δ及其权重
        removal_rates = [0.1, 0.2, 0.3, 0.4]  # δ₁, δ₂, δ₃, δ₄
        weights = [0.4, 0.3, 0.2, 0.1]  # 权重递减，更倾向于小幅度破坏
        
        # 计算累积权重
        cumulative_weights = []
        cum_sum = 0
        for w in weights:
            cum_sum += w
            cumulative_weights.append(cum_sum)
            
        # 生成0到1之间的随机数
        r = random.random()
        
        # 轮盘赌选择移除比率
        selected_rate = removal_rates[0]  # 默认移除比率等于δ₁
        for i, cum_weight in enumerate(cumulative_weights):
            if r <= cum_weight:
                selected_rate = removal_rates[i]
                break
                
        # 计算beta值并四舍五入为整数
        beta = round(customer_count * selected_rate)
        
        # 确保至少移除一个客户点
        beta = max(1, beta)
    
    # 初始化结果列表
    removed_customers = []
    removed_indices = []
    removed_count = 0
    
    # 如果没有客户点或beta为0，直接返回原始路径
    if not removable_indices or beta <= 0:
        return [], list(path_layer), list(service_layer)
    
    # 随机选择初始焦点客户点索引
    focus_pos = random.randint(0, len(removable_indices) - 1)
    focus_idx = removable_indices.pop(focus_pos)
    focus = path_layer[focus_idx]
    
    removed_indices.append(focus_idx)
    removed_customers.append(focus)
    removed_count += 1
    
    # 检查被移除的客户是否为无人机发射点
    if focus in drone_tasks:
        # 如果是发射点，则移除其对应的所有无人机客户点
        for drone_customer in drone_tasks[focus]:
            if drone_customer in node_to_index and node_to_index[drone_customer] not in removed_indices:
                drone_idx = node_to_index[drone_customer]
                if drone_idx in removable_indices:
                    removable_indices.remove(drone_idx)
                removed_indices.append(drone_idx)
                removed_customers.append(drone_customer)
                removed_count += 1
    
    # 开始群集移除过程
    while removed_count < beta and removable_indices:
        # 如果只剩一个客户点，直接移除
        if len(removable_indices) == 1:
            last_idx = removable_indices.pop(0)
            last_customer = path_layer[last_idx]
            removed_indices.append(last_idx)
            removed_customers.append(last_customer)
            removed_count += 1
            
            # 检查是否为无人机发射点
            if last_customer in drone_tasks:
                for drone_customer in drone_tasks[last_customer]:
                    if drone_customer in node_to_index and node_to_index[drone_customer] not in removed_indices:
                        drone_idx = node_to_index[drone_customer]
                        if drone_idx in removable_indices:
                            removable_indices.remove(drone_idx)
                        removed_indices.append(drone_idx)
                        removed_customers.append(drone_customer)
                        removed_count += 1
            break
        
        # 计算所有剩余客户点到当前焦点的距离
        distances = []
        for i, idx in enumerate(removable_indices):
            customer = path_layer[idx]
            if focus in problem.locations and customer in problem.locations:
                # 使用真实欧几里得距离
                dist = euclidean_distance(
                    problem.locations[focus],
                    problem.locations[customer]
                )
            else:
                # 使用客户点ID的差值作为距离的替代
                dist = abs(customer - focus)
            
            distances.append((i, idx, customer, dist))
        
        # 按距离排序（从近到远）
        distances.sort(key=lambda x: x[3])
        
        # 选择最近的两个客户点（形成最近节点集合）
        nearest_indices = [distances[0][1], distances[1][1]]
        
        # 从最近节点集合中随机选择一个作为新的焦点
        next_idx = random.choice(nearest_indices)
        next_focus = path_layer[next_idx]
        
        # 找出新焦点在removable_indices中的位置
        next_focus_pos = removable_indices.index(next_idx)
        
        # 移除新焦点
        removable_indices.pop(next_focus_pos)
        removed_indices.append(next_idx)
        removed_customers.append(next_focus)
        removed_count += 1
        focus = next_focus
        
        # 检查新焦点是否为无人机发射点
        if focus in drone_tasks:
            # 如果是发射点，则移除其对应的所有无人机客户点
            for drone_customer in drone_tasks[focus]:
                if drone_customer in node_to_index and node_to_index[drone_customer] not in removed_indices:
                    drone_idx = node_to_index[drone_customer]
                    if drone_idx in removable_indices:
                        removable_indices.remove(drone_idx)
                    removed_indices.append(drone_idx)
                    removed_customers.append(drone_customer)
                    removed_count += 1
    
    # 构建remaining_customers和remaining_services
    remaining_customers = []
    remaining_services = []
    
    for i, (customer, service) in enumerate(zip(path_layer, service_layer)):
        if i not in removed_indices:
            remaining_customers.append(customer)
            remaining_services.append(service)
    
    return removed_customers, remaining_customers, remaining_services

def greedy_insert_and_repair(removed_customers: List[int], remaining_customers: List[int], 
                           remaining_services: List[int], problem: Problem, 
                           current_gen: int = 0, max_gen: int = 100) -> Individual:
    """
    贪婪插入算子 + 修复：将移除的客户点按总成本最小原则插入回路径，然后修复解决方案
    
    Args:
        removed_customers: 被移除的客户点列表
        remaining_customers: 剩余的客户点列表
        remaining_services: 剩余客户点对应的服务方式列表
        problem: 问题实例
        current_gen: 当前代数
        max_gen: 最大代数
    
    Returns:
        Individual: 修复后的个体
    """
    from utils import calculate_total_cost
    
    # 创建当前解的副本
    updated_customers = list(remaining_customers) if isinstance(remaining_customers, tuple) else remaining_customers.copy()
    updated_services = list(remaining_services) if isinstance(remaining_services, tuple) else remaining_services.copy()
    
    # 每个发射点绑定的最大无人机数量
    max_drones = problem.num_drones
    
    # 对每个被移除的客户点，贪婪插入
    for customer in removed_customers:
        best_cost = float('inf')
        best_position = 0
        best_service_type = 0
        
        # 修改：排除边界位置，不在索引0和最后一个位置插入
        valid_insert_positions = range(1, len(updated_customers))
        
        # 尝试每个有效的插入位置
        for insert_pos in valid_insert_positions:
            # 尝试两种服务方式（0=卡车服务，1=无人机服务）
            for service_type in [0, 1]:
                # 新增约束：无人机客户点不能直接插在配送中心后面
                if service_type == 1:  # 如果尝试作为无人机服务插入
                    # 检查插入位置前一个节点是否是配送中心
                    if insert_pos > 0 and updated_customers[insert_pos-1] == 0:
                        continue  # 跳过这种组合

                # 无人机服务相关检查
                if service_type == 1:
                    # 获取客户点的需求量检查载重约束
                    pickup, delivery = problem.demands.get(customer, (0, 0))
                    if max(pickup, delivery) > problem.drone_params['max_load']:
                        continue  # 跳过无人机服务选项
                        
                    # 找到插入位置前面最近的卡车服务点作为发射点
                    launch_point_found = False
                    launch_point_idx = -1
                    
                    # 向前查找发射点
                    for j in range(insert_pos - 1, -1, -1):
                        if j < len(updated_customers) and updated_services[j] == 0 and updated_customers[j] != 0:
                            launch_point_found = True
                            launch_point_idx = j
                            break
                    
                    # 如果没找到发射点，无法以无人机服务插入
                    if not launch_point_found:
                        continue
                    
                    # 计算该发射点目前绑定的无人机客户点数量
                    launch_point = updated_customers[launch_point_idx]
                    
                    # 创建临时个体以解码当前状态
                    temp_chromosomes = (tuple(updated_customers), tuple(updated_services))
                    temp_individual = Individual(chromosomes=temp_chromosomes)
                    
                    # 解码获取无人机任务信息
                    decoded = Individual.decode(temp_chromosomes, problem)
                    
                    # 查找指定发射点的无人机任务数量
                    drone_count = 0
                    for _, tasks in decoded:
                        if launch_point in tasks:
                            drone_count = len(tasks[launch_point])
                            break
                    
                    # 如果该发射点的无人机任务已满，不能再添加新的无人机任务
                    if drone_count >= max_drones:
                        continue  # 跳过无人机服务选项
                
                # 创建临时解
                temp_customers = updated_customers.copy()
                temp_services = updated_services.copy()
                
                # 插入客户点
                temp_customers.insert(insert_pos, customer)
                temp_services.insert(insert_pos, service_type)
                
                # 创建临时个体
                temp_chromosomes = (tuple(temp_customers), tuple(temp_services))
                temp_individual = Individual(chromosomes=temp_chromosomes)
                
                # 应用所有修复算子
                check_and_repair_service_type(problem, temp_individual)
                check_and_repair_drone_launch_point(problem, temp_individual)
                check_and_repair_drone_task_limit(problem, temp_individual)
                check_and_repair_path_separation(problem, temp_individual)
                check_and_repair_drone_payload(problem, temp_individual)
                
                # 解码获取路径和无人机任务
                decoded = temp_individual.decode(temp_individual.chromosomes, problem)
                routes = [route for route, _ in decoded]
                
                # 构建无人机任务字典
                drone_tasks = {}
                for _, tasks in decoded:
                    for launch_node, targets in tasks.items():
                        if launch_node not in drone_tasks:
                            drone_tasks[launch_node] = []
                        drone_tasks[launch_node].extend(targets)
                
                # 计算插入后的总成本
                try:
                    cost = calculate_total_cost(problem, routes, drone_tasks)
                    
                    # 如果当前插入位置和服务方式的成本更低，则更新最佳选择
                    if cost < best_cost:
                        best_cost = cost
                        best_position = insert_pos
                        best_service_type = service_type
                except Exception:
                    # 如果计算成本过程中出错，则跳过此选项
                    continue
        
        # 使用最佳位置和服务方式插入客户点
        if best_cost < float('inf'):
            updated_customers.insert(best_position, customer)
            updated_services.insert(best_position, best_service_type)
        else:
            # 如果没找到有效方案，尝试以卡车服务插入到一个有效位置
            if len(updated_customers) > 1:
                valid_pos = min(len(updated_customers) - 1, max(1, len(updated_customers) // 2))
                updated_customers.insert(valid_pos, customer)
                updated_services.insert(valid_pos, 0)  # 卡车服务
    
    # 创建最终个体
    final_chromosomes = (tuple(updated_customers), tuple(updated_services))
    individual = Individual(chromosomes=final_chromosomes)
    
    # 应用修复算子
    check_and_repair_service_type(problem, individual)
    check_and_repair_drone_launch_point(problem, individual)
    check_and_repair_drone_task_limit(problem, individual)
    check_and_repair_path_separation(problem, individual)
    check_and_repair_drone_payload(problem, individual)
    
    # 返回修复后的个体
    return individual

def nearest_neighbor_insert_and_repair(removed_customers: List[int], remaining_customers: List[int], 
                                     remaining_services: List[int], problem: Problem, 
                                     current_gen: int = 0, max_gen: int = 100) -> Individual:
    """
    最近邻插入算子 + 修复：将移除的客户点按最近邻和成本最小原则插入回路径，然后修复解决方案
    
    插入时遵循以下规则：
    1. 不能在路径的首尾插入客户点
    2. 无人机客户点不能直接插在配送中心后面
    3. 检查前置发射点的无人机任务数量是否已达上限
    
    Args:
        removed_customers: 被移除的客户点列表
        remaining_customers: 剩余的客户点列表
        remaining_services: 剩余客户点对应的服务方式列表
        problem: 问题实例
        current_gen: 当前代数
        max_gen: 最大代数
    
    Returns:
        Individual: 修复后的个体
    """
    from utils import calculate_total_cost
    
    # 复制列表，避免修改原始列表
    updated_customers = list(remaining_customers) if isinstance(remaining_customers, tuple) else remaining_customers.copy()
    updated_services = list(remaining_services) if isinstance(remaining_services, tuple) else remaining_services.copy()
    
    # 打乱被移除的客户点列表，随机顺序处理
    random_removed = removed_customers.copy()
    random.shuffle(random_removed)
    
    # 每个发射点绑定的最大无人机数量
    max_drones = problem.num_drones
    
    # 无人机最大载重
    drone_max_payload = problem.drone_params['max_load']
    
    # 逐个插入被移除的客户点
    for customer in random_removed:
        # 如果当前解为空，特殊处理
        if len(updated_customers) <= 2:  # 只有配送中心或为空
            # 创建基本路径 [0, customer, 0]
            updated_customers = [0, customer, 0]
            updated_services = [0, 0, 0]  # 默认使用卡车服务
            continue
        
        # 找出最近的节点
        nearest_node_idx = -1
        min_distance = float('inf')
        
        for i, node in enumerate(updated_customers):
            if node != 0:  # 跳过配送中心
                # 计算欧几里得距离
                x1, y1 = problem.locations[customer]
                x2, y2 = problem.locations[node]
                distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
                
                if distance < min_distance:
                    min_distance = distance
                    nearest_node_idx = i
        
        # 如果没有找到最近节点（可能所有节点都是配送中心），则随机选择一个有效位置
        if nearest_node_idx == -1:
            # 确定有效的插入位置（排除首尾）
            valid_positions = list(range(1, len(updated_customers)))
            
            # 排除列表中的最后一个位置
            if valid_positions and valid_positions[-1] == len(updated_customers) - 1:
                valid_positions.pop()
                
            # 如果没有有效位置，跳过当前客户点
            if not valid_positions:
                continue
                
            # 随机选择有效位置
            insert_pos = random.choice(valid_positions)
            service_type = 0  # 默认使用卡车服务
            updated_customers.insert(insert_pos, customer)
            updated_services.insert(insert_pos, service_type)
            continue
        
        # 计算在最近节点前后插入的总成本
        best_cost = float('inf')
        best_position = 0
        best_service_type = 0
        
        # 考虑两个插入位置：最近节点前面和后面
        potential_positions = [nearest_node_idx, nearest_node_idx + 1]
        
        # 约束1: 排除首尾位置
        valid_positions = []
        for pos in potential_positions:
            # 排除首位置
            if pos == 0:
                continue
            # 排除尾位置
            if pos == len(updated_customers):
                continue
            valid_positions.append(pos)
        
        # 如果没有有效位置，跳过当前客户点
        if not valid_positions:
            continue
        
        # 尝试每个有效的插入位置
        for insert_pos in valid_positions:
            # 尝试两种服务方式
            for service_type in [0, 1]:  # 0=卡车服务，1=无人机服务
                
                # 约束2: 无人机客户点不能直接插在配送中心后面
                if service_type == 1:  # 如果是无人机服务
                    if insert_pos > 0 and updated_customers[insert_pos-1] == 0:
                        continue  # 跳过这种情况
                
                # 无人机服务需要检查载重约束
                if service_type == 1:
                    # 检查载重约束
                    pickup, delivery = problem.demands.get(customer, (0, 0))
                    if max(pickup, delivery) > drone_max_payload:
                        continue  # 跳过无人机服务选项
                    
                    # 约束3: 检查前置发射点的无人机任务数量
                    # 找到前置发射点
                    launch_point_found = False
                    launch_point_idx = -1
                    
                    # 向前查找卡车服务点作为发射点
                    for j in range(insert_pos - 1, -1, -1):
                        if updated_services[j] == 0 and updated_customers[j] != 0:
                            launch_point_found = True
                            launch_point_idx = j
                            break
                    
                    # 如果没找到发射点，不能选择无人机服务
                    if not launch_point_found:
                        continue
                    
                    # 获取发射点
                    launch_point = updated_customers[launch_point_idx]
                    
                    # 创建临时个体以解码当前状态
                    temp_chromosomes = (tuple(updated_customers), tuple(updated_services))
                    temp_individual = Individual(chromosomes=temp_chromosomes)
                    
                    # 解码获取无人机任务信息
                    decoded = temp_individual.decode(temp_chromosomes, problem)
                    
                    # 计算该发射点目前绑定的无人机客户点数量
                    drone_count = 0
                    for _, tasks in decoded:
                        if launch_point in tasks:
                            drone_count = len(tasks[launch_point])
                            break
                    
                    # 如果已达上限，不能再添加无人机任务
                    if drone_count >= max_drones:
                        continue
                
                # 创建临时解
                temp_customers = updated_customers.copy()
                temp_services = updated_services.copy()
                
                # 插入客户点
                temp_customers.insert(insert_pos, customer)
                temp_services.insert(insert_pos, service_type)
                
                # 创建临时个体
                temp_chromosomes = (tuple(temp_customers), tuple(temp_services))
                temp_individual = Individual(chromosomes=temp_chromosomes)
                
                # 应用修复算子
                check_and_repair_service_type(problem, temp_individual)
                check_and_repair_drone_launch_point(problem, temp_individual)
                check_and_repair_path_separation(problem, temp_individual)
                check_and_repair_drone_payload(problem, temp_individual)
                check_and_repair_drone_task_limit(problem, temp_individual)
                
                # 解码获取路径和无人机任务
                decoded = temp_individual.decode(temp_individual.chromosomes, problem)
                routes = [route for route, _ in decoded]
                
                # 构建无人机任务字典
                drone_tasks = {}
                for _, tasks in decoded:
                    for launch_node, targets in tasks.items():
                        if launch_node not in drone_tasks:
                            drone_tasks[launch_node] = []
                        drone_tasks[launch_node].extend(targets)
                
                # 计算总成本
                try:
                    cost = calculate_total_cost(problem, routes, drone_tasks)
                    
                    # 更新最佳插入选项
                    if cost < best_cost:
                        best_cost = cost
                        best_position = insert_pos
                        best_service_type = service_type
                except Exception:
                    # 跳过计算失败的情况
                    continue
        
        # 如果找到有效插入方案，执行插入
        if best_cost < float('inf'):
            updated_customers.insert(best_position, customer)
            updated_services.insert(best_position, best_service_type)
        else:
            # 没找到有效方案，使用默认策略：插入到非首尾位置
            valid_positions = list(range(1, len(updated_customers)))
            if valid_positions and valid_positions[-1] == len(updated_customers) - 1:
                valid_positions.pop()
            
            if valid_positions:
                insert_pos = random.choice(valid_positions)
                updated_customers.insert(insert_pos, customer)
                updated_services.insert(insert_pos, 0)  # 默认使用卡车服务
    
    # 创建最终个体
    final_chromosomes = (tuple(updated_customers), tuple(updated_services))
    individual = Individual(chromosomes=final_chromosomes)
    
    # 应用最终修复
    check_and_repair_service_type(problem, individual)
    check_and_repair_drone_launch_point(problem, individual)
    check_and_repair_drone_payload(problem, individual)
    check_and_repair_path_separation(problem, individual)
    check_and_repair_drone_task_limit(problem, individual)
    
    return individual

def apply_destroy_repair(individual: Individual, problem: Problem, current_gen: int = 0, max_gen: int = 100) -> Individual:
    """
    应用破坏-修复操作：随机选择一种破坏算子和一种修复算子进行操作
    
    Args:
        individual: 要操作的个体
        problem: 问题实例
        current_gen: 当前代数
        max_gen: 最大代数
    
    Returns:
        Individual: 修复后的个体
    """
    # 创建个体的深拷贝
    new_individual = copy.deepcopy(individual)
    
    # 随机选择破坏算子
    destroy_operator = random.choice([max_route_destroy, cluster_destroy])
    
    # 随机选择修复算子
    repair_operator = random.choice([greedy_insert_and_repair, nearest_neighbor_insert_and_repair])
    
    # 应用破坏算子
    removed_customers, remaining_customers, remaining_services = destroy_operator(new_individual, problem)
    
    # 如果没有客户点被移除，直接返回原个体
    if not removed_customers:
        return new_individual
    
    # 应用修复算子
    repaired_individual = repair_operator(removed_customers, remaining_customers, remaining_services, problem, current_gen, max_gen)
    
    return repaired_individual