import numpy as np
import re
from typing import List, Dict, Tuple, Set, Any, Optional
import os
import random
import math

class Customer:
    """客户点类"""
    def __init__(self, id: int, x: float, y: float, pickup: float, delivery: float):
        self.id = id          # 客户点ID
        self.x = x            # x坐标
        self.y = y            # y坐标
        self.pickup = pickup  # 取货需求
        self.delivery = delivery  # 送货需求

class DroneTask:
    """无人机任务类"""
    def __init__(self, drone_id: int, customer_id: int, launch_point: int, energy_consumption: float = 0.0):
        self.drone_id = drone_id  # 无人机ID
        self.customer_id = customer_id  # 客户ID
        self.launch_point = launch_point  # 发射点ID
        self.energy_consumption = energy_consumption  # 能耗

class TruckRoute:
    """卡车路径类"""
    def __init__(self, truck_id: int, route: List[int], drone_tasks: Dict[int, List[DroneTask]]):
        self.truck_id = truck_id  # 卡车ID
        self.route = route  # 卡车路径
        self.drone_tasks = drone_tasks  # 每个发射点的无人机任务列表

class Problem:
    
    def __init__(self, data_path="A-n32-k2-d4.vrp"):
        """
        Problem类构造函数
        
        参数:
            data_path: 数据文件路径，如果为None则创建空问题
        """
        # 保存数据文件路径
        self.data_file = "A-n32-k2-d4.vrp"
        
        # 设置默认参数
        self._set_default_params()
        
        # 初始化数据结构
        self.customers = []
        self.depot = None
        self.nodes = []
        self.locations = {}
        self.demands = {}
        
        # 如果提供了数据路径，则加载数据
        if data_path:
            self._load_data(data_path)

    def _set_default_params(self):
        """设置默认参数值"""
        # 基本信息
        self.name = ""
        self.dimension = 0              # 节点数量（包括配送中心）
        self.num_customers = 0          # 客户点数量
        
        # 车辆参数
        self.num_vehicles = 2           # 卡车数量
        self.num_drones = 2            # 每辆卡车配备的无人机数量
        self.vehicle_capacity = 200.0   # 卡车载重容量 (kg) 
        self.vehicle_speed = 30.0       # 卡车速度 (km/h)
        self.vehicle_per_cost = 3.25     # 卡车单位距离成本 (元/km)
        self.vehicle_fixed_cost = 200.0  # 卡车固定成本 (元/辆)
        
        # 无人机参数
        self.drone_max_payload = 10.0    # 无人机最大载重 (kg)
        self.drone_mass = 15.0           # 无人机自身质量 (kg)
        self.battery_energy = 300.0     # 电池容量 (Wh)
        self.drone_speed = 80.0         # 无人机速度 (km/h)
        self.drone_energy_rate = 66.5   # 能耗系数 (W/kg)
        self.drone_per_cost = 0.3       # 无人机单位能耗成本 (元/Wh)
        self.drone_fixed_cost = 10.0     # 无人机固定成本 (元/架)
        # 满载最远飞12km
        
        # 其他参数
        self.service_time = 3.0         # 服务时间 (min)
        self.wait_time_per_cost = 1.2   # 等待单位时间成本 (元/min)
        self.system_operation_cost_per_hour = 18.0  # 系统运营单位时间成本 (元/小时)

    def _load_data(self, path: str):
        """从简化的VRP文件加载数据"""
        import os
        
        try:
            # 统一使用utf-8编码读取文件
            with open(path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # 过滤掉注释行和空行
            lines = [line.strip() for line in lines 
                    if line.strip() and not line.strip().startswith('//')]
            
            # 打印前几行用于调试
            # print("文件前5行:")
            # for i in range(min(5, len(lines))):
            #     print(f"  {lines[i]}")
            
            # 解析文件头信息
            i = 0
            
            # 解析头部信息
            while i < len(lines):
                line = lines[i].strip()
                
                if line.startswith('NAME'):
                    self.name = line.split(':')[1].strip()
                elif line.startswith('DIMENSION'):
                    self.dimension = int(line.split(':')[1].strip())
                elif line.startswith('NUM_CUSTOMERS'):
                    self.num_customers = int(line.split(':')[1].strip())
                # 只保留解析这些基本参数，其他使用默认值
                elif line.startswith('NODE_COORD_SECTION'):
                    break
                
                i += 1  # 增加索引
            
            # 处理节点坐标部分
            if i < len(lines) and lines[i].strip() == 'NODE_COORD_SECTION':
                i += 1  # 跳过NODE_COORD_SECTION行
                
                # 初始化节点列表
                self.nodes = [(0, 0)] * self.dimension
                
                node_count = 0
                while node_count < self.dimension and i < len(lines):
                    line = lines[i].strip()
                    
                    # 检查是否到达下一个段落
                    if line.startswith('DEMAND_SECTION') or line.startswith('DEPOT_SECTION'):
                        break
                    
                    parts = line.split()
                    if len(parts) >= 3:
                        node_id = int(parts[0])
                        x = float(parts[1])
                        y = float(parts[2])
                        
                        # 存储节点坐标
                        self.nodes[node_id] = (x, y)
                        self.locations[node_id] = (x, y)
                        
                        # 如果是配送中心
                        if node_id == 0:
                            self.depot = Customer(0, x, y, 0, 0)
                        else:
                            # 创建客户点，需求稍后会更新
                            customer = Customer(node_id, x, y, 0, 0)
                            self.customers.append(customer)
                        
                        node_count += 1
                    
                    i += 1  # 递增索引
            
            # 处理需求数据部分
            while i < len(lines):
                if lines[i].strip() == 'DEMAND_SECTION':
                    i += 1  # 跳过DEMAND_SECTION行
                    break
                i += 1
            
            # 读取需求数据
            while i < len(lines):
                line = lines[i].strip()
                
                # 检查是否到达下一个段落
                if line.startswith('DEPOT_SECTION') or line.startswith('EOF'):
                    break
                
                parts = line.split()
                if len(parts) >= 3:
                    try:
                        node_id = int(parts[0])
                        delivery = float(parts[1])
                        pickup = float(parts[2])
                        
                        # 存储需求数据
                        self.demands[node_id] = (pickup, delivery)
                        
                        # 更新客户点需求
                        if node_id > 0:
                            for customer in self.customers:
                                if customer.id == node_id:
                                    customer.pickup = pickup
                                    customer.delivery = delivery
                                    break
                    except:
                        pass  # 忽略解析错误
                
                i += 1  # 递增索引
            
            # 设置车辆和无人机参数字典
            self._update_params_dict()
            
            print(f"成功加载数据: {self.name}")
            print(f"节点数量: {self.dimension}, 客户点数量: {self.num_customers}")
            print(f"卡车数量: {self.num_vehicles}, 每辆卡车配备无人机数量: {self.num_drones}")
        
        except Exception as e:
            print(f"加载数据失败: {e}")
            import traceback
            traceback.print_exc()
            # 创建模拟数据以允许测试继续
            self._create_mock_data()
        
    def _update_params_dict(self):
        """更新参数字典"""
        # 设置卡车参数字典
        self.truck_params = {
            'num_vehicles': self.num_vehicles,
            'max_load': self.vehicle_capacity,
            'speed': self.vehicle_speed,
            'cost_per_km': self.vehicle_per_cost,
            'cost_fixed': self.vehicle_fixed_cost,
            'service_time': self.service_time,
            'wait_time_per_cost':self.wait_time_per_cost
        }
        
        # 设置无人机参数字典
        self.drone_params = {
            'num_drones': self.num_drones,
            'mass': self.drone_mass,
            'max_load': self.drone_max_payload,
            'speed': self.drone_speed,
            'battery': self.battery_energy,
            'energy_consumption_rate': self.drone_energy_rate,
            'cost_per_energy': self.drone_per_cost,
            'cost_fixed': self.drone_fixed_cost,
            'service_time': self.service_time
        }
    
    def get_customer(self, customer_id: int) -> Customer:
        """
        根据ID获取客户点
        
        参数:
            customer_id: 客户点ID
            
        返回:
            客户点实例
        """
        if customer_id == 0:
            return self.depot
        
        for customer in self.customers:
            if customer.id == customer_id:
                return customer
        
        raise ValueError(f"找不到ID为{customer_id}的客户点")

class Individual:
    def __init__(self, routes=None, drone_tasks=None, chromosomes=None):
        """
        个体编码，支持两种初始化方式：
        1. 直接提供routes和drone_tasks (兼容旧版API)
        2. 直接提供chromosomes (新的染色体编码)
        
        参数:
            routes: 卡车路径列表 [[0,1,8,0], [0,3,9,4,0]]
            drone_tasks: 无人机任务字典 {1:[5,7], 8:[2], 9:[6,10]}
            chromosomes: 个体染色体，包含两层编码 (路径层, 服务方式层)
        """
        self.fitness = -np.inf    # 适应度值
        self.penalty = 0          # 惩罚值
        self.total_cost = np.inf  # 总成本值
        self._decode_cache = None # 解码缓存
        
        # 根据初始化参数设置属性
        if chromosomes:
            self.chromosomes = chromosomes
            # 如果提供了染色体，解码获取routes和drone_tasks
            decoded = self.decode(chromosomes, None)
            self.routes = [route for route, _ in decoded]
            
            # 合并无人机任务
            self.drone_tasks = {}
            for _, tasks in decoded:
                for launch_node, targets in tasks.items():
                    if launch_node in self.drone_tasks:
                        self.drone_tasks[launch_node].extend(targets)
                    else:
                        self.drone_tasks[launch_node] = targets
        elif routes:
            # 如果提供了routes和drone_tasks，进行编码
            self.routes = routes
            self.drone_tasks = drone_tasks or {}
            # 默认使用传统编码，可以通过参数控制
            self.chromosomes = Individual.encode(routes, drone_tasks or {}, None, use_extended_encoding=False)
        else:
            # 默认初始化
            self.routes = []
            self.drone_tasks = {}
            self.chromosomes = ([], [])

    # 在Individual类中添加此方法
    def invalidate_cache(self):
        """
        重置缓存的计算结果，在染色体发生变化时调用
        """
        self._fitness = None
        self._total_cost = None
        self._penalty = None
        self._is_evaluated = False
        # 如果有其他缓存的属性，也需要在这里重置

    @staticmethod
    def encode(routes, drone_tasks, problem=None, use_extended_encoding=False):
        """
        将路径和无人机任务编码为染色体

        参数:
            routes: 卡车路径列表
            drone_tasks: 无人机任务字典
            problem: 问题实例
            use_extended_encoding: 是否使用扩展编码（支持服务方式2,3,4）
        """
        if use_extended_encoding:
            return Individual._encode_extended(routes, drone_tasks, problem)
        else:
            return Individual._encode_traditional(routes, drone_tasks, problem)

    @staticmethod
    def _encode_traditional(routes, drone_tasks, problem=None):
        """传统编码方法（只支持服务方式0和1）"""
        path_layer = []
        service_layer = []

        # 处理所有路径，包括空路径[0,0]
        for i, route in enumerate(routes):
            # 添加每条路径
            if i > 0 and len(path_layer) > 0 and path_layer[-1] == 0:
                # 如果前一条路径已以0结尾，则直接附加当前路径（不包括起始0）
                path_layer.extend(route[1:])
            else:
                # 否则添加完整路径
                path_layer.extend(route)

        # 初始化服务方式为全0（卡车服务）
        service_layer = [0] * len(path_layer)

        # 将无人机客户点插入到路径层和服务方式层
        final_path_layer = []
        final_service_layer = []

        # 跟踪当前处理的位置
        current_idx = 0

        while current_idx < len(path_layer):
            node = path_layer[current_idx]
            service = service_layer[current_idx]

            # 添加当前节点
            final_path_layer.append(node)
            final_service_layer.append(service)

            # 如果当前节点是发射点，则在其后添加其对应的无人机客户点
            if node != 0 and node in drone_tasks and drone_tasks[node]:
                # 添加所有由该发射点控制的无人机客户点
                for drone_node in drone_tasks[node]:
                    final_path_layer.append(drone_node)
                    final_service_layer.append(1)  # 设置为无人机服务

            current_idx += 1

        return (tuple(final_path_layer), tuple(final_service_layer))

    @staticmethod
    def _encode_extended(routes, drone_tasks, problem=None):
        """
        扩展编码方法（支持服务方式0,1,2,3,4）

        服务方式编码：
        - 0: 配送中心或仅卡车服务
        - 1: 无人机客户点
        - 2: 卡车服务+发射无人机
        - 3: 卡车服务+回收无人机
        - 4: 卡车服务+回收+发射
        """
        path_layer = []
        service_layer = []

        # 处理所有路径
        for i, route in enumerate(routes):
            if i > 0 and len(path_layer) > 0 and path_layer[-1] == 0:
                path_layer.extend(route[1:])
            else:
                path_layer.extend(route)

        # 初始化服务方式为全0（卡车服务）
        service_layer = [0] * len(path_layer)

        # 生成扩展的服务方式和无人机客户点
        final_path_layer = []
        final_service_layer = []

        # 随机选择一些卡车节点作为发射点和回收点
        truck_nodes = [i for i, node in enumerate(path_layer) if node != 0 and service_layer[i] == 0]

        # 随机选择发射点和回收点
        if len(truck_nodes) >= 2:
            num_launch_recovery_pairs = min(2, len(truck_nodes) // 2)  # 最多2对发射-回收点

            for _ in range(num_launch_recovery_pairs):
                if len(truck_nodes) >= 2:
                    # 随机选择发射点和回收点
                    launch_idx = random.choice(truck_nodes)
                    truck_nodes.remove(launch_idx)

                    recovery_idx = random.choice(truck_nodes)
                    truck_nodes.remove(recovery_idx)

                    # 确保发射点在回收点之前
                    if launch_idx > recovery_idx:
                        launch_idx, recovery_idx = recovery_idx, launch_idx

                    # 设置服务方式
                    service_layer[launch_idx] = 2  # 发射点
                    service_layer[recovery_idx] = 3  # 回收点

        # 构建最终的染色体
        current_idx = 0
        while current_idx < len(path_layer):
            node = path_layer[current_idx]
            service = service_layer[current_idx]

            # 添加当前节点
            final_path_layer.append(node)
            final_service_layer.append(service)

            # 如果是发射点，在其后插入无人机客户点
            if service == 2:
                # 生成1-3个无人机客户点
                num_drone_customers = random.randint(1, 3)
                drone_customers = Individual._generate_drone_customers(node, num_drone_customers, problem)

                for drone_customer in drone_customers:
                    final_path_layer.append(drone_customer)
                    final_service_layer.append(1)  # 无人机客户

            # 如果当前节点是传统的发射点（有无人机任务），也要插入
            elif node != 0 and node in drone_tasks and drone_tasks[node] and service == 0:
                for drone_node in drone_tasks[node]:
                    final_path_layer.append(drone_node)
                    final_service_layer.append(1)  # 设置为无人机服务

            current_idx += 1

        return (tuple(final_path_layer), tuple(final_service_layer))

    @staticmethod
    def _generate_drone_customers(launch_node, num_customers, problem):
        """为发射点生成无人机客户点"""
        if not problem:
            # 如果没有问题实例，生成随机客户点
            return [random.randint(1, 20) for _ in range(num_customers)]

        # 基于距离和约束生成合适的客户点
        suitable_customers = []
        max_distance = 15.0  # 最大距离阈值

        for customer_id in range(1, problem.num_customers + 1):
            if customer_id == launch_node:
                continue

            # 检查距离约束
            if hasattr(problem, 'locations') and launch_node in problem.locations:
                # 计算欧几里得距离
                loc1 = problem.locations[launch_node]
                loc2 = problem.locations[customer_id]
                distance = math.sqrt((loc1[0] - loc2[0])**2 + (loc1[1] - loc2[1])**2)

                if distance <= max_distance:
                    suitable_customers.append(customer_id)

        # 如果没有合适的客户，随机选择
        if not suitable_customers:
            suitable_customers = list(range(1, min(problem.num_customers + 1, 21)))
            if launch_node in suitable_customers:
                suitable_customers.remove(launch_node)

        # 随机选择指定数量的客户
        num_to_select = min(num_customers, len(suitable_customers))
        return random.sample(suitable_customers, num_to_select) if suitable_customers else []
    @staticmethod
    def decode(chromosomes, problem=None):
        """将染色体解码为卡车路径和无人机任务，使用优化的路径分割逻辑"""
        # 检查是否为Individual实例的方法调用，若是则尝试使用缓存
        if hasattr(chromosomes, '_decode_cache') and chromosomes._decode_cache is not None:
            return chromosomes._decode_cache
        
        path_layer, service_layer = chromosomes if not hasattr(chromosomes, 'chromosomes') else chromosomes.chromosomes
        
        if len(path_layer) != len(service_layer):
            raise ValueError("路径层和服务方式层长度不一致")
        
        # 路径分割逻辑
        routes_segments = []  # 存储路径段列表，每段包含(节点,服务方式)元组
        remaining_path = list(zip(path_layer, service_layer))  # 将路径层和服务方式层打包
        
        while remaining_path:
            if len(remaining_path) == 1 and remaining_path[0][0] == 0:
                break
            
            # 找到第一个0（配送中心）的位置
            start_idx = -1
            for i, (node, _) in enumerate(remaining_path):
                if node == 0:
                    start_idx = i
                    break
            
            if start_idx == -1:  # 没有找到配送中心，停止解码
                break
            
            # 找到下一个0（配送中心）的位置
            end_idx = -1
            for i in range(start_idx + 1, len(remaining_path)):
                if remaining_path[i][0] == 0:
                    end_idx = i
                    break
            
            if end_idx == -1:  # 没有找到下一个配送中心，取到末尾
                current_segment = remaining_path[start_idx:]
                # 添加终止配送中心（如果不是以0结尾）
                if current_segment[-1][0] != 0:
                    current_segment.append((0, 0))
                routes_segments.append(current_segment)
                remaining_path = []  # 清空剩余路径，结束循环
            else:
                # 提取当前路径段（不包括第二个0）
                current_segment = remaining_path[start_idx:end_idx]
                # 添加终止配送中心（手动补充0）
                current_segment.append((0, 0))
                routes_segments.append(current_segment)
                # 更新剩余路径（保留第二个0，作为下一段的起点）
                remaining_path = remaining_path[end_idx:]
        
        # 解码各路径段为卡车路径和无人机任务
        decoded_solution = []
        
        for segment in routes_segments:
            # 提取卡车路径（只包含配送中心和卡车服务节点）
            truck_route = [node for node, service in segment if node == 0 or service == 0]

            # 确保路径至少包含起点和终点配送中心
            if len(truck_route) < 2:  # 防止出现空路径
                truck_route = [0, 0]
            elif truck_route[0] != 0:  # 确保以配送中心开始
                truck_route.insert(0, 0)
            if truck_route[-1] != 0:  # 确保以配送中心结束
                truck_route.append(0)

            # 提取无人机任务
            drone_tasks = {}

            # 找出路径中所有的无人机服务节点
            drone_nodes = [(i, node) for i, (node, service) in enumerate(segment) if service == 1 and node != 0]

            # 为每个无人机节点找到前面最近的卡车服务节点作为发射点
            for _, drone_node in drone_nodes:
                # 找到合适的发射点
                launch_node = None

                # 在segment中找到该无人机节点
                for i, (node, service) in enumerate(segment):
                    if node == drone_node and service == 1:
                        # 向前查找最近的卡车服务节点
                        for j in range(i-1, -1, -1):
                            prev_node, prev_service = segment[j]
                            if prev_service == 0 and prev_node != 0:  # 找到卡车服务节点(不是配送中心)
                                launch_node = prev_node
                                break
                        break

                # 如果找到了发射点
                if launch_node is not None:
                    if launch_node not in drone_tasks:
                        drone_tasks[launch_node] = []
                    drone_tasks[launch_node].append(drone_node)

            decoded_solution.append((truck_route, drone_tasks))
        
        # 如果是Individual实例调用，则缓存结果
        if hasattr(chromosomes, '_decode_cache'):
            chromosomes._decode_cache = decoded_solution
        
        return decoded_solution

class Solution:
    def __init__(self, truck_routes: List[TruckRoute] = None):
        """
        解决方案类
        
        参数:
            truck_routes: 卡车路径列表
        """
        self.problem = None
        self.total_cost = np.inf  # 总成本
        self.fitness = -np.inf    # 适应度
        self.penalty = 0          # 惩罚
        self.is_feasible = False  # 是否可行
        self.truck_routes = truck_routes or []
        self.cost = np.inf  # 兼容旧API
        self.drone_missions = []  # 兼容旧API

    @staticmethod
    def decode(chromosomes: Tuple[List[int], List[int]], problem: Problem) -> 'Solution':
        """
        从个体染色体解码为解决方案
        
        参数:
            chromosomes: 个体染色体 (路径层, 服务方式层)
            problem: 问题实例
            
        返回:
            解决方案实例
        """
        # 使用Individual.decode解码染色体
        decoded = Individual.decode(chromosomes, problem)
        
        # 创建TruckRoute列表
        truck_routes = []
        
        for truck_id, (route, drone_tasks_dict) in enumerate(decoded):
            # 创建DroneTask列表
            drone_tasks = {}
            
            for launch_point, customers in drone_tasks_dict.items():
                drone_tasks[launch_point] = []
                for i, customer_id in enumerate(customers):
                    # 计算能耗
                    energy = 0
                    if problem and problem.demands.get(customer_id):
                        pickup, delivery = problem.demands[customer_id]
                        # 计算能耗
                        if hasattr(problem, 'get_customer'):
                            launch_node = problem.get_customer(launch_point)
                            customer_node = problem.get_customer(customer_id)
                            
                            import math
                            distance = math.sqrt((launch_node.x - customer_node.x)**2 + 
                                            (launch_node.y - customer_node.y)**2)
                            
                            # 能耗与距离、载重相关
                            energy = problem.drone_energy_rate * (problem.drone_mass + delivery + pickup) * \
                                    distance / problem.drone_speed
                        
                    drone_tasks[launch_point].append(
                        DroneTask(i, customer_id, launch_point, energy)
                    )
            
            truck_routes.append(TruckRoute(truck_id, route, drone_tasks))
        
        # 创建解决方案
        solution = Solution(truck_routes)
        solution.problem = problem
        
        return solution
    
    @staticmethod
    def from_individual(individual: 'Individual') -> 'Solution':
        """将Individual对象转换为Solution对象"""
        solution = Solution()
        
        # 创建TruckRoute对象列表
        solution.truck_routes = []
        
        # 遍历individual中的路径
        for i, route in enumerate(individual.routes):
            # 收集对应路径的无人机任务
            drone_tasks = {}
            if individual.drone_tasks:
                for launch_point, tasks in individual.drone_tasks.items():
                    if launch_point in route:
                        drone_tasks[launch_point] = tasks
            
            # 正确创建TruckRoute对象，提供所需的三个参数
            truck_route = TruckRoute(
                truck_id=i,  # 使用索引作为卡车ID
                route=route,
                drone_tasks=drone_tasks
            )
            
            solution.truck_routes.append(truck_route)
        
        return solution

    def invalidate_cache(self):
        """使解码缓存失效，当染色体发生变化时调用"""
        self._decode_cache = None
