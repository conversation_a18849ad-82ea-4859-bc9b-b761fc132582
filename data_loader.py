import numpy as np
import re
from typing import List, Dict, Tuple, Set, Any, Optional
import os
import random
import math

class Customer:
    """客户点类"""
    def __init__(self, id: int, x: float, y: float, pickup: float, delivery: float):
        self.id = id          # 客户点ID
        self.x = x            # x坐标
        self.y = y            # y坐标
        self.pickup = pickup  # 取货需求
        self.delivery = delivery  # 送货需求

# class DroneTask:
#     """无人机任务类"""
#     def __init__(self, drone_id: int, customer_id: int, launch_point: int, energy_consumption: float = 0.0):
#         self.drone_id = drone_id  # 无人机ID
#         self.customer_id = customer_id  # 客户ID
#         self.launch_point = launch_point  # 发射点ID
#         self.energy_consumption = energy_consumption  # 能耗

class DroneTask:
    """
    无人机任务类 - 支持多客户服务链
    drone_tasks = {
    发射点ID: {
        'drone_tasks': [
    (这个就是一个类) → DroneTask(drone_id=1, customer_sequence=[4,7,11], launch_point=6, recovery_point=12, total_energy=156.8),
            DroneTask(drone_id=2, customer_sequence=[3,9], launch_point=6, recovery_point=12, total_energy=89.4),
            # 更多DroneTask对象...
        ], 
        'recovery_point': 回收点ID
    },
    # 更多发射点...
}

    # 实际的drone_tasks示例
    drone_tasks = {
        6: {  # 发射点6
            'drone_tasks': [
                DroneTask(1, [4, 7, 11], 6, 12, 156.8),    # 无人机1服务3个客户
                DroneTask(2, [3, 9], 6, 12, 89.4),         # 无人机2服务2个客户
            ],
            'recovery_point': 12
        },
        15: {  # 发射点15
            'drone_tasks': [
                DroneTask(3, [14, 17, 20], 15, 22, 134.5), # 无人机3服务3个客户
            ],
            'recovery_point': 22
        }
    }
    
    """
    def __init__(self, drone_id: int, customer_sequence: list, launch_point: int,
                 recovery_point: int = None, total_energy: float = 0.0):
        # 验证配送中心限制
        if launch_point == 0:
            raise ValueError("配送中心(节点0)不能作为无人机发射点")
        if recovery_point == 0:
            raise ValueError("配送中心(节点0)不能作为无人机回收点")

        self.drone_id = drone_id                    # 无人机ID
        self.customer_sequence = customer_sequence   # 客户访问序列 [4, 7, 11]
        self.launch_point = launch_point            # 发射点ID
        self.recovery_point = recovery_point        # 回收点ID
        self.total_energy = total_energy            # 总能耗

        # 兼容性：保持原有接口
        self.customer_id = customer_sequence[0] if customer_sequence else None
        self.energy_consumption = total_energy

    @property
    def num_customers(self):
        """返回服务的客户数量"""
        return len(self.customer_sequence)

    def get_route_sequence(self):
        """返回完整的飞行路径序列"""
        if not self.customer_sequence:
            return [self.launch_point, self.recovery_point]

        route = [self.launch_point]
        route.extend(self.customer_sequence)
        if self.recovery_point:
            route.append(self.recovery_point)
        return route

class TruckRoute:
    """卡车路径类"""
    def __init__(self, truck_id: int, route: List[int], drone_tasks: Dict[int, List[DroneTask]]):
        self.truck_id = truck_id  # 卡车ID
        self.route = route  # 卡车路径
        self.drone_tasks = drone_tasks  # 每个发射点的无人机任务列表

class Problem:
    
    def __init__(self, data_path="A-n32-k2-d4.vrp"):
        """
        Problem类构造函数
        
        参数:
            data_path: 数据文件路径，如果为None则创建空问题
        """
        # 保存数据文件路径
        self.data_file = "A-n32-k2-d4.vrp"
        
        # 设置默认参数
        self._set_default_params()
        
        # 初始化数据结构
        self.customers = []
        self.depot = None
        self.nodes = []
        self.locations = {}
        self.demands = {}
        
        # 如果提供了数据路径，则加载数据
        if data_path:
            self._load_data(data_path)

    def _set_default_params(self):
        """设置默认参数值"""
        # 基本信息
        self.name = ""
        self.dimension = 0              # 节点数量（包括配送中心）
        self.num_customers = 0          # 客户点数量
        
        # 车辆参数
        self.num_vehicles = 2           # 卡车数量
        self.num_drones = 2            # 每辆卡车配备的无人机数量
        self.vehicle_capacity = 200.0   # 卡车载重容量 (kg) 
        self.vehicle_speed = 30.0       # 卡车速度 (km/h)
        self.vehicle_per_cost = 3.25     # 卡车单位距离成本 (元/km)
        self.vehicle_fixed_cost = 200.0  # 卡车固定成本 (元/辆)
        
        # 无人机参数
        self.drone_max_payload = 10.0    # 无人机最大载重 (kg)
        self.drone_mass = 15.0           # 无人机自身质量 (kg)
        self.battery_energy = 300.0     # 电池容量 (Wh)
        self.drone_speed = 80.0         # 无人机速度 (km/h)
        self.drone_energy_rate = 66.5   # 能耗系数 (W/kg)
        self.drone_per_cost = 0.3       # 无人机单位能耗成本 (元/Wh)
        self.drone_fixed_cost = 10.0     # 无人机固定成本 (元/架)
        # 满载最远飞12km
        
        # 其他参数
        self.service_time = 3.0         # 服务时间 (min)
        self.wait_time_per_cost = 1.2   # 等待单位时间成本 (元/min)
        self.system_operation_cost_per_hour = 18.0  # 系统运营单位时间成本 (元/小时)

    def _load_data(self, path: str):
        """从简化的VRP文件加载数据"""
        import os
        
        try:
            # 统一使用utf-8编码读取文件
            with open(path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # 过滤掉注释行和空行
            lines = [line.strip() for line in lines 
                    if line.strip() and not line.strip().startswith('//')]
            
            # 打印前几行用于调试
            # print("文件前5行:")
            # for i in range(min(5, len(lines))):
            #     print(f"  {lines[i]}")
            
            # 解析文件头信息
            i = 0
            
            # 解析头部信息
            while i < len(lines):
                line = lines[i].strip()
                
                if line.startswith('NAME'):
                    self.name = line.split(':')[1].strip()
                elif line.startswith('DIMENSION'):
                    self.dimension = int(line.split(':')[1].strip())
                elif line.startswith('NUM_CUSTOMERS'):
                    self.num_customers = int(line.split(':')[1].strip())
                # 只保留解析这些基本参数，其他使用默认值
                elif line.startswith('NODE_COORD_SECTION'):
                    break
                
                i += 1  # 增加索引
            
            # 处理节点坐标部分
            if i < len(lines) and lines[i].strip() == 'NODE_COORD_SECTION':
                i += 1  # 跳过NODE_COORD_SECTION行
                
                # 初始化节点列表
                self.nodes = [(0, 0)] * self.dimension
                
                node_count = 0
                while node_count < self.dimension and i < len(lines):
                    line = lines[i].strip()
                    
                    # 检查是否到达下一个段落
                    if line.startswith('DEMAND_SECTION') or line.startswith('DEPOT_SECTION'):
                        break
                    
                    parts = line.split()
                    if len(parts) >= 3:
                        node_id = int(parts[0])
                        x = float(parts[1])
                        y = float(parts[2])
                        
                        # 存储节点坐标
                        self.nodes[node_id] = (x, y)
                        self.locations[node_id] = (x, y)
                        
                        # 如果是配送中心
                        if node_id == 0:
                            self.depot = Customer(0, x, y, 0, 0)
                        else:
                            # 创建客户点，需求稍后会更新
                            customer = Customer(node_id, x, y, 0, 0)
                            self.customers.append(customer)
                        
                        node_count += 1
                    
                    i += 1  # 递增索引
            
            # 处理需求数据部分
            while i < len(lines):
                if lines[i].strip() == 'DEMAND_SECTION':
                    i += 1  # 跳过DEMAND_SECTION行
                    break
                i += 1
            
            # 读取需求数据
            while i < len(lines):
                line = lines[i].strip()
                
                # 检查是否到达下一个段落
                if line.startswith('DEPOT_SECTION') or line.startswith('EOF'):
                    break
                
                parts = line.split()
                if len(parts) >= 3:
                    try:
                        node_id = int(parts[0])
                        delivery = float(parts[1])
                        pickup = float(parts[2])
                        
                        # 存储需求数据
                        self.demands[node_id] = (pickup, delivery)
                        
                        # 更新客户点需求
                        if node_id > 0:
                            for customer in self.customers:
                                if customer.id == node_id:
                                    customer.pickup = pickup
                                    customer.delivery = delivery
                                    break
                    except:
                        pass  # 忽略解析错误
                
                i += 1  # 递增索引
            
            # 设置车辆和无人机参数字典
            self._update_params_dict()
            
            print(f"成功加载数据: {self.name}")
            print(f"节点数量: {self.dimension}, 客户点数量: {self.num_customers}")
            print(f"卡车数量: {self.num_vehicles}, 每辆卡车配备无人机数量: {self.num_drones}")
        
        except Exception as e:
            print(f"加载数据失败: {e}")
            import traceback
            traceback.print_exc()
            # 创建模拟数据以允许测试继续
            self._create_mock_data()
        
    def _update_params_dict(self):
        """更新参数字典"""
        # 设置卡车参数字典
        self.truck_params = {
            'num_vehicles': self.num_vehicles,
            'max_load': self.vehicle_capacity,
            'speed': self.vehicle_speed,
            'cost_per_km': self.vehicle_per_cost,
            'cost_fixed': self.vehicle_fixed_cost,
            'service_time': self.service_time,
            'wait_time_per_cost':self.wait_time_per_cost
        }
        
        # 设置无人机参数字典
        self.drone_params = {
            'num_drones': self.num_drones,
            'mass': self.drone_mass,
            'max_load': self.drone_max_payload,
            'speed': self.drone_speed,
            'battery': self.battery_energy,
            'energy_consumption_rate': self.drone_energy_rate,
            'cost_per_energy': self.drone_per_cost,
            'cost_fixed': self.drone_fixed_cost,
            'service_time': self.service_time
        }
    
    def get_customer(self, customer_id: int) -> Customer:
        """
        根据ID获取客户点
        
        参数:
            customer_id: 客户点ID
            
        返回:
            客户点实例
        """
        if customer_id == 0:
            return self.depot
        
        for customer in self.customers:
            if customer.id == customer_id:
                return customer
        
        raise ValueError(f"找不到ID为{customer_id}的客户点")

class Individual:
    def __init__(self, routes=None, drone_tasks=None, chromosomes=None):
        """
        个体编码，支持两种初始化方式：
        1. 直接提供routes和drone_tasks (兼容旧版API)
        2. 直接提供chromosomes (新的染色体编码)
        
        参数:
            routes: 卡车路径列表 [[0,1,8,0], [0,3,9,4,0]]
            drone_tasks: 无人机任务字典 {1:[5,7], 8:[2], 9:[6,10]}
            chromosomes: 个体染色体，包含两层编码 (路径层, 服务方式层)
        """
        self.fitness = -np.inf    # 适应度值
        self.penalty = 0          # 惩罚值
        self.total_cost = np.inf  # 总成本值
        self._decode_cache = None # 解码缓存
        
        # 根据初始化参数设置属性
        if chromosomes:
            self.chromosomes = chromosomes
            # 如果提供了染色体，解码获取routes和drone_tasks
            decoded = self.decode(chromosomes, None)
            self.routes = [route for route, _ in decoded]
            
            # 合并无人机任务
            self.drone_tasks = {}
            for _, tasks in decoded:
                for launch_node, targets in tasks.items():
                    if launch_node in self.drone_tasks:
                        self.drone_tasks[launch_node].extend(targets)
                    else:
                        self.drone_tasks[launch_node] = targets
        elif routes:
            # 如果提供了routes和drone_tasks，进行编码
            self.routes = routes
            self.drone_tasks = drone_tasks or {}
            # 默认使用传统编码，可以通过参数控制
            self.chromosomes = Individual.encode(routes, drone_tasks or {}, None, use_extended_encoding=False)
        else:
            # 默认初始化
            self.routes = []
            self.drone_tasks = {}
            self.chromosomes = ([], [])

    # 在Individual类中添加此方法
    def invalidate_cache(self):
        """
        重置缓存的计算结果，在染色体发生变化时调用
        """
        self._fitness = None
        self._total_cost = None
        self._penalty = None
        self._is_evaluated = False
        # 如果有其他缓存的属性，也需要在这里重置

    @staticmethod
    def encode(routes, drone_tasks, problem=None, use_extended_encoding=False):
        """
        将路径和无人机任务编码为染色体

        参数:
            routes: 卡车路径列表
            drone_tasks: 无人机任务字典
            problem: 问题实例
            use_extended_encoding: 是否使用扩展编码（支持服务方式2,3,4）
        """
        if use_extended_encoding:
            return Individual._encode_extended(routes, drone_tasks, problem)
        else:
            return Individual._encode_traditional(routes, drone_tasks, problem)

    @staticmethod
    def _encode_traditional(routes, drone_tasks, problem=None):
        """传统编码方法（只支持服务方式0和1）"""
        path_layer = []
        service_layer = []

        # 处理所有路径，包括空路径[0,0]
        for i, route in enumerate(routes):
            # 添加每条路径
            if i > 0 and len(path_layer) > 0 and path_layer[-1] == 0:
                # 如果前一条路径已以0结尾，则直接附加当前路径（不包括起始0）
                path_layer.extend(route[1:])
            else:
                # 否则添加完整路径
                path_layer.extend(route)

        # 初始化服务方式为全0（卡车服务）
        service_layer = [0] * len(path_layer)

        # 将无人机客户点插入到路径层和服务方式层
        final_path_layer = []
        final_service_layer = []

        # 跟踪当前处理的位置
        current_idx = 0

        while current_idx < len(path_layer):
            node = path_layer[current_idx]
            service = service_layer[current_idx]

            # 添加当前节点
            final_path_layer.append(node)
            final_service_layer.append(service)

            # 如果当前节点是发射点，则在其后添加其对应的无人机客户点
            if node != 0 and node in drone_tasks and drone_tasks[node]:
                # 添加所有由该发射点控制的无人机客户点
                for drone_node in drone_tasks[node]:
                    final_path_layer.append(drone_node)
                    final_service_layer.append(1)  # 设置为无人机服务

            current_idx += 1

        return (tuple(final_path_layer), tuple(final_service_layer))

    @staticmethod
    def _encode_extended(routes, drone_tasks, problem=None):
        """
        扩展编码方法 - 支持两种输入格式

        参数:
        - routes: 卡车路径列表 [[0,6,12,0], [0,15,22,0]]
        - drone_tasks: 无人机任务字典，支持两种格式：
          1. 传统格式: {launch_point: [customer_list]}
          2. 扩展格式: {launch_point: {'drone_tasks': [DroneTask对象], 'recovery_point': int}}
        - problem: 问题实例

        返回: (path_layer, service_layer) 染色体编码
        """

        # 检查输入格式并转换为扩展格式
        drone_tasks_extended = {}

        if drone_tasks:
            # 检查第一个值的格式来判断输入类型，传统格式的值是列表，扩展格式的值是字典
            first_value = next(iter(drone_tasks.values()))

            if isinstance(first_value, dict) and 'drone_tasks' in first_value:
                # 已经是扩展格式
                drone_tasks_extended = drone_tasks
            elif isinstance(first_value, list):
                # 传统格式，需要转换
                print("警告：使用传统格式的drone_tasks，建议使用扩展格式以获得更好的性能")
                # 这里可以添加传统格式的处理逻辑，但建议用户使用新格式
                return Individual._encode_traditional(routes, drone_tasks, problem)
            else:
                raise ValueError(f"不支持的drone_tasks格式: {type(first_value)}")
        else:
            drone_tasks_extended = {}
        path_layer = []
        service_layer = []

        # 第一步：合并所有卡车路径
        for i, route in enumerate(routes):
            if i > 0 and len(path_layer) > 0 and path_layer[-1] == 0:
                path_layer.extend(route[1:])  # 避免重复配送中心
            else:
                path_layer.extend(route)

        # 第二步：根据完整无人机任务信息确定服务方式
        service_layer = [0] * len(path_layer)  # 初始化为卡车服务

        # 收集所有发射点和回收点
        launch_points = set(drone_tasks_extended.keys())
        recovery_points = set()
        for task_info in drone_tasks_extended.values():
            if task_info['recovery_point'] is not None:
                recovery_points.add(task_info['recovery_point'])

        # 设置服务方式
        for i, node in enumerate(path_layer):
            if node == 0:
                service_layer[i] = 0  # 配送中心
            elif node in launch_points and node in recovery_points:
                service_layer[i] = 4  # 先回收再发射然后卡车服务
            elif node in launch_points:
                service_layer[i] = 2  # 先发射然后卡车服务
            elif node in recovery_points:
                service_layer[i] = 3  # 先回收然后卡车服务
            else:
                service_layer[i] = 0  # 仅卡车服务

        # 第三步：插入无人机客户点
        final_path_layer = []
        final_service_layer = []

        for i, (node, service) in enumerate(zip(path_layer, service_layer)):
            final_path_layer.append(node)
            final_service_layer.append(service)

            # 如果是发射点（服务方式2或4），插入无人机客户点
            if service in [2, 4] and node in drone_tasks_extended:
                drone_tasks_list = drone_tasks_extended[node]['drone_tasks']
                for drone_task in drone_tasks_list:
                    final_path_layer.append(drone_task.customer_id)
                    final_service_layer.append(1)  # 无人机服务

        return (tuple(final_path_layer), tuple(final_service_layer))

    @staticmethod
    def decode_extended(chromosomes, problem=None):
        """
        新的解码函数 - 返回完整的无人机任务信息

        返回格式:
        [
            (truck_route, drone_tasks_extended),
            ...
        ]
        其中 drone_tasks_extended = {
            launch_point: {
                'drone_tasks': [DroneTask(drone_id, customer_id, launch_point, recovery_point, energy)],
                'recovery_point': recovery_point_id
            }
        }
        """
        path_layer, service_layer = chromosomes if not hasattr(chromosomes, 'chromosomes') else chromosomes.chromosomes

        if len(path_layer) != len(service_layer):
            raise ValueError("路径层和服务方式层长度不一致")

        # 路径分割 - 找到所有配送中心位置进行分割
        depot_positions = [i for i, node in enumerate(path_layer) if node == 0]

        routes_segments = []
        start_idx = 0

        for i in range(len(depot_positions) - 1):
            end_idx = depot_positions[i + 1]
            segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, end_idx + 1)]
            routes_segments.append(segment)
            start_idx = depot_positions[i + 1]

        # 处理最后一段（如果存在）
        if start_idx < len(path_layer):
            segment = [(path_layer[j], service_layer[j]) for j in range(start_idx, len(path_layer))]
            routes_segments.append(segment)

        # 解码每个路径段
        decoded_solution = []

        for segment in routes_segments:
            truck_route = []
            drone_tasks_extended = {}

            # 当前活跃的无人机任务跟踪
            active_drone_tasks = {}  # {launch_point: {'customers': [], 'recovery_point': None}}
            drone_id_counter = 1

            i = 0
            while i < len(segment):
                node, service = segment[i]

                if service == 0:  # 配送中心或仅卡车服务
                    truck_route.append(node)

                elif service == 1:  # 无人机客户点
                    # 这个客户点属于最近的发射点
                    if active_drone_tasks:
                        # 找到最近的未完成任务
                        latest_launch = max(active_drone_tasks.keys())
                        active_drone_tasks[latest_launch]['customers'].append(node)

                elif service == 2:  # 发射+卡车服务
                    truck_route.append(node)
                    # 开始新的无人机任务
                    active_drone_tasks[node] = {'customers': [], 'recovery_point': None}

                elif service == 3:  # 回收+卡车服务
                    truck_route.append(node)
                    # 完成无人机任务 - 找到对应的发射点（按顺序匹配）
                    for launch_point in sorted(active_drone_tasks.keys()):
                        if active_drone_tasks[launch_point]['recovery_point'] is None:
                            active_drone_tasks[launch_point]['recovery_point'] = node
                            break

                elif service == 4:  # 回收+发射+卡车服务
                    truck_route.append(node)
                    # 先完成回收
                    for launch_point in active_drone_tasks:
                        if active_drone_tasks[launch_point]['recovery_point'] is None:
                            active_drone_tasks[launch_point]['recovery_point'] = node
                            break
                    # 再开始新发射
                    active_drone_tasks[node] = {'customers': [], 'recovery_point': None}

                i += 1

            # 转换为DroneTask对象
            for launch_point, task_info in active_drone_tasks.items():
                if task_info['customers']:  # 只处理有客户的任务
                    # 将所有客户作为一个服务链
                    if task_info['customers']:
                        drone_task = DroneTask(
                            drone_id=drone_id_counter,
                            customer_sequence=task_info['customers'],  # 客户序列
                            launch_point=launch_point,
                            recovery_point=task_info['recovery_point'],
                            total_energy=0.0  # 需要后续计算
                        )
                        drone_tasks_list = [drone_task]
                        drone_id_counter += 1
                    else:
                        drone_tasks_list = []

                    drone_tasks_extended[launch_point] = {
                        'drone_tasks': drone_tasks_list,
                        'recovery_point': task_info['recovery_point']
                    }

            decoded_solution.append((truck_route, drone_tasks_extended))

        return decoded_solution

    @staticmethod
    def _find_recovery_point(launch_idx, path_layer, service_layer):
        """
        找到对应的回收点位置

        参数:
            launch_idx: 发射点在路径中的索引
            path_layer: 路径层
            service_layer: 服务方式层

        返回:
            回收点的节点ID，如果没找到则返回发射点ID（退化为同步模式）
        """
        launch_node = path_layer[launch_idx]

        # 在发射点之后寻找服务方式为3的回收点
        for i in range(launch_idx + 1, len(service_layer)):
            if service_layer[i] == 3:  # 回收点
                return path_layer[i]

        # 如果没找到专门的回收点，返回发射点（同步模式）
        return launch_node

    @staticmethod
    def _generate_async_drone_chain(launch_node, recovery_node, num_customers, problem=None):
        """
        生成异步无人机任务链，考虑发射点到回收点的路径优化

        参数:
            launch_node: 发射点节点ID
            recovery_node: 回收点节点ID
            num_customers: 需要服务的客户数量
            problem: 问题实例

        返回:
            优化后的客户点访问序列
        """
        if not problem:
            return list(range(1, min(num_customers + 1, 6)))

        # 获取发射点和回收点的坐标
        launch_coord = problem.locations.get(launch_node, (0, 0))
        recovery_coord = problem.locations.get(recovery_node, (0, 0))

        # 选择合适的客户点（载重约束 + 距离约束）
        suitable_customers = []
        drone_max_load = problem.drone_params.get('max_load', 10)

        for customer_id in range(1, problem.num_customers + 1):
            if customer_id in [launch_node, recovery_node]:
                continue

            # 检查载重约束
            pickup, delivery = problem.demands.get(customer_id, (0, 0))
            if max(pickup, delivery) > drone_max_load:
                continue

            # 检查距离约束（简化的能耗估算）
            customer_coord = problem.locations.get(customer_id, (0, 0))

            # 计算发射点->客户点->回收点的总距离
            dist_launch_customer = ((launch_coord[0] - customer_coord[0])**2 +
                                  (launch_coord[1] - customer_coord[1])**2)**0.5
            dist_customer_recovery = ((customer_coord[0] - recovery_coord[0])**2 +
                                    (customer_coord[1] - recovery_coord[1])**2)**0.5

            total_distance = dist_launch_customer + dist_customer_recovery

            # 简化的能耗检查（假设最大飞行距离12km）
            if total_distance <= 12.0:
                suitable_customers.append((customer_id, total_distance))

        # 按距离排序，优先选择距离较短的客户
        suitable_customers.sort(key=lambda x: x[1])

        # 选择前num_customers个客户
        selected_customers = [cust[0] for cust in suitable_customers[:num_customers]]

        # 如果没有合适的客户，随机选择一些
        if not selected_customers:
            all_customers = list(range(1, min(problem.num_customers + 1, 21)))
            if launch_node in all_customers:
                all_customers.remove(launch_node)
            if recovery_node in all_customers and recovery_node in all_customers:
                all_customers.remove(recovery_node)
            selected_customers = random.sample(all_customers, min(num_customers, len(all_customers)))

        return selected_customers
    @staticmethod
    def decode(chromosomes, problem=None):
        """
        将染色体解码为卡车路径和无人机任务，支持异步模式

        扩展功能：
        - 支持服务方式0,1,2,3,4的解码
        - 处理异步发射-回收模式
        - 生成多客户点无人机任务链
        - 计算时间同步信息
        """
        # 检查是否为Individual实例的方法调用，若是则尝试使用缓存
        if hasattr(chromosomes, '_decode_cache') and chromosomes._decode_cache is not None:
            return chromosomes._decode_cache

        path_layer, service_layer = chromosomes if not hasattr(chromosomes, 'chromosomes') else chromosomes.chromosomes

        if len(path_layer) != len(service_layer):
            raise ValueError("路径层和服务方式层长度不一致")
        
        # 路径分割逻辑
        routes_segments = []  # 存储路径段列表，每段包含(节点,服务方式)元组
        remaining_path = list(zip(path_layer, service_layer))  # 将路径层和服务方式层打包
        
        while remaining_path:
            if len(remaining_path) == 1 and remaining_path[0][0] == 0:
                break
            
            # 找到第一个0（配送中心）的位置
            start_idx = -1
            for i, (node, _) in enumerate(remaining_path):
                if node == 0:
                    start_idx = i
                    break
            
            if start_idx == -1:  # 没有找到配送中心，停止解码
                break
            
            # 找到下一个0（配送中心）的位置
            end_idx = -1
            for i in range(start_idx + 1, len(remaining_path)):
                if remaining_path[i][0] == 0:
                    end_idx = i
                    break
            
            if end_idx == -1:  # 没有找到下一个配送中心，取到末尾
                current_segment = remaining_path[start_idx:]
                # 添加终止配送中心（如果不是以0结尾）
                if current_segment[-1][0] != 0:
                    current_segment.append((0, 0))
                routes_segments.append(current_segment)
                remaining_path = []  # 清空剩余路径，结束循环
            else:
                # 提取当前路径段（不包括第二个0）
                current_segment = remaining_path[start_idx:end_idx]
                # 添加终止配送中心（手动补充0）
                current_segment.append((0, 0))
                routes_segments.append(current_segment)
                # 更新剩余路径（保留第二个0，作为下一段的起点）
                remaining_path = remaining_path[end_idx:]
        
        # 解码各路径段为卡车路径和无人机任务
        decoded_solution = []
        
        for segment in routes_segments:
            # 异步模式解码 - 支持服务方式0,1,2,3,4
            truck_route = []
            drone_tasks = {}

            # 异步任务跟踪
            async_missions = {}  # {launch_point: {'customers': [], 'recovery_point': None}}
            current_launch_point = None

            for node, service_type in segment:
                if service_type == 0:  # 配送中心或卡车服务
                    truck_route.append(node)
                    if node != 0:
                        current_launch_point = node

                elif service_type == 1:  # 无人机客户
                    if current_launch_point is not None:
                        # 检查是否属于异步任务
                        active_async_launch = None
                        for launch_point, mission in async_missions.items():
                            if mission['recovery_point'] is None:  # 还未回收的任务
                                active_async_launch = launch_point
                                break

                        if active_async_launch:
                            # 添加到异步任务链
                            async_missions[active_async_launch]['customers'].append(node)
                        else:
                            # 传统同步模式
                            if current_launch_point not in drone_tasks:
                                drone_tasks[current_launch_point] = []
                            drone_tasks[current_launch_point].append(node)

                elif service_type == 2:  # 发射点
                    truck_route.append(node)
                    current_launch_point = node
                    # 开始新的异步任务
                    async_missions[node] = {'customers': [], 'recovery_point': None}

                elif service_type == 3:  # 回收点
                    truck_route.append(node)
                    # 完成对应的异步任务
                    for launch_point, mission in async_missions.items():
                        if mission['recovery_point'] is None:
                            mission['recovery_point'] = node
                            # 转换为标准无人机任务格式
                            if mission['customers']:
                                drone_tasks[launch_point] = mission['customers']
                            break

                elif service_type == 4:  # 回收+发射
                    truck_route.append(node)
                    # 先完成回收
                    for launch_point, mission in async_missions.items():
                        if mission['recovery_point'] is None:
                            mission['recovery_point'] = node
                            if mission['customers']:
                                drone_tasks[launch_point] = mission['customers']
                            break
                    # 再开始新的发射
                    current_launch_point = node
                    async_missions[node] = {'customers': [], 'recovery_point': None}

            # 确保卡车路径格式正确
            if len(truck_route) < 2:
                truck_route = [0, 0]
            elif truck_route[0] != 0:
                truck_route.insert(0, 0)
            if truck_route[-1] != 0:
                truck_route.append(0)

            decoded_solution.append((truck_route, drone_tasks))
        
        # 如果是Individual实例调用，则缓存结果
        if hasattr(chromosomes, '_decode_cache'):
            chromosomes._decode_cache = decoded_solution
        
        return decoded_solution

class Solution:
    def __init__(self, truck_routes: List[TruckRoute] = None):
        """
        解决方案类
        
        参数:
            truck_routes: 卡车路径列表
        """
        self.problem = None
        self.total_cost = np.inf  # 总成本
        self.fitness = -np.inf    # 适应度
        self.penalty = 0          # 惩罚
        self.is_feasible = False  # 是否可行
        self.truck_routes = truck_routes or []
        self.cost = np.inf  # 兼容旧API
        self.drone_missions = []  # 兼容旧API

    @staticmethod
    def decode(chromosomes: Tuple[List[int], List[int]], problem: Problem) -> 'Solution':
        """
        从个体染色体解码为解决方案
        
        参数:
            chromosomes: 个体染色体 (路径层, 服务方式层)
            problem: 问题实例
            
        返回:
            解决方案实例
        """
        # 使用Individual.decode解码染色体
        decoded = Individual.decode(chromosomes, problem)
        
        # 创建TruckRoute列表
        truck_routes = []
        
        for truck_id, (route, drone_tasks_dict) in enumerate(decoded):
            # 创建DroneTask列表
            drone_tasks = {}
            
            for launch_point, customers in drone_tasks_dict.items():
                drone_tasks[launch_point] = []
                for i, customer_id in enumerate(customers):
                    # 计算能耗
                    energy = 0
                    if problem and problem.demands.get(customer_id):
                        pickup, delivery = problem.demands[customer_id]
                        # 计算能耗
                        if hasattr(problem, 'get_customer'):
                            launch_node = problem.get_customer(launch_point)
                            customer_node = problem.get_customer(customer_id)
                            
                            import math
                            distance = math.sqrt((launch_node.x - customer_node.x)**2 + 
                                            (launch_node.y - customer_node.y)**2)
                            
                            # 能耗与距离、载重相关
                            energy = problem.drone_energy_rate * (problem.drone_mass + delivery + pickup) * \
                                    distance / problem.drone_speed
                        
                    drone_tasks[launch_point].append(
                        DroneTask(i, customer_id, launch_point, energy)
                    )
            
            truck_routes.append(TruckRoute(truck_id, route, drone_tasks))
        
        # 创建解决方案
        solution = Solution(truck_routes)
        solution.problem = problem
        
        return solution
    
    @staticmethod
    def from_individual(individual: 'Individual') -> 'Solution':
        """将Individual对象转换为Solution对象"""
        solution = Solution()
        
        # 创建TruckRoute对象列表
        solution.truck_routes = []
        
        # 遍历individual中的路径
        for i, route in enumerate(individual.routes):
            # 收集对应路径的无人机任务
            drone_tasks = {}
            if individual.drone_tasks:
                for launch_point, tasks in individual.drone_tasks.items():
                    if launch_point in route:
                        drone_tasks[launch_point] = tasks
            
            # 正确创建TruckRoute对象，提供所需的三个参数
            truck_route = TruckRoute(
                truck_id=i,  # 使用索引作为卡车ID
                route=route,
                drone_tasks=drone_tasks
            )
            
            solution.truck_routes.append(truck_route)
        
        return solution

    def invalidate_cache(self):
        """使解码缓存失效，当染色体发生变化时调用"""
        self._decode_cache = None
