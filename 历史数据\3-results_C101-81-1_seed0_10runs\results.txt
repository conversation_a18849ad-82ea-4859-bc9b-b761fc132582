if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.6:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期

# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)


savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成


- 能耗超限惩罚值
"""
# 基础惩罚系数和参数
namuda2 = 1.0  # 元/Wh
gamma = 3.0
alpha = 1.5


========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1125.63
最小总成本: 1006.06 (运行 10)
最大总成本: 1309.40 (运行 3)
总成本标准差: 113.68

========== 算法精度与稳定性分析 ==========
最大偏差: 303.35 (30.15%)
平均偏差: 119.58 (11.89%)
平均求解时间: 302.17秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000928 | 1077.87 |    0.00 | 301.71
     2 |        2 | 0.000984 | 1016.25 |    0.00 | 301.73
     3 |        3 | 0.000764 | 1309.40 |    0.00 | 303.33
     4 |        4 | 0.000982 | 1018.21 |    0.00 | 302.09
     5 |        5 | 0.000962 | 1039.82 |    0.00 | 302.00
     6 |        6 | 0.000817 | 1224.63 |    0.00 | 301.52
     7 |        7 | 0.000780 | 1282.20 |    0.00 | 301.97
     8 |        8 | 0.000944 | 1059.61 |    0.00 | 301.65
     9 |        9 | 0.000818 | 1222.28 |    0.00 | 301.73
    10 |       10 | 0.000994 | 1006.06 |    0.00 | 303.93

最佳解详细信息:
运行ID: 10
适应度: 0.000994
总成本: 1006.06
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 17, 18, 21, 16, 24, 20, 23, 40, 38, 36, 37, 35, 32, 33, 34, 0, 6, 7, 5, 3, 4, 1, 0, 60, 2, 9, 8, 78, 76, 75, 74, 73, 80, 79, 77, 10, 13, 12, 11, 14, 15, 27, 29, 31, 30, 26, 28, 25, 41, 0, 52, 50, 49, 53, 59, 54, 48, 55, 51, 64, 62, 58, 56, 57, 61, 63, 44, 43, 47, 46, 42, 45, 39, 22, 19, 71, 70, 65, 67, 69, 68, 66, 72, 0)
染色体 2: (0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0)

最佳解路线详情:
路线 1: [0, 17, 18, 21, 20, 40, 38, 35, 32, 33, 34, 0]
  无人机任务:
    从节点 21 发射无人机访问: [16, 24]
    从节点 20 发射无人机访问: [23]
    从节点 38 发射无人机访问: [36, 37]
路线 2: [0, 6, 3, 0]
  无人机任务:
    从节点 6 发射无人机访问: [7, 5]
    从节点 3 发射无人机访问: [4, 1]
路线 3: [0, 60, 2, 78, 76, 75, 80, 10, 11, 27, 29, 31, 26, 25, 41, 0]
  无人机任务:
    从节点 2 发射无人机访问: [9, 8]
    从节点 75 发射无人机访问: [74, 73]
    从节点 80 发射无人机访问: [79, 77]
    从节点 10 发射无人机访问: [13, 12]
    从节点 11 发射无人机访问: [14, 15]
    从节点 31 发射无人机访问: [30]
    从节点 26 发射无人机访问: [28]
路线 4: [0, 52, 50, 49, 54, 51, 64, 62, 58, 56, 61, 63, 44, 46, 39, 22, 19, 71, 70, 69, 72, 0]
  无人机任务:
    从节点 49 发射无人机访问: [53, 59]
    从节点 54 发射无人机访问: [48, 55]
    从节点 56 发射无人机访问: [57]
    从节点 44 发射无人机访问: [43, 47]
    从节点 46 发射无人机访问: [42, 45]
    从节点 70 发射无人机访问: [65, 67]
    从节点 69 发射无人机访问: [68, 66]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000505 | 1978.94 |    0.00
  10 | 0.000505 | 1978.94 |    0.00
  20 | 0.000505 | 1978.94 |    0.00
  30 | 0.000516 | 1938.00 |    0.00
  40 | 0.000516 | 1938.00 |    0.00
  50 | 0.000516 | 1938.00 |    0.00
  60 | 0.000520 | 1923.87 |    0.00
  70 | 0.000520 | 1923.87 |    0.00
  80 | 0.000524 | 1909.12 |    0.00
  90 | 0.000533 | 1875.00 |    0.00
 100 | 0.000586 | 1706.39 |    0.00
 110 | 0.000586 | 1706.39 |    0.00
 120 | 0.000600 | 1666.47 |    0.00
 130 | 0.000648 | 1544.10 |    0.00
 140 | 0.000649 | 1541.26 |    0.00
 150 | 0.000659 | 1517.61 |    0.00
 160 | 0.000661 | 1512.03 |    0.00
 170 | 0.000661 | 1512.03 |    0.00
 180 | 0.000661 | 1512.03 |    0.00
 190 | 0.000669 | 1494.26 |    0.00
 200 | 0.000683 | 1464.34 |    0.00
 210 | 0.000683 | 1464.34 |    0.00
 220 | 0.000708 | 1413.36 |    0.00
 230 | 0.000737 | 1356.09 |    0.00
 240 | 0.000741 | 1349.30 |    0.00
 250 | 0.000773 | 1294.46 |    0.00
 260 | 0.000781 | 1280.62 |    0.00
 270 | 0.000799 | 1251.68 |    0.00
 280 | 0.000808 | 1237.29 |    0.00
 290 | 0.000808 | 1237.29 |    0.00
 300 | 0.000812 | 1231.49 |    0.00
 310 | 0.000850 | 1176.45 |    0.00
 320 | 0.000861 | 1160.79 |    0.00
 330 | 0.000865 | 1155.51 |    0.00
 340 | 0.000933 | 1071.14 |    0.41
 350 | 0.000936 | 1067.95 |    0.42
 360 | 0.000941 | 1061.94 |    0.43
 370 | 0.000952 | 1049.90 |    0.44
 380 | 0.000974 | 1027.19 |    0.00
 390 | 0.000974 | 1027.19 |    0.00
 400 | 0.000993 | 1007.37 |    0.00
 410 | 0.000993 | 1007.37 |    0.00
