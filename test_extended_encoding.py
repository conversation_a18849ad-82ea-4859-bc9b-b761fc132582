#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扩展的编码功能
"""

import random
from data_loader import Problem, Individual

def test_extended_encoding():
    """测试扩展编码功能"""
    print("=" * 80)
    print("测试扩展编码功能")
    print("=" * 80)
    
    # 设置随机种子
    random.seed(42)
    
    # 加载问题
    problem = Problem("C_聚类_数据集/C101-21-1.vrp")
    
    print(f"使用数据集: C101-21-1.vrp")
    print(f"节点数量: {problem.dimension}")
    print(f"客户数量: {problem.num_customers}")
    print()
    
    # 创建测试路径和无人机任务
    test_routes = [
        [0, 1, 2, 3, 0],
        [0, 4, 5, 6, 0]
    ]
    test_drone_tasks = {
        1: [7, 8],
        4: [9, 10]
    }
    
    print("测试数据:")
    print(f"卡车路径: {test_routes}")
    print(f"无人机任务: {test_drone_tasks}")
    print()
    
    # 测试传统编码
    print("1. 传统编码测试")
    print("-" * 40)
    
    traditional_chromosomes = Individual.encode(test_routes, test_drone_tasks, problem, use_extended_encoding=False)
    print(f"传统编码结果:")
    print(f"  路径层: {traditional_chromosomes[0]}")
    print(f"  服务层: {traditional_chromosomes[1]}")
    
    # 分析服务方式
    service_counts_traditional = {}
    for service in traditional_chromosomes[1]:
        service_counts_traditional[service] = service_counts_traditional.get(service, 0) + 1
    print(f"  服务方式分布: {service_counts_traditional}")
    print()
    
    # 测试扩展编码
    print("2. 扩展编码测试")
    print("-" * 40)
    
    extended_chromosomes = Individual.encode(test_routes, test_drone_tasks, problem, use_extended_encoding=True)
    print(f"扩展编码结果:")
    print(f"  路径层: {extended_chromosomes[0]}")
    print(f"  服务层: {extended_chromosomes[1]}")
    
    # 分析服务方式
    service_counts_extended = {}
    for service in extended_chromosomes[1]:
        service_counts_extended[service] = service_counts_extended.get(service, 0) + 1
    print(f"  服务方式分布: {service_counts_extended}")
    print()
    
    # 验证扩展编码的特点
    print("3. 扩展编码验证")
    print("-" * 40)
    
    service_names = {
        0: "配送中心/卡车服务",
        1: "无人机客户",
        2: "发射点",
        3: "回收点",
        4: "发射+回收"
    }
    
    print("扩展编码详细分析:")
    for service, count in service_counts_extended.items():
        service_name = service_names.get(service, "未知")
        print(f"  服务方式{service} ({service_name}): {count}次")
    
    # 检查是否包含扩展服务方式
    has_extended_services = any(service in [2, 3, 4] for service in service_counts_extended.keys())
    print(f"\n✓ 包含扩展服务方式: {'是' if has_extended_services else '否'}")
    
    if has_extended_services:
        print("✓ 扩展编码成功！支持发射回收分离")
        
        # 显示染色体的逐位解析
        print("\n染色体逐位解析:")
        path_layer, service_layer = extended_chromosomes
        for i, (node, service) in enumerate(zip(path_layer, service_layer)):
            service_name = service_names.get(service, "未知")
            print(f"  位置{i:2d}: 节点{node:2d} -> 服务方式{service} ({service_name})")
    else:
        print("⚠ 警告：扩展编码没有生成扩展服务方式")
    
    print()
    
    # 测试多次生成的随机性
    print("4. 随机性测试")
    print("-" * 40)
    
    print("生成5个扩展编码个体，验证随机性:")
    for i in range(5):
        chromosomes = Individual.encode(test_routes, test_drone_tasks, problem, use_extended_encoding=True)
        service_counts = {}
        for service in chromosomes[1]:
            service_counts[service] = service_counts.get(service, 0) + 1
        
        extended_count = sum(count for service, count in service_counts.items() if service in [2, 3, 4])
        print(f"  个体{i+1}: 扩展服务方式数量 = {extended_count}, 分布 = {service_counts}")
    
    print("\n关键验证:")
    print("✓ 扩展编码功能实现成功")
    print("✓ 能够生成包含服务方式2,3,4的染色体")
    print("✓ 为后续差分进化算法提供了扩展的解空间")

if __name__ == "__main__":
    test_extended_encoding()
