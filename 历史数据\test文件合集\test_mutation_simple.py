# import copy
# import random
# import numpy as np
# from data_loader import Individual
# from operators import mutation

# def create_mock_problem():
#     """创建模拟问题实例，使用给定的参数和自定义坐标"""
#     mock_problem = type('MockProblem', (), {
#         'name': "模拟测试问题",
#         'dimension': 11,  # 0-10号客户点
#         'num_customers': 10,
#         'num_vehicles': 2,
#         'num_drones': 2,
#         'vehicle_capacity': 100.0,
#         'vehicle_speed': 40.0,
#         'vehicle_per_cost': 5.0,
#         'vehicle_fixed_cost': 30.0,
#         'drone_max_payload': 5.0,
#         'drone_mass': 4.0,
#         'battery_energy': 200.0,
#         'drone_speed': 50.0,
#         'drone_energy_rate': 50.0,
#         'drone_per_cost': 5.0,
#         'drone_fixed_cost': 3.0,
#         'service_time': 5.0,
#         'wait_time_per_cost': 1.0,
        
#         # 自定义客户点坐标，设计为有一定距离分布
#         'locations': {
#             0: (0, 0),     # 配送中心
#             1: (1, 5),     # 卡车客户点
#             2: (8, 2),     # 卡车客户点
#             3: (5, 8),     # 卡车客户点
#             4: (10, 5),    # 卡车客户点
#             5: (2, 4),     # 无人机客户点(由1发射)
#             6: (7, 6),     # 无人机客户点(由3发射)
#             7: (3, 6),     # 无人机客户点(由1发射)
#             8: (9, 4),     # 无人机客户点(由2发射)
#             9: (6, 9),     # 无人机客户点(由3发射)
#             10: (8, 7),    # 无人机客户点(由4发射)
#         },
        
#         # 设计合理的客户点需求
#         'demands': {
#             0: (0, 0),     # 配送中心
#             1: (10, 5),    # 卡车客户点
#             2: (8, 12),    # 卡车客户点
#             3: (15, 8),    # 卡车客户点
#             4: (12, 10),   # 卡车客户点
#             5: (2, 3),     # 无人机客户点(轻量)
#             6: (3, 2),     # 无人机客户点(轻量)
#             7: (4, 1),     # 无人机客户点(轻量)
#             8: (2, 4),     # 无人机客户点(轻量)
#             9: (3, 3),     # 无人机客户点(轻量)
#             10: (4, 2),    # 无人机客户点(轻量)
#         }
#     })()
    
#     # 添加兼容属性
#     mock_problem.truck_params = {
#         'num_vehicles': mock_problem.num_vehicles,
#         'max_load': mock_problem.vehicle_capacity,
#         'speed': mock_problem.vehicle_speed,
#         'cost_per_km': mock_problem.vehicle_per_cost
#     }
    
#     mock_problem.drone_params = {
#         'max_drones': mock_problem.num_drones,
#         'mass': mock_problem.drone_mass,
#         'max_load': mock_problem.drone_max_payload,
#         'speed': mock_problem.drone_speed,
#         'battery': mock_problem.battery_energy,
#         'energy_consumption_rate': mock_problem.drone_energy_rate,
#         'cost_per_energy': mock_problem.drone_per_cost
#     }
    
#     return mock_problem

# def simple_mutation_test():
#     """简单测试mutation函数的效果"""
#     # 设置随机种子以保证结果可重现
#     random.seed(42)
#     np.random.seed(42)
    
#     print("="*50)
#     print("简单测试mutation函数")
#     print("="*50)
    
#     # 创建模拟问题实例
#     problem = create_mock_problem()
#     print(f"已创建模拟问题实例: {problem.name}")
    
#     # 创建父代A
#     # 按照要求设置染色体数据
#     path_layer    = (0, 1, 5, 7, 8, 2, 0, 3, 9, 6, 10, 4, 0)
#     service_layer = (0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 0)
#     parent = Individual(chromosomes=(path_layer, service_layer))
    
#     print("\n父代染色体:")
#     print(f"路径层:    {parent.chromosomes[0]}")
#     print(f"服务方式层: {parent.chromosomes[1]}")
    
#     # 复制父代创建子代
#     child = copy.deepcopy(parent)
    
#     # 对子代应用变异操作
#     print("\n应用mutation函数...")
#     mutation(child, problem, 0, 100)
    
#     # 打印子代染色体
#     print("\n子代染色体:")
#     print(f"路径层:    {child.chromosomes[0]}")
#     print(f"服务方式层: {child.chromosomes[1]}")
    
#     # 比较父代和子代染色体的不同
#     path_before, service_before = parent.chromosomes
#     path_after, service_after = child.chromosomes
    
#     # 计算变化
#     if len(path_before) == len(path_after):
#         path_changes = sum(1 for a, b in zip(path_before, path_after) if a != b)
#         print(f"\n路径层变化: {path_changes}处")
#     else:
#         print(f"\n路径层长度变化: {len(path_before)} -> {len(path_after)}")
    
#     if len(service_before) == len(service_after):
#         service_changes = sum(1 for a, b in zip(service_before, service_after) if a != b)
#         print(f"服务方式层变化: {service_changes}处")
#     else:
#         print(f"服务方式层长度变化: {len(service_before)} -> {len(service_after)}")

# if __name__ == "__main__":
#     simple_mutation_test()

import copy
import random
import numpy as np
from data_loader import Problem
from initialization import initialize_population
from operators import mutation

def test_mutation():
    """测试mutation函数对初始种群中的5个个体进行变异"""
    
    # 设置随机种子以确保结果可重现
    random.seed(42)
    np.random.seed(42)
    
    # 加载问题实例
    problem = Problem('A-n32-k2-d4.vrp')
    
    # 生成初始种群（种群规模为5）
    population = initialize_population(problem, 5)
    
    # 对每个个体应用mutation函数
    for i, individual in enumerate(population):
        # 记录变异前的染色体
        before_mutation = copy.deepcopy(individual.chromosomes)
        
        # 应用mutation函数
        mutation(individual, problem, population, current_gen=0, max_gen=100)
        
        # 输出变异前后的染色体对比
        print(f"\n个体 {i+1} 变异前后染色体对比:")
        
        # 路径层对比
        print(f"路径层变异前: {before_mutation[0]}")
        print(f"路径层变异后: {individual.chromosomes[0]}")
        
        # 服务方式层对比
        print(f"服务方式层变异前: {before_mutation[1]}")
        print(f"服务方式层变异后: {individual.chromosomes[1]}")

if __name__ == "__main__":
    test_mutation()