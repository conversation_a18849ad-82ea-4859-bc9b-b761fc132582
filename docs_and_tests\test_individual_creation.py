#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Individual对象创建和chromosomes属性
"""

from data_loader import Individual
import numpy as np

def test_individual_creation():
    """测试Individual对象的创建"""
    print("=" * 80)
    print("测试Individual对象创建")
    print("=" * 80)
    
    # 测试1: 用chromosomes创建Individual
    print("测试1: 用chromosomes创建Individual")
    try:
        chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        individual = Individual(chromosomes=chromosomes)
        print(f"  ✅ 成功创建Individual")
        print(f"  chromosomes类型: {type(individual.chromosomes)}")
        print(f"  chromosomes内容: {individual.chromosomes}")
        print(f"  chromosomes[0]类型: {type(individual.chromosomes[0])}")
        print(f"  chromosomes[1]类型: {type(individual.chromosomes[1])}")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试2: 检查Individual的chromosomes属性
    print("测试2: 检查Individual的chromosomes属性")
    try:
        chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        individual = Individual(chromosomes=chromosomes)
        
        # 检查chromosomes属性
        print(f"  hasattr(individual, 'chromosomes'): {hasattr(individual, 'chromosomes')}")
        print(f"  individual.chromosomes is None: {individual.chromosomes is None}")
        print(f"  isinstance(individual.chromosomes, tuple): {isinstance(individual.chromosomes, tuple)}")
        print(f"  len(individual.chromosomes): {len(individual.chromosomes)}")
        
        # 检查每个元素
        path_layer, service_layer = individual.chromosomes
        print(f"  isinstance(path_layer, list): {isinstance(path_layer, list)}")
        print(f"  isinstance(service_layer, list): {isinstance(service_layer, list)}")
        
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试3: 直接调用解码函数
    print("测试3: 直接调用解码函数")
    try:
        chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        individual = Individual(chromosomes=chromosomes)
        
        # 调用扩展解码
        result = Individual._decode_extended(individual)
        print(f"  ✅ 扩展解码成功: {len(result)} 个路径段")
        
        # 调用传统解码
        result = Individual._decode_traditional(individual)
        print(f"  ✅ 传统解码成功: {len(result)} 个路径段")
        
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()

def debug_individual_chromosomes():
    """调试Individual的chromosomes属性"""
    print("=" * 80)
    print("调试Individual的chromosomes属性")
    print("=" * 80)
    
    chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
    individual = Individual(chromosomes=chromosomes)
    
    print("Individual对象属性:")
    print(f"  individual.chromosomes = {individual.chromosomes}")
    print(f"  type(individual.chromosomes) = {type(individual.chromosomes)}")
    
    if hasattr(individual, 'chromosomes') and individual.chromosomes is not None:
        print(f"  len(individual.chromosomes) = {len(individual.chromosomes)}")
        
        if len(individual.chromosomes) == 2:
            path_layer, service_layer = individual.chromosomes
            print(f"  path_layer = {path_layer}")
            print(f"  service_layer = {service_layer}")
            print(f"  type(path_layer) = {type(path_layer)}")
            print(f"  type(service_layer) = {type(service_layer)}")
            print(f"  isinstance(path_layer, list) = {isinstance(path_layer, list)}")
            print(f"  isinstance(service_layer, list) = {isinstance(service_layer, list)}")
    
    print()
    print("验证解码函数的输入处理:")
    
    # 模拟解码函数的输入处理逻辑
    chromosomes_input = individual
    
    print(f"  hasattr(chromosomes_input, 'chromosomes') = {hasattr(chromosomes_input, 'chromosomes')}")
    
    if hasattr(chromosomes_input, 'chromosomes'):
        print(f"  chromosomes_input.chromosomes is None = {chromosomes_input.chromosomes is None}")
        if chromosomes_input.chromosomes is not None:
            chromosome_data = chromosomes_input.chromosomes
            print(f"  chromosome_data = {chromosome_data}")
            print(f"  isinstance(chromosome_data, (tuple, list)) = {isinstance(chromosome_data, (tuple, list))}")
            print(f"  len(chromosome_data) = {len(chromosome_data)}")
            
            if isinstance(chromosome_data, (tuple, list)) and len(chromosome_data) == 2:
                try:
                    path_layer, service_layer = chromosome_data
                    print(f"  解包成功: path_layer={type(path_layer)}, service_layer={type(service_layer)}")
                    print(f"  isinstance(path_layer, list) = {isinstance(path_layer, list)}")
                    print(f"  isinstance(service_layer, list) = {isinstance(service_layer, list)}")
                except Exception as e:
                    print(f"  解包失败: {e}")

if __name__ == "__main__":
    test_individual_creation()
    debug_individual_chromosomes()
