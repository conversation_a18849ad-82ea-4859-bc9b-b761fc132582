# 在代码开头添加中文字体支持
import matplotlib.pyplot as plt
import numpy as np
import math
from typing import Dict, List, Tuple
import matplotlib as mpl

# 配置中文字体支持
def setup_chinese_font():
    try:
        # 方法1：使用SimHei字体
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
        plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
        
        # 测试是否成功设置中文字体
        fig = plt.figure(figsize=(1, 1))
        plt.text(0.1, 0.1, '测试')
        plt.close(fig)
        return True
    except:
        try:
            # 方法2：使用系统字体
            from matplotlib.font_manager import FontProperties
            font_path = 'C:/Windows/Fonts/simhei.ttf'  # Windows系统黑体字体路径
            font_prop = FontProperties(fname=font_path)
            plt.rcParams['font.family'] = font_prop.get_name()
            plt.rcParams['axes.unicode_minus'] = False
            return True
        except:
            print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")
            return False

# 在程序开始时调用此函数
setup_chinese_font()

def calculate_customer_distances(dataset_path: str) -> Dict:
    """
    计算数据集中所有客户点对之间的距离统计
    
    参数:
        dataset_path: 数据集文件路径
        
    返回:
        包含距离统计信息的字典
    """
    # 读取数据文件
    with open(dataset_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # 提取客户点坐标 (跳过配送中心-节点0)
    customer_coords = {}
    reading_coords = False
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        if 'NODE_COORD_SECTION' in line:
            reading_coords = True
            continue
        
        if reading_coords and 'DEMAND_SECTION' in line:
            reading_coords = False
            continue
            
        if reading_coords:
            try:
                parts = line.split()
                if len(parts) >= 3:
                    customer_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    
                    # 排除配送中心(客户ID=0)
                    if customer_id > 0:
                        customer_coords[customer_id] = (x_coord, y_coord)
            except ValueError:
                continue
    
    # 检查是否找到客户点
    if not customer_coords:
        print("警告: 未能从文件中提取客户点坐标")
        return {
            "customer_count": 0,
            "pair_count": 0,
            "avg_distance": 0,
            "max_distance": 0,
            "min_distance": 0,
            "max_distance_pair": None,
            "min_distance_pair": None,
            "all_distances": [],
            "customer_coords": {}
        }
    
    # 计算所有客户点对之间的距离
    distances = []
    customer_ids = list(customer_coords.keys())
    
    for i in range(len(customer_ids)):
        for j in range(i + 1, len(customer_ids)):
            id1, id2 = customer_ids[i], customer_ids[j]
            coord1, coord2 = customer_coords[id1], customer_coords[id2]
            
            # 计算欧氏距离
            dist = math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
            distances.append((id1, id2, dist))
    
    # 计算统计数据
    distance_values = [d[2] for d in distances]
    
    # 防止除零错误
    if not distance_values:
        print("警告: 未计算出任何客户点对之间的距离")
        return {
            "customer_count": len(customer_coords),
            "pair_count": 0,
            "avg_distance": 0,
            "max_distance": 0,
            "min_distance": 0,
            "max_distance_pair": None,
            "min_distance_pair": None,
            "all_distances": [],
            "customer_coords": customer_coords
        }
    
    avg_distance = sum(distance_values) / len(distance_values)
    max_distance = max(distance_values)
    min_distance = min(distance_values)
    
    # 找出最远和最近的两个客户点
    max_pair = next((d for d in distances if d[2] == max_distance), None)
    min_pair = next((d for d in distances if d[2] == min_distance), None)
    
    return {
        "customer_count": len(customer_coords),
        "pair_count": len(distances),
        "avg_distance": avg_distance,
        "max_distance": max_distance,
        "min_distance": min_distance,
        "max_distance_pair": max_pair,
        "min_distance_pair": min_pair,
        "all_distances": distances,
        "customer_coords": customer_coords
    }


def visualize_distance_distribution(stats: Dict) -> None:
    """可视化距离分布"""
    distances = [d[2] for d in stats["all_distances"]]
    
    plt.figure(figsize=(10, 6))
    plt.hist(distances, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(stats["avg_distance"], color='r', linestyle='dashed', linewidth=1, 
                label=f'平均距离: {stats["avg_distance"]:.2f}')
    plt.xlabel('距离')
    plt.ylabel('频次')
    plt.title('客户点对之间距离分布')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.savefig('distance_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def visualize_customer_map(stats: Dict) -> None:
    """可视化客户点分布和最远/最近点对"""
    coords = stats["customer_coords"]
    max_pair = stats["max_distance_pair"]
    min_pair = stats["min_distance_pair"]
    
    plt.figure(figsize=(10, 8))
    
    # 绘制所有客户点
    x_coords = [coord[0] for coord in coords.values()]
    y_coords = [coord[1] for coord in coords.values()]
    plt.scatter(x_coords, y_coords, c='blue', s=30, alpha=0.6, label='客户点')
    
    # 标记最远的两个点
    if max_pair:
        id1, id2, _ = max_pair
        coord1, coord2 = coords[id1], coords[id2]
        plt.scatter([coord1[0], coord2[0]], [coord1[1], coord2[1]], c='red', s=80, 
                   edgecolor='black', label=f'最远点对: {id1}-{id2} ({stats["max_distance"]:.2f})')
        plt.plot([coord1[0], coord2[0]], [coord1[1], coord2[1]], 'r--', alpha=0.5)
    
    # 标记最近的两个点
    if min_pair:
        id1, id2, _ = min_pair
        coord1, coord2 = coords[id1], coords[id2]
        plt.scatter([coord1[0], coord2[0]], [coord1[1], coord2[1]], c='green', s=80, 
                   edgecolor='black', label=f'最近点对: {id1}-{id2} ({stats["min_distance"]:.2f})')
        plt.plot([coord1[0], coord2[0]], [coord1[1], coord2[1]], 'g--', alpha=0.5)
    
    plt.title('客户点分布及最远/最近点对')
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.savefig('customer_map.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    # 替换为实际数据集路径
    dataset_path = "RC_随机+聚类_数据集\generated_datasets\RC101-61-2.vrp"
    
    # 计算距离统计
    stats = calculate_customer_distances(dataset_path)
    
    # 打印结果
    print(f"数据集客户点数量: {stats['customer_count']}")
    print(f"客户点对数量: {stats['pair_count']}")
    print(f"平均距离: {stats['avg_distance']:.2f}")
    print(f"最远距离: {stats['max_distance']:.2f} (客户点 {stats['max_distance_pair'][0]} 和 {stats['max_distance_pair'][1]})")
    print(f"最短距离: {stats['min_distance']:.2f} (客户点 {stats['min_distance_pair'][0]} 和 {stats['min_distance_pair'][1]})")
    
    # 可视化
    visualize_distance_distribution(stats)
    visualize_customer_map(stats)

if __name__ == "__main__":
    main()