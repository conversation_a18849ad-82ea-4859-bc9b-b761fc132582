# import sys
# import os
# import random
# from data_loader import Problem, Individual
# from operators import crossover

# def test_crossover():
#     # 设置随机种子以确保结果可重现
#     random.seed(42)
    
#     # 加载问题实例
#     problem_file = "A-n32-k2-d4.vrp"
#     problem_path = os.path.join(os.path.dirname(__file__), problem_file)
#     problem = Problem(problem_path)
    
#     # 创建父代个体
#     parent_a_path = (0,1,5,10,2,7,0,8,4,3,9,6,0,11,12,13,14,0)
#     parent_a_service = (0,0,1,1,0,1,0,0,1,0,1,0,0,0,1,1,0,0)
#     parent_a = Individual(chromosomes=(parent_a_path, parent_a_service))
    
#     parent_b_path = (0,10,7,9,3,0,5,1,8,6,4,2,0,14,13,12,11,0)
#     parent_b_service = (0,0,0,1,1,0,0,0,1,1,0,1,0,0,0,1,1,0)
#     parent_b = Individual(chromosomes=(parent_b_path, parent_b_service))
    
#     # 执行交叉操作
#     child_a, child_b = crossover(parent_a, parent_b, problem)
    
#     # 打印父代和子代的染色体对比
#     print("交叉算子测试结果:")
#     print("-" * 80)
    
#     print("父代A:")
#     print(f"路径层: {parent_a_path}")
#     print(f"服务方式层: {parent_a_service}")
#     print()
    
#     print("父代B:")
#     print(f"路径层: {parent_b_path}")
#     print(f"服务方式层: {parent_b_service}")
#     print()
    
#     print("子代A:")
#     print(f"路径层: {child_a.chromosomes[0]}")
#     print(f"服务方式层: {child_a.chromosomes[1]}")
#     print()
    
#     print("子代B:")
#     print(f"路径层: {child_b.chromosomes[0]}")
#     print(f"服务方式层: {child_b.chromosomes[1]}")
    
#     # 分析交叉结果
#     print("\n交叉结果分析:")
    
#     # 检查子代是否保持了父代的配送中心分布
#     parent_a_centers = [i for i, node in enumerate(parent_a_path) if node == 0]
#     parent_b_centers = [i for i, node in enumerate(parent_b_path) if node == 0]
#     child_a_centers = [i for i, node in enumerate(child_a.chromosomes[0]) if node == 0]
#     child_b_centers = [i for i, node in enumerate(child_b.chromosomes[0]) if node == 0]
    
#     print(f"父代A配送中心位置: {parent_a_centers}")
#     print(f"子代A配送中心位置: {child_a_centers}")
#     print(f"父代B配送中心位置: {parent_b_centers}")
#     print(f"子代B配送中心位置: {child_b_centers}")
    
#     # 检查所有客户点是否都存在于子代中
#     parent_a_customers = sorted([node for node in parent_a_path if node != 0])
#     parent_b_customers = sorted([node for node in parent_b_path if node != 0])
#     child_a_customers = sorted([node for node in child_a.chromosomes[0] if node != 0])
#     child_b_customers = sorted([node for node in child_b.chromosomes[0] if node != 0])
    
#     print(f"\n父代A客户点: {parent_a_customers}")
#     print(f"子代A客户点: {child_a_customers}")
#     print(f"父代B客户点: {parent_b_customers}")
#     print(f"子代B客户点: {child_b_customers}")
    
#     # 检查服务方式的继承情况
#     print("\n服务方式继承分析:")
#     print("从父代A继承服务方式的客户点:")
#     for i, node in enumerate(child_a.chromosomes[0]):
#         if node != 0:
#             # 在父代A中查找该节点
#             for j, parent_node in enumerate(parent_a_path):
#                 if parent_node == node:
#                     if child_a.chromosomes[1][i] == parent_a_service[j]:
#                         print(f"客户点 {node}：服务方式 {child_a.chromosomes[1][i]}")
#                     break

# if __name__ == "__main__":
#     test_crossover()
import copy
import random
import numpy as np
from data_loader import Problem
from initialization import initialize_population
from operators import mutation, crossover

def test_crossover():
    """测试crossover函数"""
    print("开始测试交叉操作函数...")
    
    # 设置随机种子以确保结果可重现
    random.seed(42)
    np.random.seed(42)
    
    # 加载问题实例
    problem = Problem('A-n32-k2-d4.vrp')
    print(f"成功加载数据: {problem.name}")
    print(f"节点数量: {problem.dimension}, 客户点数量: {problem.num_customers}")
    print(f"卡车数量: {problem.num_vehicles}, 每辆卡车配备无人机数量: {problem.num_drones}")
    
    # 1. 生成初始种群（种群规模为5）
    print("\n生成初始种群(规模=5)...")
    population = initialize_population(problem, 5)
    
    # 2. 对每个个体进行变异
    print("\n对每个个体应用变异操作...")
    for i, individual in enumerate(population):
        # 记录变异前的染色体
        before_mutation = copy.deepcopy(individual.chromosomes)
        
        # 应用mutation函数
        mutation(individual, problem, current_gen=0, max_gen=100)
        
        # 输出变异前后的染色体对比
        # print(f"\n个体 {i+1} 变异前后染色体对比:")
        # print(f"路径层变异前: {before_mutation[0]}")
        # print(f"路径层变异后: {individual.chromosomes[0]}")
        # print(f"服务方式层变异前: {before_mutation[1]}")
        # print(f"服务方式层变异后: {individual.chromosomes[1]}")
    
    # 3. 在变异后的种群中选择前两个个体进行交叉
    print("\n从变异后的种群中选择两个个体进行交叉...")
    
    # 由于交叉操作要求两个父代必须有相同的客户点集合，我们创建一个副本
    parent1 = population[0]
    parent2 = population[1]
    
    # # 修改服务方式层，创建不同的个体（保持客户点集合不变）
    # path_layer, service_layer = parent2.chromosomes
    # service_layer = list(service_layer)
    
    # # 随机修改非配送中心节点的服务方式
    # for i in range(len(service_layer)):
    #     if path_layer[i] != 0 and random.random() < 0.5:  # 50%概率翻转服务方式
    #         service_layer[i] = 1 - service_layer[i]  # 翻转0/1
    
    # parent2.chromosomes = (path_layer, tuple(service_layer))
    
    # 记录交叉前的染色体
    before_crossover1 = copy.deepcopy(parent1.chromosomes)
    before_crossover2 = copy.deepcopy(parent2.chromosomes)
    
    # 4. 执行交叉操作
    try:
        # 应用crossover函数
        child1, child2 = crossover(parent1, parent2, problem)
        
        # 输出交叉前后的染色体对比
        print(f"\n交叉操作结果:")
        print(f"父代1路径层: {before_crossover1[0]}")
        print(f"父代1服务方式层: {before_crossover1[1]}")
        print(f"父代2路径层: {before_crossover2[0]}")
        print(f"父代2服务方式层: {before_crossover2[1]}")
        print(f"\n子代1路径层: {child1.chromosomes[0]}")
        print(f"子代1服务方式层: {child1.chromosomes[1]}")
        print(f"\n子代2路径层: {child2.chromosomes[0]}")
        print(f"子代2服务方式层: {child2.chromosomes[1]}")
        
        # 5. 检查交叉结果
        # 验证子代是否包含所有客户点
        parent_customers = set([n for n in before_crossover1[0] if n != 0])
        child1_customers = set([n for n in child1.chromosomes[0] if n != 0])
        child2_customers = set([n for n in child2.chromosomes[0] if n != 0])
        
        if parent_customers == child1_customers == child2_customers:
            print("\n交叉测试成功！子代包含所有客户点。")
        else:
            print("\n警告：交叉后子代的客户点集合发生变化。")
    
    except Exception as e:
        print(f"\n交叉操作失败: {e}")

if __name__ == "__main__":
    test_crossover()