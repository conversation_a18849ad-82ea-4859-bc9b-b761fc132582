NAME : C101-61-1
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 61
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 60

NODE_COORD_SECTION
 0 10.0 12.5
 1 11.2 17.0
 2 11.2 17.5
 3 10.5 16.5
 4 10.5 17.0
 5 10.5 16.2
 6 10.0 17.2
 7 10.0 16.5
 8 9.5 17.0
 9 9.5 17.5
 10 8.8 16.5
 11 8.8 17.2
 12 6.2 21.2
 13 5.5 18.8
 14 7.5 12.5
 15 7.5 13.0
 16 7.0 13.0
 17 7.0 13.8
 18 6.2 12.5
 19 6.2 13.0
 20 5.8 13.0
 21 5.0 12.5
 22 2.5 8.8
 23 8.8 7.5
 24 8.8 8.0
 25 8.2 8.0
 26 8.2 8.8
 27 8.0 7.5
 28 7.5 7.5
 29 7.5 8.0
 30 7.5 8.8
 31 7.0 7.5
 32 7.0 8.8
 33 6.5 8.0
 34 6.2 7.5
 35 6.2 8.8
 36 12.5 7.5
 37 12.5 8.8
 38 12.5 10.0
 39 12.0 7.5
 40 12.0 10.0
 41 11.8 8.8
 42 11.8 10.0
 43 11.2 7.5
 44 11.2 8.8
 45 11.2 16.2
 46 22.5 8.8
 47 22.0 7.5
 48 22.0 8.8
 49 21.8 7.5
 50 21.2 8.8
 51 18.8 13.8
 52 18.0 13.8
 53 17.5 14.5
 54 17.0 15.0
 55 16.5 13.8
 56 16.2 13.8
 57 15.8 14.5
 58 14.5 18.8
 59 13.8 20.0
 60 13.8 21.2

DEMAND_SECTION
 0 0 0      
 1 3.1 0.0      // 仅送货需求客户 - light包裹
 2 3.7 0.0      // 仅送货需求客户 - light包裹
 3 0.0 3.3      // 仅取货需求客户 - light包裹
 4 9.7 0.0      // 仅送货需求客户 - medium包裹
 5 0.0 1.9      // 仅取货需求客户 - light包裹
 6 0.0 4.5      // 仅取货需求客户 - light包裹
 7 4.7 0.0      // 仅送货需求客户 - light包裹
 8 1.3 0.0      // 仅送货需求客户 - light包裹
 9 9.6 0.0      // 仅送货需求客户 - medium包裹
 10 2.6 2.3      // 送货取货双需求客户 - light包裹
 11 6.6 0.0      // 仅送货需求客户 - medium包裹
 12 0.0 1.4      // 仅取货需求客户 - light包裹
 13 5.8 5.1      // 送货取货双需求客户 - medium包裹
 14 4.0 0.0      // 仅送货需求客户 - light包裹
 15 7.2 0.0      // 仅送货需求客户 - medium包裹
 16 0.0 1.6      // 仅取货需求客户 - light包裹
 17 1.4 0.0      // 仅送货需求客户 - light包裹
 18 2.6 0.0      // 仅送货需求客户 - light包裹
 19 10.4 0.0      // 仅送货需求客户 - heavy包裹
 20 2.8 9.6      // 送货取货双需求客户 - light包裹
 21 0.0 5.6      // 仅取货需求客户 - medium包裹
 22 3.2 0.0      // 仅送货需求客户 - light包裹
 23 0.0 13.7      // 仅取货需求客户 - heavy包裹
 24 1.8 2.1      // 送货取货双需求客户 - light包裹
 25 7.3 7.0      // 送货取货双需求客户 - medium包裹
 26 3.4 0.0      // 仅送货需求客户 - light包裹
 27 4.1 3.2      // 送货取货双需求客户 - light包裹
 28 3.3 1.6      // 送货取货双需求客户 - light包裹
 29 1.8 0.0      // 仅送货需求客户 - light包裹
 30 4.5 4.0      // 送货取货双需求客户 - light包裹
 31 0.0 4.6      // 仅取货需求客户 - light包裹
 32 4.4 0.0      // 仅送货需求客户 - light包裹
 33 1.5 0.0      // 仅送货需求客户 - light包裹
 34 1.7 0.0      // 仅送货需求客户 - light包裹
 35 1.1 0.0      // 仅送货需求客户 - light包裹
 36 4.2 3.1      // 送货取货双需求客户 - light包裹
 37 1.7 1.5      // 送货取货双需求客户 - light包裹
 38 3.1 7.7      // 送货取货双需求客户 - light包裹
 39 4.1 0.0      // 仅送货需求客户 - light包裹
 40 1.4 0.0      // 仅送货需求客户 - light包裹
 41 4.8 1.4      // 送货取货双需求客户 - light包裹
 42 4.5 0.0      // 仅送货需求客户 - light包裹
 43 2.8 0.0      // 仅送货需求客户 - light包裹
 44 10.7 0.0      // 仅送货需求客户 - heavy包裹
 45 0.0 2.6      // 仅取货需求客户 - light包裹
 46 3.0 9.2      // 送货取货双需求客户 - light包裹
 47 1.3 0.0      // 仅送货需求客户 - light包裹
 48 1.3 0.0      // 仅送货需求客户 - light包裹
 49 0.0 3.5      // 仅取货需求客户 - light包裹
 50 2.3 0.0      // 仅送货需求客户 - light包裹
 51 15.4 5.1      // 送货取货双需求客户 - heavy包裹
 52 1.4 0.0      // 仅送货需求客户 - light包裹
 53 0.0 4.4      // 仅取货需求客户 - light包裹
 54 4.1 0.0      // 仅送货需求客户 - light包裹
 55 1.9 0.0      // 仅送货需求客户 - light包裹
 56 1.9 4.1      // 送货取货双需求客户 - light包裹
 57 2.7 0.0      // 仅送货需求客户 - light包裹
 58 2.7 0.0      // 仅送货需求客户 - light包裹
 59 2.5 0.0      // 仅送货需求客户 - light包裹
 60 0.0 4.7      // 仅取货需求客户 - light包裹
 1 10 0      
 2 30 0      
 3 10 0      
 4 10 0      
 5 10 0      
 6 20 0      
 7 20 0      
 8 20 0      
 9 10 0      
 10 10 0      
 11 10 0      
 12 20 0      
 13 30 0      
 14 10 0      
 15 20 0      
 16 20 0      
 17 10 0      
 18 10 0      
 19 40 0      
 20 10 0      
 21 10 0      
 22 20 0      
 23 10 0      
 24 10 0      
 25 20 0      
 26 10 0      
 27 10 0      
 28 10 0      
 29 30 0      
 30 10 0      
 31 10 0      
 32 10 0      
 33 10 0      
 34 10 0      
 35 10 0      
 36 10 0      
 37 20 0      
 38 50 0      
 39 10 0      
 40 10 0      
 41 10 0      
 42 10 0      
 43 10 0      
 44 10 0      
 45 20 0      
 46 10 0      
 47 10 0      
 48 20 0      
 49 10 0      
 50 30 0      
 51 20 0      
 52 10 0      
 53 20 0      
 54 30 0      
 55 10 0      
 56 20 0      
 57 10 0      
 58 20 0      
 59 10 0      
 60 20 0      

DEPOT_SECTION
 0
-1
EOF