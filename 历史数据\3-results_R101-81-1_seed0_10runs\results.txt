# 确定局部搜索概率（随着进化进行逐渐增加）
base_probability = 0.1
adaptive_factor = current_gen / max_gen
search_probability = base_probability + (0.9 * adaptive_factor)

# 确定应用局部搜索的个体数量，采用自适应策略
max_elite_percentage = 0.2
min_elite_percentage = 0.05
elite_percentage = min_elite_percentage + (max_elite_percentage - min_elite_percentage) * adaptive_factor
elite_count = max(1, int(len(population) * elite_percentage))

if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.5:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期


# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.4 * elapsed_time / self.max_runtime)


# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成

========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1027.46
最小总成本: 955.80 (运行 2)
最大总成本: 1144.52 (运行 9)
总成本标准差: 57.16

========== 算法精度与稳定性分析 ==========
最大偏差: 188.72 (19.74%)
平均偏差: 71.66 (7.50%)
平均求解时间: 303.44秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000963 | 1038.33 |    0.00 | 302.34
     2 |        2 | 0.001046 |  955.80 |    0.00 | 302.64
     3 |        3 | 0.000921 | 1085.93 |    0.00 | 303.18
     4 |        4 | 0.000926 | 1079.50 |    0.00 | 310.48
     5 |        5 | 0.001007 |  993.24 |    0.00 | 302.19
     6 |        6 | 0.001024 |  976.84 |    0.00 | 304.04
     7 |        7 | 0.000988 | 1012.35 |    0.00 | 302.39
     8 |        8 | 0.000980 | 1020.41 |    0.00 | 302.16
     9 |        9 | 0.000874 | 1144.52 |    0.00 | 302.11
    10 |       10 | 0.001033 |  967.68 |    0.00 | 302.90

最佳解详细信息:
运行ID: 2
适应度: 0.001046
总成本: 955.80
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 22, 58, 1, 25, 17, 55, 54, 75, 60, 29, 9, 69, 28, 67, 66, 24, 3, 65, 43, 68, 57, 10, 46, 0, 32, 72, 35, 12, 36, 13, 37, 48, 2, 49, 0, 15, 70, 40, 30, 42, 16, 53, 27, 59, 74, 44, 26, 7, 41, 8, 38, 39, 14, 71, 51, 5, 52, 80, 31, 76, 50, 79, 78, 6, 11, 77, 73, 19, 63, 47, 56, 34, 4, 21, 0, 45, 33, 18, 61, 64, 20, 62, 23, 0)
染色体 2: (0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 22, 58, 1, 25, 17, 55, 60, 9, 69, 28, 67, 3, 65, 68, 46, 0]
  无人机任务:
    从节点 55 发射无人机访问: [54, 75]
    从节点 60 发射无人机访问: [29]
    从节点 67 发射无人机访问: [66, 24]
    从节点 65 发射无人机访问: [43]
    从节点 68 发射无人机访问: [57, 10]
路线 2: [0, 32, 35, 12, 36, 48, 2, 49, 0]
  无人机任务:
    从节点 32 发射无人机访问: [72]
    从节点 36 发射无人机访问: [13, 37]
路线 3: [0, 15, 70, 40, 16, 53, 74, 7, 41, 8, 38, 71, 51, 5, 52, 80, 31, 76, 78, 77, 73, 19, 47, 4, 21, 0]
  无人机任务:
    从节点 40 发射无人机访问: [30, 42]
    从节点 53 发射无人机访问: [27, 59]
    从节点 74 发射无人机访问: [44, 26]
    从节点 38 发射无人机访问: [39, 14]
    从节点 76 发射无人机访问: [50, 79]
    从节点 78 发射无人机访问: [6, 11]
    从节点 19 发射无人机访问: [63]
    从节点 47 发射无人机访问: [56, 34]
路线 4: [0, 45, 33, 18, 61, 62, 23, 0]
  无人机任务:
    从节点 61 发射无人机访问: [64, 20]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000572 | 1749.15 |    0.00
  10 | 0.000572 | 1749.15 |    0.00
  20 | 0.000572 | 1749.15 |    0.00
  30 | 0.000580 | 1723.73 |    0.00
  40 | 0.000593 | 1687.29 |    0.00
  50 | 0.000603 | 1658.16 |    0.00
  60 | 0.000671 | 1489.54 |    0.00
  70 | 0.000673 | 1485.88 |    0.00
  80 | 0.000689 | 1452.29 |    0.00
  90 | 0.000727 | 1376.03 |    0.00
 100 | 0.000728 | 1373.83 |    0.00
 110 | 0.000730 | 1370.16 |    0.00
 120 | 0.000747 | 1338.92 |    0.00
 130 | 0.000763 | 1310.60 |    0.00
 140 | 0.000765 | 1307.17 |    0.00
 150 | 0.000770 | 1299.23 |    0.00
 160 | 0.000770 | 1298.72 |    0.00
 170 | 0.000770 | 1298.48 |    0.00
 180 | 0.000784 | 1275.27 |    0.00
 190 | 0.000788 | 1269.75 |    0.00
 200 | 0.000794 | 1259.69 |    0.00
 210 | 0.000808 | 1238.35 |    0.00
 220 | 0.000881 | 1134.84 |    0.00
 230 | 0.000881 | 1134.60 |    0.00
 240 | 0.000915 | 1093.18 |    0.00
 250 | 0.000915 | 1093.18 |    0.00
 260 | 0.000987 | 1012.75 |    0.00
 270 | 0.000987 | 1012.75 |    0.00
 280 | 0.000998 | 1001.89 |    0.00
 290 | 0.001002 |  997.91 |    0.00
 300 | 0.001038 |  963.19 |    0.00
 310 | 0.001046 |  955.80 |    0.00
 320 | 0.001046 |  955.80 |    0.00
 330 | 0.001046 |  955.80 |    0.00
 340 | 0.001046 |  955.80 |    0.00
 350 | 0.001046 |  955.80 |    0.00
