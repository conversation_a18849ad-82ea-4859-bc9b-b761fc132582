NAME : RC101-31-1
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 31
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 30

NODE_COORD_SECTION
 0 8.0 10.0
 1 4.4 15.0
 2 4.0 17.0
 3 3.0 15.0
 4 3.0 16.0
 5 2.0 7.0
 6 1.0 9.0
 7 7.0 1.0
 8 19.0 6.0
 9 18.0 7.0
 10 17.6 7.0
 11 17.0 5.0
 12 12.0 16.0
 13 11.0 17.0
 14 3.6 16.0
 15 8.4 1.0
 16 5.0 6.0
 17 10.0 7.0
 18 7.0 8.0
 19 12.8 8.4
 20 8.0 12.0
 21 12.6 13.0
 22 1.6 11.2
 23 1.2 13.6
 24 11.4 5.8
 25 12.6 4.6
 26 13.4 1.0
 27 7.4 9.4
 28 12.2 10.4
 29 11.2 7.4
 30 0.8 3.6

DEMAND_SECTION
 0 0 0      
 1 8.5 0.0      // 仅送货需求客户 - medium包裹
 2 4.3 0.0      // 仅送货需求客户 - light包裹
 3 1.0 1.0      // 送货取货双需求客户 - light包裹
 4 7.9 0.0      // 仅送货需求客户 - medium包裹
 5 11.8 0.0      // 仅送货需求客户 - heavy包裹
 6 2.4 0.0      // 仅送货需求客户 - light包裹
 7 0.0 10.6      // 仅取货需求客户 - heavy包裹
 8 3.0 2.8      // 送货取货双需求客户 - light包裹
 9 0.0 4.4      // 仅取货需求客户 - light包裹
 10 3.8 3.8      // 送货取货双需求客户 - light包裹
 11 3.5 0.0      // 仅送货需求客户 - light包裹
 12 4.3 4.1      // 送货取货双需求客户 - light包裹
 13 4.1 4.1      // 送货取货双需求客户 - light包裹
 14 0.0 4.2      // 仅取货需求客户 - light包裹
 15 3.8 0.0      // 仅送货需求客户 - light包裹
 16 1.8 0.0      // 仅送货需求客户 - light包裹
 17 0.0 1.4      // 仅取货需求客户 - light包裹
 18 1.4 0.0      // 仅送货需求客户 - light包裹
 19 6.4 0.0      // 仅送货需求客户 - medium包裹
 20 2.0 0.0      // 仅送货需求客户 - light包裹
 21 0.0 4.4      // 仅取货需求客户 - light包裹
 22 2.1 0.0      // 仅送货需求客户 - light包裹
 23 7.4 0.0      // 仅送货需求客户 - medium包裹
 24 0.0 2.7      // 仅取货需求客户 - light包裹
 25 2.4 1.1      // 送货取货双需求客户 - light包裹
 26 0.0 2.0      // 仅取货需求客户 - light包裹
 27 3.8 0.0      // 仅送货需求客户 - light包裹
 28 4.6 0.0      // 仅送货需求客户 - light包裹
 29 2.8 0.0      // 仅送货需求客户 - light包裹
 30 17.7 6.5      // 送货取货双需求客户 - heavy包裹

DEPOT_SECTION
 0
-1
EOF