if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.5:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期

# 确定局部搜索概率（随着进化进行逐渐增加）
base_probability = 0.1
adaptive_factor = elapsed_time / self.max_runtime 
search_probability = base_probability + (0.9 * adaptive_factor)

# 确定应用局部搜索的个体数量，采用自适应策略
max_elite_percentage = 0.2
min_elite_percentage = 0.05
elite_percentage = min_elite_percentage + (max_elite_percentage - min_elite_percentage) * adaptive_factor
elite_count = max(1, int(len(population) * elite_percentage))


# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.4 * elapsed_time / self.max_runtime)

# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成

========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1024.38
最小总成本: 932.56 (运行 6)
最大总成本: 1120.05 (运行 10)
总成本标准差: 66.10

========== 算法精度与稳定性分析 ==========
最大偏差: 95.67 (9.34%)
平均偏差: 58.82 (5.74%)
平均求解时间: 304.94秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000897 | 1114.64 |    0.00 | 302.07
     2 |        2 | 0.001042 |  959.71 |    0.00 | 316.95
     3 |        3 | 0.001060 |  943.38 |    0.00 | 302.14
     4 |        4 | 0.000951 | 1052.03 |    0.00 | 302.24
     5 |        5 | 0.000978 | 1022.89 |    0.00 | 302.42
     6 |        6 | 0.001072 |  932.56 |    0.00 | 306.86
     7 |        7 | 0.000947 | 1055.48 |    0.00 | 302.21
     8 |        8 | 0.001024 |  969.29 |    6.80 | 309.97
     9 |        9 | 0.000931 | 1073.82 |    0.00 | 302.17
    10 |       10 | 0.000893 | 1120.05 |    0.00 | 302.35

最佳解详细信息:
运行ID: 6
适应度: 0.001072
总成本: 932.56
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 23, 3, 67, 69, 9, 55, 60, 54, 29, 66, 24, 57, 4, 28, 65, 43, 25, 17, 71, 59, 75, 27, 58, 22, 0, 77, 6, 79, 76, 50, 31, 78, 52, 72, 80, 12, 37, 32, 36, 35, 48, 13, 73, 11, 0, 44, 15, 5, 51, 70, 8, 38, 41, 40, 42, 39, 30, 16, 74, 53, 7, 26, 1, 0, 21, 10, 68, 14, 18, 61, 64, 63, 20, 47, 56, 46, 19, 62, 34, 2, 33, 49, 45, 0)
染色体 2: (0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 23, 3, 67, 69, 9, 55, 60, 66, 24, 57, 65, 43, 25, 59, 58, 22, 0]
  无人机任务:
    从节点 60 发射无人机访问: [54, 29]
    从节点 57 发射无人机访问: [4, 28]
    从节点 25 发射无人机访问: [17, 71]
    从节点 59 发射无人机访问: [75, 27]
路线 2: [0, 77, 76, 78, 80, 12, 36, 35, 73, 11, 0]
  无人机任务:
    从节点 77 发射无人机访问: [6, 79]
    从节点 76 发射无人机访问: [50, 31]
    从节点 78 发射无人机访问: [52, 72]
    从节点 12 发射无人机访问: [37, 32]
    从节点 35 发射无人机访问: [48, 13]
路线 3: [0, 44, 15, 70, 41, 40, 30, 16, 74, 26, 1, 0]
  无人机任务:
    从节点 15 发射无人机访问: [5, 51]
    从节点 70 发射无人机访问: [8, 38]
    从节点 40 发射无人机访问: [42, 39]
    从节点 74 发射无人机访问: [53, 7]
路线 4: [0, 21, 10, 18, 61, 64, 47, 19, 2, 33, 49, 45, 0]
  无人机任务:
    从节点 10 发射无人机访问: [68, 14]
    从节点 64 发射无人机访问: [63, 20]
    从节点 47 发射无人机访问: [56, 46]
    从节点 19 发射无人机访问: [62, 34]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000557 | 1794.41 |    0.00
  10 | 0.000561 | 1783.69 |    0.00
  20 | 0.000565 | 1771.32 |    0.00
  30 | 0.000567 | 1764.86 |    0.00
  40 | 0.000586 | 1705.04 |    0.00
  50 | 0.000588 | 1701.80 |    0.00
  60 | 0.000624 | 1603.36 |    0.00
  70 | 0.000631 | 1584.83 |    0.00
  80 | 0.000657 | 1521.68 |    0.00
  90 | 0.000687 | 1455.52 |    0.00
 100 | 0.000717 | 1394.00 |    0.00
 110 | 0.000717 | 1394.00 |    0.00
 120 | 0.000721 | 1386.56 |    0.00
 130 | 0.000874 | 1144.23 |    0.00
 140 | 0.000876 | 1141.55 |    0.00
 150 | 0.000889 | 1124.23 |    0.00
 160 | 0.000894 | 1118.73 |    0.00
 170 | 0.000895 | 1117.53 |    0.00
 180 | 0.000895 | 1117.15 |    0.00
 190 | 0.000895 | 1117.15 |    0.00
 200 | 0.000896 | 1116.23 |    0.00
 210 | 0.000899 | 1111.97 |    0.00
 220 | 0.000900 | 1111.36 |    0.00
 230 | 0.000921 | 1086.15 |    0.00
 240 | 0.000938 | 1066.23 |    0.00
 250 | 0.000940 | 1064.11 |    0.00
 260 | 0.000940 | 1064.11 |    0.00
 270 | 0.000940 | 1064.11 |    0.00
 280 | 0.000987 | 1013.27 |    0.00
 290 | 0.000991 | 1009.07 |    0.00
 300 | 0.001006 |  993.95 |    0.00
 310 | 0.001017 |  983.73 |    0.00
 320 | 0.001059 |  944.28 |    0.00
 330 | 0.001059 |  944.28 |    0.00
 340 | 0.001059 |  944.28 |    0.00
 350 | 0.001059 |  944.28 |    0.00
 360 | 0.001067 |  937.49 |    0.00
 370 | 0.001067 |  937.47 |    0.00
 380 | 0.001067 |  937.47 |    0.00
 390 | 0.001072 |  932.56 |    0.00
 400 | 0.001072 |  932.56 |    0.00
 410 | 0.001072 |  932.56 |    0.00
 420 | 0.001072 |  932.56 |    0.00
 430 | 0.001072 |  932.56 |    0.00
