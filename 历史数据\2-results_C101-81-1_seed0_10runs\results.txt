# 计算各方法生成的数量
savings_count = int(pop_size * 0.5)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成

if progress_ratio <= 0.2:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.6:
ls_frequency = 20  # 中期
else:
ls_frequency = 10  # 后期


# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)
========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1021.96
最小总成本: 930.65 (运行 2)
最大总成本: 1175.01 (运行 10)
总成本标准差: 66.42

========== 算法精度与稳定性分析 ==========
最大偏差: 244.36 (26.26%)
平均偏差: 91.31 (9.81%)
平均求解时间: 302.35秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000978 | 1022.67 |    0.00 | 304.02
     2 |        2 | 0.001075 |  930.65 |    0.00 | 302.05
     3 |        3 | 0.000987 | 1013.43 |    0.00 | 301.71
     4 |        4 | 0.000977 | 1023.62 |    0.00 | 301.57
     5 |        5 | 0.001022 |  978.74 |    0.00 | 302.23
     6 |        6 | 0.000984 | 1015.87 |    0.00 | 303.43
     7 |        7 | 0.000938 | 1066.21 |    0.00 | 301.91
     8 |        8 | 0.000947 | 1056.44 |    0.00 | 301.82
     9 |        9 | 0.001067 |  936.96 |    0.00 | 303.10
    10 |       10 | 0.000851 | 1175.01 |    0.00 | 301.67

最佳解详细信息:
运行ID: 2
适应度: 0.001075
总成本: 930.65
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 17, 16, 18, 19, 22, 24, 21, 20, 23, 30, 31, 28, 26, 29, 27, 25, 0, 72, 70, 71, 69, 64, 62, 57, 63, 61, 58, 56, 65, 66, 67, 68, 75, 73, 77, 76, 74, 79, 80, 78, 10, 12, 13, 14, 15, 11, 9, 7, 6, 0, 8, 5, 3, 2, 60, 4, 1, 0, 34, 39, 40, 41, 37, 36, 38, 35, 33, 32, 52, 50, 49, 53, 59, 48, 51, 44, 45, 42, 46, 47, 43, 54, 55, 0)
染色体 2: (0, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 17, 18, 22, 21, 30, 28, 26, 25, 0]
  无人机任务:
    从节点 17 发射无人机访问: [16]
    从节点 18 发射无人机访问: [19]
    从节点 22 发射无人机访问: [24]
    从节点 21 发射无人机访问: [20, 23]
    从节点 30 发射无人机访问: [31]
    从节点 26 发射无人机访问: [29, 27]
路线 2: [0, 72, 70, 69, 64, 63, 61, 65, 66, 67, 68, 75, 76, 79, 10, 12, 13, 14, 9, 7, 6, 0]
  无人机任务:
    从节点 70 发射无人机访问: [71]
    从节点 64 发射无人机访问: [62, 57]
    从节点 61 发射无人机访问: [58, 56]
    从节点 75 发射无人机访问: [73, 77]
    从节点 76 发射无人机访问: [74]
    从节点 79 发射无人机访问: [80, 78]
    从节点 14 发射无人机访问: [15, 11]
路线 3: [0, 8, 3, 60, 0]
  无人机任务:
    从节点 8 发射无人机访问: [5]
    从节点 3 发射无人机访问: [2]
    从节点 60 发射无人机访问: [4, 1]
路线 4: [0, 34, 39, 37, 35, 33, 52, 50, 49, 48, 44, 46, 54, 55, 0]
  无人机任务:
    从节点 39 发射无人机访问: [40, 41]
    从节点 37 发射无人机访问: [36, 38]
    从节点 33 发射无人机访问: [32]
    从节点 49 发射无人机访问: [53, 59]
    从节点 48 发射无人机访问: [51]
    从节点 44 发射无人机访问: [45, 42]
    从节点 46 发射无人机访问: [47, 43]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000504 | 1983.80 |    0.00
  10 | 0.000504 | 1983.80 |    0.00
  20 | 0.000504 | 1983.80 |    0.00
  30 | 0.000504 | 1983.80 |    0.00
  40 | 0.000514 | 1943.86 |    0.00
  50 | 0.000514 | 1943.86 |    0.00
  60 | 0.000521 | 1920.13 |    0.00
  70 | 0.000521 | 1910.41 |    7.64
  80 | 0.000523 | 1913.82 |    0.00
  90 | 0.000535 | 1869.42 |    0.00
 100 | 0.000562 | 1778.89 |    0.00
 110 | 0.000562 | 1778.89 |    0.00
 120 | 0.000562 | 1778.64 |    0.00
 130 | 0.000565 | 1769.34 |    0.00
 140 | 0.000566 | 1766.25 |    0.00
 150 | 0.000581 | 1721.66 |    0.00
 160 | 0.000581 | 1721.41 |    0.00
 170 | 0.000581 | 1721.41 |    0.00
 180 | 0.000581 | 1721.41 |    0.00
 190 | 0.000581 | 1721.41 |    0.00
 200 | 0.000661 | 1513.70 |    0.00
 210 | 0.000677 | 1476.38 |    0.00
 220 | 0.000754 | 1326.96 |    0.00
 230 | 0.000754 | 1326.96 |    0.00
 240 | 0.000754 | 1326.96 |    0.00
 250 | 0.000759 | 1317.05 |    0.00
 260 | 0.000790 | 1265.69 |    0.00
 270 | 0.000797 | 1255.12 |    0.00
 280 | 0.000814 | 1228.72 |    0.00
 290 | 0.000816 | 1225.69 |    0.00
 300 | 0.000816 | 1225.69 |    0.00
 310 | 0.000854 | 1170.77 |    0.00
 320 | 0.000911 | 1098.00 |    0.00
 330 | 0.000911 | 1098.00 |    0.00
 340 | 0.000945 | 1057.93 |    0.00
 350 | 0.000945 | 1057.93 |    0.00
 360 | 0.000955 | 1047.42 |    0.00
 370 | 0.000993 | 1006.77 |    0.00
 380 | 0.000993 | 1006.77 |    0.00
 390 | 0.000993 | 1006.77 |    0.00
 400 | 0.001015 |  984.77 |    0.00
 410 | 0.001044 |  957.90 |    0.00
 420 | 0.001060 |  943.28 |    0.00
 430 | 0.001073 |  931.84 |    0.00
