import unittest
import random
import copy
from data_loader import Problem
from initialization import initialize_population
from operators import mutation
import numpy as np

class TestDifferentialEvolutionMutation(unittest.TestCase):
    """测试基于差分向量机制的变异算子"""
    
    def setUp(self):
        """初始化测试环境"""
        # 设置随机种子以确保测试可重复
        random.seed(42)
        np.random.seed(42)
        
        # 加载问题实例
        self.problem = Problem("A-n32-k2-d4.vrp")
        
        # 初始化种群，大小为4
        self.population = initialize_population(self.problem, 4)
        
        # 确保所有个体都有初始适应度
        for ind in self.population:
            if not hasattr(ind, 'fitness') or ind.fitness == -float('inf'):
                ind.fitness = 0.001

    def test_mutation_structure_with_content(self):
        """测试变异前后个体染色体内容的变化"""
        # 复制一个个体进行测试
        test_individual = copy.deepcopy(self.population[0])
        
        # 变异前染色体信息
        original_path, original_service = test_individual.chromosomes
        
        print("\n=== 变异前个体染色体内容 ===")
        print(f"路径层: {original_path}")
        print(f"服务方式层: {original_service}")
        
        # 应用变异
        mutation(test_individual, self.problem, self.population)
        
        # 变异后染色体信息
        mutated_path, mutated_service = test_individual.chromosomes
        
        print("\n=== 变异后个体染色体内容 ===")
        print(f"路径层: {mutated_path}")
        print(f"服务方式层: {mutated_service}")
        
        # 找出路径变化的位置
        path_diff_indices = [i for i in range(min(len(original_path), len(mutated_path))) 
                            if original_path[i] != mutated_path[i]]
        
        # 找出服务方式变化的位置
        service_diff_indices = [i for i in range(min(len(original_service), len(mutated_service))) 
                                if original_service[i] != mutated_service[i]]
        
        print("\n=== 染色体变化分析 ===")
        print(f"路径层变化位置索引: {path_diff_indices}")
        print(f"服务方式层变化位置索引: {service_diff_indices}")
        
        # 显示路径层具体变化
        if path_diff_indices:
            print("\n路径层变化详情:")
            for idx in path_diff_indices:
                if idx < len(original_path) and idx < len(mutated_path):
                    print(f"  位置 {idx}: {original_path[idx]} -> {mutated_path[idx]}")
        
        # 显示服务方式层具体变化
        if service_diff_indices:
            print("\n服务方式层变化详情:")
            for idx in service_diff_indices:
                if idx < len(original_service) and idx < len(mutated_service):
                    print(f"  位置 {idx}: {original_service[idx]} -> {mutated_service[idx]}")
        
        # 测试染色体结构
        self.assertEqual(len(mutated_path), len(mutated_service), 
                        "变异后路径层和服务方式层长度不一致")

    def test_mutation_structure(self):
        """测试变异后个体结构的完整性"""
        # 复制一个个体进行测试
        test_individual = copy.deepcopy(self.population[0])
        
        # 原始染色体信息
        original_path, original_service = test_individual.chromosomes
        print(f"原始路径层长度: {len(original_path)}")
        print(f"原始服务方式层长度: {len(original_service)}")
        
        # 应用变异
        mutation(test_individual, self.problem, self.population)
        
        # 变异后染色体信息
        mutated_path, mutated_service = test_individual.chromosomes
        
        # 测试染色体结构
        self.assertEqual(len(mutated_path), len(mutated_service), 
                        "变异后路径层和服务方式层长度不一致")
        
        # 输出变异前后的结构差异
        print(f"变异后路径层长度: {len(mutated_path)}")
        print(f"变异后服务方式层长度: {len(mutated_service)}")
    
    def test_mutation_constraints(self):
        """测试变异后个体是否满足基本约束"""
        for i, individual in enumerate(self.population):
            # 保存原始适应度
            original_fitness = individual.fitness
            
            # 复制个体进行测试
            test_individual = copy.deepcopy(individual)
            
            # 应用变异
            mutation(test_individual, self.problem, self.population)
            
            # 验证变异后的个体
            path_layer, service_layer = test_individual.chromosomes
            
            # 1. 服务方式层只包含0和1
            for service in service_layer:
                self.assertIn(service, [0, 1], "服务方式层包含无效值")
                
            # 2. 配送中心的服务方式必须为0
            for i, node in enumerate(path_layer):
                if node == 0:
                    self.assertEqual(service_layer[i], 0, "配送中心的服务方式不为0")
                    
            # 3. 检查是否包含所有客户点
            customer_nodes = set(range(1, self.problem.dimension))
            path_nodes = set(node for node in path_layer if node != 0)
            self.assertEqual(customer_nodes, path_nodes, "路径中缺少客户点或包含无效客户点")
            
            # 输出适应度变化
            print(f"个体 {i}: 变异前适应度 = {original_fitness:.6f}, "
                  f"变异后适应度 = {test_individual.fitness:.6f}")
    
    def test_mutation_effectiveness(self):
        """测试变异的有效性，即某些情况下应该提高适应度"""
        # 统计变异后适应度提高的个体数量
        improved_count = 0
        
        # 多次测试以增加统计可靠性
        num_tests = 20
        
        for _ in range(num_tests):
            # 随机选择一个个体进行测试
            individual = random.choice(self.population)
            original_fitness = individual.fitness
            
            # 复制个体进行变异
            test_individual = copy.deepcopy(individual)
            
            # 应用变异
            mutation(test_individual, self.problem, self.population)
            
            # 检查适应度是否提高
            if test_individual.fitness > original_fitness:
                improved_count += 1
                
        # 输出统计结果
        print(f"在 {num_tests} 次测试中，变异提高适应度的次数: {improved_count}")
        
        # 在某些情况下应该能提高适应度（至少1次）
        self.assertGreater(improved_count, 0, "变异算子未能提高任何个体的适应度")
        
    def test_mutation_deterministic(self):
        """测试变异的确定性，即相同种子下应产生相同结果"""
        # 设置相同的随机种子
        random.seed(42)
        
        # 复制两个相同的个体
        individual1 = copy.deepcopy(self.population[0])
        individual2 = copy.deepcopy(self.population[0])
        
        # 对两个个体分别应用变异（使用相同的种群）
        mutation(individual1, self.problem, self.population)
        
        # 重置随机种子
        random.seed(42)
        
        # 对第二个个体应用变异
        mutation(individual2, self.problem, self.population)
        
        # 验证两次变异结果一致
        self.assertEqual(individual1.chromosomes, individual2.chromosomes, 
                        "相同种子下变异结果不一致")
        self.assertEqual(individual1.fitness, individual2.fitness,
                        "相同种子下变异后适应度不一致")
        
    

if __name__ == '__main__':
    unittest.main()