#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试具体的失败案例
"""

from data_loader import Individual

def test_specific_failing_case():
    """测试具体的失败案例"""
    print("=" * 80)
    print("测试具体的失败案例")
    print("=" * 80)
    
    print("重现测试2: Individual对象输入")
    try:
        chromosomes_tuple = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        print(f"  原始chromosomes_tuple: {chromosomes_tuple}")
        print(f"  类型: {type(chromosomes_tuple)}")
        
        individual = Individual(chromosomes=chromosomes_tuple)
        print(f"  Individual创建成功")
        print(f"  individual.chromosomes: {individual.chromosomes}")
        print(f"  individual.chromosomes类型: {type(individual.chromosomes)}")
        
        # 检查chromosomes的每个元素
        if hasattr(individual, 'chromosomes') and individual.chromosomes:
            path_layer, service_layer = individual.chromosomes
            print(f"  path_layer: {path_layer}, 类型: {type(path_layer)}")
            print(f"  service_layer: {service_layer}, 类型: {type(service_layer)}")
            print(f"  path_layer是列表: {isinstance(path_layer, list)}")
            print(f"  service_layer是列表: {isinstance(service_layer, list)}")
        
        # 现在调用解码函数
        print("  调用_decode_extended...")
        result = Individual._decode_extended(individual)
        print(f"  ✅ 成功: {len(result)} 个路径段")
        
    except Exception as e:
        print(f"  ❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    print()

def test_step_by_step_validation():
    """逐步测试验证过程"""
    print("=" * 80)
    print("逐步测试验证过程")
    print("=" * 80)
    
    chromosomes_tuple = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
    individual = Individual(chromosomes=chromosomes_tuple)
    
    print("步骤1: 检查输入是否为None")
    chromosomes = individual
    print(f"  chromosomes is None: {chromosomes is None}")
    
    print("步骤2: 检查是否有chromosomes属性")
    has_attr = hasattr(chromosomes, 'chromosomes')
    print(f"  hasattr(chromosomes, 'chromosomes'): {has_attr}")
    
    if has_attr:
        print("步骤3: 检查chromosomes属性是否为None")
        attr_is_none = chromosomes.chromosomes is None
        print(f"  chromosomes.chromosomes is None: {attr_is_none}")
        
        if not attr_is_none:
            print("步骤4: 提取chromosome_data")
            chromosome_data = chromosomes.chromosomes
            print(f"  chromosome_data: {chromosome_data}")
            print(f"  type(chromosome_data): {type(chromosome_data)}")
            
            print("步骤5: 检查是否为元组或列表且包含两个元素")
            is_valid_format = isinstance(chromosome_data, (tuple, list)) and len(chromosome_data) == 2
            print(f"  isinstance(chromosome_data, (tuple, list)): {isinstance(chromosome_data, (tuple, list))}")
            print(f"  len(chromosome_data): {len(chromosome_data)}")
            print(f"  is_valid_format: {is_valid_format}")
            
            if is_valid_format:
                print("步骤6: 尝试解包")
                try:
                    path_layer, service_layer = chromosome_data
                    print(f"  解包成功")
                    print(f"  path_layer: {path_layer}, type: {type(path_layer)}")
                    print(f"  service_layer: {service_layer}, type: {type(service_layer)}")
                    
                    print("步骤7: 检查是否为列表")
                    path_is_list = isinstance(path_layer, list)
                    service_is_list = isinstance(service_layer, list)
                    print(f"  isinstance(path_layer, list): {path_is_list}")
                    print(f"  isinstance(service_layer, list): {service_is_list}")
                    
                    if not path_is_list or not service_is_list:
                        print(f"  ❌ 验证失败: path_layer和service_layer必须是列表")
                    else:
                        print(f"  ✅ 所有验证通过")
                        
                except Exception as e:
                    print(f"  ❌ 解包失败: {e}")

if __name__ == "__main__":
    test_specific_failing_case()
    test_step_by_step_validation()
