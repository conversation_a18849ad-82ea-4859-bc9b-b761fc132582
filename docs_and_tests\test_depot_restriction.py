#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配送中心(节点0)的无人机操作限制
验证发射点和回收点不能为配送中心的约束
"""

from data_loader import DroneTask

def test_depot_restrictions():
    """测试配送中心限制"""
    print("=" * 70)
    print("配送中心(节点0)无人机操作限制测试")
    print("=" * 70)
    
    print("测试1: 正常的无人机任务（应该成功）")
    try:
        task1 = DroneTask(1, [4, 7], 6, 12, 89.4)
        print(f"✅ 成功创建: 无人机{task1.drone_id}, 发射点{task1.launch_point}, 回收点{task1.recovery_point}")
        print(f"   飞行路径: {task1.get_route_sequence()}")
    except Exception as e:
        print(f"❌ 意外错误: {e}")
    print()
    
    print("测试2: 发射点为配送中心（应该失败）")
    try:
        task2 = DroneTask(2, [5, 9], 0, 8, 67.3)  # launch_point = 0
        print(f"❌ 意外成功: {task2}")
    except ValueError as e:
        print(f"✅ 正确拒绝: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    print()
    
    print("测试3: 回收点为配送中心（应该失败）")
    try:
        task3 = DroneTask(3, [3, 11], 5, 0, 78.9)  # recovery_point = 0
        print(f"❌ 意外成功: {task3}")
    except ValueError as e:
        print(f"✅ 正确拒绝: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    print()
    
    print("测试4: 发射点和回收点都为配送中心（应该失败）")
    try:
        task4 = DroneTask(4, [7, 13], 0, 0, 45.6)  # 都为0
        print(f"❌ 意外成功: {task4}")
    except ValueError as e:
        print(f"✅ 正确拒绝: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    print()
    
    print("测试5: 回收点为None（应该成功）")
    try:
        task5 = DroneTask(5, [8, 14], 10, None, 56.7)
        print(f"✅ 成功创建: 无人机{task5.drone_id}, 发射点{task5.launch_point}, 回收点{task5.recovery_point}")
        print(f"   飞行路径: {task5.get_route_sequence()}")
    except Exception as e:
        print(f"❌ 意外错误: {e}")
    print()

def test_drone_tasks_structure_with_restriction():
    """测试drone_tasks结构中的限制"""
    print("=" * 70)
    print("drone_tasks结构限制测试")
    print("=" * 70)
    
    print("测试1: 正常的drone_tasks结构")
    try:
        drone_tasks_valid = {
            6: {  # 发射点6（非配送中心）
                'drone_tasks': [
                    DroneTask(1, [4, 7, 11], 6, 12, 156.8),
                    DroneTask(2, [3, 9], 6, 12, 89.4),
                ],
                'recovery_point': 12
            },
            15: {  # 发射点15（非配送中心）
                'drone_tasks': [
                    DroneTask(3, [14, 17], 15, 22, 134.5),
                ],
                'recovery_point': 22
            }
        }
        print("✅ 成功创建正常的drone_tasks结构")
        
        # 显示结构
        for launch_point, task_info in drone_tasks_valid.items():
            print(f"   发射点{launch_point} → 回收点{task_info['recovery_point']}")
            for task in task_info['drone_tasks']:
                route = task.get_route_sequence()
                print(f"     无人机{task.drone_id}: {' → '.join(map(str, route))}")
        
    except Exception as e:
        print(f"❌ 意外错误: {e}")
    print()
    
    print("测试2: 尝试创建包含配送中心发射的drone_tasks（应该失败）")
    try:
        drone_tasks_invalid = {
            0: {  # 发射点0（配送中心）- 应该失败
                'drone_tasks': [
                    DroneTask(1, [4, 7], 0, 12, 89.4),  # 这里会抛出异常
                ],
                'recovery_point': 12
            }
        }
        print("❌ 意外成功创建了无效结构")
    except ValueError as e:
        print(f"✅ 正确拒绝配送中心发射: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    print()
    
    print("测试3: 尝试创建包含配送中心回收的drone_tasks（应该失败）")
    try:
        drone_tasks_invalid2 = {
            8: {  # 发射点8（正常）
                'drone_tasks': [
                    DroneTask(2, [5, 9], 8, 0, 67.3),  # 回收点0（配送中心）- 应该失败
                ],
                'recovery_point': 0
            }
        }
        print("❌ 意外成功创建了无效结构")
    except ValueError as e:
        print(f"✅ 正确拒绝配送中心回收: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    print()

def test_edge_cases():
    """测试边界情况"""
    print("=" * 70)
    print("边界情况测试")
    print("=" * 70)
    
    print("测试1: 负数节点ID（应该成功）")
    try:
        task1 = DroneTask(1, [4], -1, 5, 45.0)  # 负数发射点
        print(f"✅ 成功: 发射点{task1.launch_point}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    print()
    
    print("测试2: 大数节点ID（应该成功）")
    try:
        task2 = DroneTask(2, [7], 999, 1000, 78.0)  # 大数节点
        print(f"✅ 成功: 发射点{task2.launch_point}, 回收点{task2.recovery_point}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    print()
    
    print("测试3: 空客户序列 + 配送中心限制")
    try:
        task3 = DroneTask(3, [], 0, 5, 0.0)  # 空客户序列但发射点为0
        print(f"❌ 意外成功: {task3}")
    except ValueError as e:
        print(f"✅ 正确拒绝: {e}")
    print()

def demonstrate_valid_scenarios():
    """演示有效的VRP-D场景"""
    print("=" * 70)
    print("有效VRP-D场景演示")
    print("=" * 70)
    
    print("场景: 卡车从配送中心出发，在客户点发射/回收无人机")
    print("卡车路径: [0, 5, 12, 18, 25, 0]")
    print("无人机操作: 在节点5发射，在节点12回收")
    print()
    
    try:
        # 有效的drone_tasks
        drone_tasks = {
            5: {  # 在客户点5发射（不是配送中心）
                'drone_tasks': [
                    DroneTask(1, [3, 7, 9], 5, 12, 134.5),   # 5→3→7→9→12
                    DroneTask(2, [4, 8], 5, 12, 89.7),       # 5→4→8→12
                ],
                'recovery_point': 12  # 在客户点12回收（不是配送中心）
            },
            18: {  # 在客户点18发射
                'drone_tasks': [
                    DroneTask(3, [15, 20, 22], 18, 25, 156.8),  # 18→15→20→22→25
                ],
                'recovery_point': 25  # 在客户点25回收
            }
        }
        
        print("✅ 成功创建有效的VRP-D场景:")
        for launch_point, task_info in drone_tasks.items():
            print(f"  发射点{launch_point} → 回收点{task_info['recovery_point']}")
            for task in task_info['drone_tasks']:
                route = task.get_route_sequence()
                print(f"    无人机{task.drone_id}: {' → '.join(map(str, route))}")
        
        print("\n说明:")
        print("  - 卡车负责配送中心(0)与客户点之间的运输")
        print("  - 无人机只在客户点之间进行发射、服务和回收操作")
        print("  - 这样的设计更符合实际VRP-D应用场景")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    test_depot_restrictions()
    test_drone_tasks_structure_with_restriction()
    test_edge_cases()
    demonstrate_valid_scenarios()
