import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# 设置中文字体（如果需要）
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
except:
    pass  # 如果没有中文字体，使用默认字体

def load_and_process_data(file_path, algorithm_name):
    """加载并处理数据，返回时间和适应度倒数"""
    df = pd.read_csv(file_path)
    
    # 打印列名用于调试
    print(f"文件 {file_path} 的列名: {df.columns.tolist()}")
    
    # 根据不同算法处理数据
    if algorithm_name == "DE":  # 差分进化算法
        time_col = "Elapsed_Time"
        fitness_col = "Best_Fitness"
    elif algorithm_name == "ALNS":  # ALNS算法
        time_col = "Elapsed_Time"
        fitness_col = "Best_Fitness"
    elif algorithm_name == "PSO":  # PSO算法
        time_col = "Elapsed_Time"
        fitness_col = "Best_Fitness"
    elif algorithm_name == "GA":  # GA算法
        time_col = "Elapsed_Time"
        fitness_col = "Best_Fitness"
    else:
        raise ValueError(f"未知算法: {algorithm_name}")
    
    # 确保列名存在
    if time_col not in df.columns:
        raise ValueError(f"文件 {file_path} 中没有 {time_col} 列")
    if fitness_col not in df.columns:
        raise ValueError(f"文件 {file_path} 中没有 {fitness_col} 列")
    
    # 提取时间和适应度值
    times = df[time_col].values
    fitness = df[fitness_col].values
    
    # 过滤掉无效值
    valid_indices = ~np.isnan(fitness) & (fitness > 0)
    times = times[valid_indices]
    fitness = fitness[valid_indices]
    
    # 计算适应度倒数
    fitness_inverse = 1.0 / fitness
    
    return times, fitness_inverse

def plot_convergence_comparison(file_paths, algorithm_names, max_time=120):
    """绘制算法收敛对比图"""
    plt.figure(figsize=(12, 8))
    
    # 为每个算法加载和处理数据
    for file_path, algorithm_name in zip(file_paths, algorithm_names):
        if not os.path.exists(file_path):
            print(f"警告: 文件 {file_path} 不存在，跳过")
            continue
        
        try:
            times, fitness_inverse = load_and_process_data(file_path, algorithm_name)
            
            # 限制时间范围
            mask = times <= max_time
            times = times[mask]
            fitness_inverse = fitness_inverse[mask]
            
            # 直接绘制所有数据点的曲线，不进行插值
            plt.plot(times, fitness_inverse, linewidth=2, label=algorithm_name)
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
    
    # 设置图表属性
    plt.xlabel('时间 (秒)')
    plt.ylabel('适应度值倒数')
    plt.title('不同算法收敛性能对比')
    plt.legend()
    plt.tight_layout()
    plt.grid(False)  # 不显示网格线
    
    # 确保坐标轴交点在(0,0)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    # 设置x轴刻度，每20秒一个
    plt.xticks(np.arange(0, max_time + 1, 20))
    
    # 确保(0,0)点可见
    x_min, x_max = plt.xlim()
    y_min, y_max = plt.ylim()
    plt.xlim(0, max_time)  # 从0开始
    plt.ylim(0, y_max)     # 从0开始
    
    # 保存图表
    plt.savefig('algorithm_convergence_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

# 主函数
def main():
    # 文件路径
    de_file = "results_RC101-51-1_seed0_10runs/best_run_convergence_by_time.csv"
    alns_file = "best_evolution_data.csv"
    pso_file = "best_run_1_convergence.csv"
    ga_file = "best_trajectory.csv"
    
    # 算法名称
    file_paths = [de_file, alns_file, pso_file, ga_file]
    algorithm_names = ["DE", "ALNS", "PSO", "GA"]
    
    # 绘制对比图
    plot_convergence_comparison(file_paths, algorithm_names)

if __name__ == "__main__":
    main()


