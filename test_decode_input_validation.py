#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解码函数输入验证的潜在错误
"""

from data_loader import Individual

def test_potential_errors():
    """测试可能的输入错误情况"""
    print("=" * 80)
    print("测试解码函数输入验证的潜在错误")
    print("=" * 80)
    
    print("🔍 当前代码行:")
    print("  path_layer, service_layer = chromosomes if not hasattr(chromosomes, 'chromosomes') else chromosomes.chromosomes")
    print()
    
    # 测试情况1: 正常的元组输入
    print("测试1: 正常的元组输入")
    try:
        chromosomes = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况2: Individual对象输入
    print("测试2: Individual对象输入")
    try:
        chromosomes_tuple = ([0, 6, 4, 7, 12, 0], [0, 2, 1, 1, 3, 0])
        individual = Individual(chromosomes_tuple)
        result = Individual._decode_extended(individual)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况3: 错误的输入类型 - 单个列表
    print("测试3: 错误输入 - 单个列表")
    try:
        chromosomes = [0, 6, 4, 7, 12, 0]  # 只有一个列表，不是元组
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况4: 错误的输入类型 - 字符串
    print("测试4: 错误输入 - 字符串")
    try:
        chromosomes = "invalid_input"
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况5: 错误的输入类型 - None
    print("测试5: 错误输入 - None")
    try:
        chromosomes = None
        result = Individual._decode_extended(chromosomes)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况6: 有chromosomes属性但值为None的对象
    print("测试6: 有chromosomes属性但值为None的对象")
    try:
        class FakeIndividual:
            def __init__(self):
                self.chromosomes = None
        
        fake_obj = FakeIndividual()
        result = Individual._decode_extended(fake_obj)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()
    
    # 测试情况7: 有chromosomes属性但不是元组的对象
    print("测试7: 有chromosomes属性但不是元组的对象")
    try:
        class FakeIndividual2:
            def __init__(self):
                self.chromosomes = [1, 2, 3]  # 不是元组
        
        fake_obj = FakeIndividual2()
        result = Individual._decode_extended(fake_obj)
        print(f"  ✅ 成功: {len(result)} 个路径段")
    except Exception as e:
        print(f"  ❌ 错误: {e}")
    print()

def analyze_problems():
    """分析问题和解决方案"""
    print("=" * 80)
    print("问题分析和解决方案")
    print("=" * 80)
    
    print("🚨 发现的问题:")
    print("1. 当输入不是元组时，解包操作会失败")
    print("2. 当Individual对象的chromosomes属性为None时会出错")
    print("3. 当Individual对象的chromosomes不是元组时会出错")
    print("4. 没有对输入类型进行充分验证")
    print()
    
    print("💡 建议的解决方案:")
    print("1. 添加输入类型检查")
    print("2. 添加元组解包的异常处理")
    print("3. 验证chromosomes属性的有效性")
    print("4. 提供更清晰的错误信息")
    print()
    
    print("🔧 改进后的代码建议:")
    print("""
def _decode_extended(chromosomes, problem=None):
    # 输入验证和处理
    if chromosomes is None:
        raise ValueError("输入chromosomes不能为None")
    
    # 提取染色体数据
    if hasattr(chromosomes, 'chromosomes'):
        # Individual对象
        if chromosomes.chromosomes is None:
            raise ValueError("Individual对象的chromosomes属性不能为None")
        chromosome_data = chromosomes.chromosomes
    else:
        # 直接输入
        chromosome_data = chromosomes
    
    # 验证是否为元组且包含两个元素
    if not isinstance(chromosome_data, (tuple, list)) or len(chromosome_data) != 2:
        raise ValueError("chromosomes必须是包含两个元素的元组或列表: (path_layer, service_layer)")
    
    try:
        path_layer, service_layer = chromosome_data
    except (ValueError, TypeError) as e:
        raise ValueError(f"无法解包chromosomes: {e}")
    
    # 验证path_layer和service_layer是否为列表
    if not isinstance(path_layer, list) or not isinstance(service_layer, list):
        raise ValueError("path_layer和service_layer必须是列表")
    
    # 后续处理...
    """)

if __name__ == "__main__":
    test_potential_errors()
    analyze_problems()
