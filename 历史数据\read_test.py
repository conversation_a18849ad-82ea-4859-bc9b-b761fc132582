import pickle
import matplotlib.pyplot as plt
import matplotlib as mpl
import math
from tabulate import tabulate
import matplotlib.font_manager as fm
import numpy as np
import pandas as pd
from data_loader import Problem, Individual

# 配置中文字体支持
mpl.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun'] 
mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 正确加载字体文件的方式
font_path = 'C:/Windows/Fonts/simhei.ttf'  # Windows系统黑体字体路径
font_prop = fm.FontProperties(fname=font_path)
plt.rcParams['font.family'] = font_prop.get_name()

def read_and_analyze_results(filename):
    """
    读取并分析多次运行的结果
    
    参数:
        filename: pickle文件路径
    """
    # 读取pickle文件
    print(f"正在读取文件: {filename}...")
    with open(filename, 'rb') as f:
        all_results = pickle.load(f)
    
    print(f"成功读取了{len(all_results)}次运行的结果。\n")
    
    # 提取关键指标
    costs = [r['total_cost'] for r in all_results]
    fitness_values = [r['fitness'] for r in all_results]
    penalties = [r['penalty'] for r in all_results]
    solve_times = [r['solve_time'] for r in all_results]
    
    # 修改统计数据计算部分
    # 按总成本排序
    sorted_results = sorted(all_results, key=lambda x: x['total_cost'])

    # 计算统计数据
    avg_cost = sum(costs) / len(costs)
    min_cost = min(costs)
    max_cost = max(costs)
    std_cost = math.sqrt(sum((c - avg_cost)**2 for c in costs) / len(costs))

    # 添加最大偏差和平均偏差计算
    max_gap = max_cost - min_cost  # 最大偏差(绝对值)
    max_gap_percentage = (max_gap / min_cost) * 100  # 最大偏差百分比

    # 计算平均偏差
    avg_gap = sum(abs(c - avg_cost) for c in costs) / len(costs)  # 平均偏差(绝对值)
    avg_gap_percentage = (avg_gap / min_cost) * 100  # 平均偏差百分比

    # 打印统计摘要
    print("========== 多次运行结果统计 ==========")
    print(f"运行次数: {len(all_results)}")
    print(f"平均总成本: {avg_cost:.2f}")
    print(f"最小总成本: {min_cost:.2f} (运行 {sorted_results[0]['run_id']})")
    print(f"最大总成本: {max_cost:.2f} (运行 {sorted_results[-1]['run_id']})")
    print(f"总成本标准差: {std_cost:.2f}")

    # 添加新的打印信息
    print("\n========== 算法精度与稳定性分析 ==========")
    print(f"最大偏差: {max_gap:.2f} ({max_gap_percentage:.2f}%)")
    print(f"平均偏差: {avg_gap:.2f} ({avg_gap_percentage:.2f}%)")
    print(f"平均求解时间: {sum(solve_times)/len(solve_times):.2f}秒")
    
    # 创建表格数据
    table_data = []
    for result in all_results:
        table_data.append([
            result['run_id'],
            result['seed'],
            f"{result['fitness']:.6f}",
            f"{result['total_cost']:.2f}",
            f"{result['penalty']:.2f}",
            f"{result['solve_time']:.2f}秒",
        ])
    
    # 打印表格
    print("\n各次运行结果详情:")
    headers = ["运行ID", "随机种子", "适应度", "总成本", "惩罚值", "求解时间"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    # 绘制总成本比较图
    plt.figure(figsize=(10, 6))
    run_ids = [r['run_id'] for r in all_results]
    
    plt.bar(run_ids, costs, color='skyblue')
    plt.axhline(y=avg_cost, color='r', linestyle='-', label=f'平均值: {avg_cost:.2f}')
    
    # 使用英文标签作为备选方案(如果中文显示仍有问题)
    plt.xlabel('Run ID')
    plt.ylabel('Total Cost')
    plt.title('Cost Comparison')
    plt.xticks(run_ids)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('cost_comparison.png', dpi=300)
    plt.show()
    
    # 打印最佳解的路径信息
    best_result = sorted_results[0]
    best_individual = best_result['best_individual']
    
    print("\n========== 最佳解详情 (运行 {}) ==========".format(best_result['run_id']))
    print(f"适应度: {best_result['fitness']:.6f}")
    print(f"总成本: {best_result['total_cost']:.2f}")
    print(f"惩罚值: {best_result['penalty']:.2f}")
    
    # # 添加在打印无人机任务后
    # print("\n========== 染色体信息 ==========")
    # if hasattr(best_individual, 'chromosome'):
    #     print(f"染色体: {best_individual.chromosome}")
    # elif hasattr(best_individual, 'genes'):
    #     print(f"基因: {best_individual.genes}")
    # else:
    #     print("直接打印对象:")
    #     print(best_individual)
        
    #     print("\n对象属性详情:")
    #     for attr, value in vars(best_individual).items():
    #         if attr not in ['routes', 'drone_tasks']:  # 这些已经单独打印了
    #             print(f"  {attr}: {value}")

    # print("\n卡车路径:")
    # for i, route in enumerate(best_individual.routes):
    #     print(f"  车辆 {i+1}: {route}")
    
    # print("\n无人机任务:")
    # if best_individual.drone_tasks:
    #     for launch, targets in best_individual.drone_tasks.items():
    #         print(f"  发射点 {launch} → 任务: {targets}")
    # else:
    #     print("  无无人机任务")
    # 添加染色体信息打印
    print("\n========== 染色体信息 ==========")
    print(f"  路径层: {best_individual.chromosomes[0]}")
    print(f"  服务方式层: {best_individual.chromosomes[1]}")

    print("\n染色体解码结果:")
    try:
        problem = best_result.get('problem')
        if problem is None:
            raise KeyError('problem')
        
        decoded_solution = Individual.decode(best_individual.chromosomes, problem)
        for i, (route, drone_tasks) in enumerate(decoded_solution):
            print(f"  车辆 {i+1} 路径: {route}")
            if drone_tasks:
                print(f"    无人机任务:")
                for launch, targets in drone_tasks.items():
                    print(f"      发射点 {launch} → 任务: {targets}")
            else:
                print(f"    无无人机任务")
    except KeyError:
        print("警告: 无法解码染色体(缺少problem对象)")
        print("显示原始染色体数据:")
        print(f"  路径层: {best_individual.chromosomes[0]}")
        print(f"  服务方式层: {best_individual.chromosomes[1]}")
        
    return all_results, best_result

if __name__ == "__main__":
    # 读取并分析结果
    filename = "results_C101-81-1_seed0_10runs/results.pkl"
    all_results, best_result = read_and_analyze_results(filename)
    
    print("\n程序执行完毕。")