========== R101-81-1 数据集求解结果 ==========

算法配置:
数据集: R_随机_数据集/R101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1105.83
最小总成本: 942.57 (运行 3)
最大总成本: 1398.25 (运行 6)
总成本标准差: 138.98

========== 算法精度与稳定性分析 ==========
最大偏差: 455.68 (48.34%)
平均偏差: 163.25 (17.32%)
平均求解时间: 303.70秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000921 | 1085.78 |    0.00 | 302.44
     2 |        2 | 0.000948 | 1054.72 |    0.00 | 303.24
     3 |        3 | 0.001061 |  942.57 |    0.00 | 303.86
     4 |        4 | 0.001025 |  975.41 |    0.00 | 302.86
     5 |        5 | 0.000886 | 1127.23 |    1.31 | 303.24
     6 |        6 | 0.000715 | 1398.25 |    0.00 | 304.31
     7 |        7 | 0.000933 | 1071.61 |    0.00 | 303.61
     8 |        8 | 0.000767 | 1303.39 |    0.00 | 306.40
     9 |        9 | 0.000882 | 1133.28 |    0.00 | 303.75
    10 |       10 | 0.001035 |  966.02 |    0.00 | 303.33

最佳解详细信息:
运行ID: 3
适应度: 0.001061
总成本: 942.57
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 45, 33, 49, 4, 56, 20, 47, 64, 63, 34, 19, 62, 61, 18, 2, 48, 36, 35, 12, 13, 78, 31, 37, 32, 76, 77, 73, 50, 79, 6, 0, 22, 58, 1, 26, 25, 59, 17, 75, 27, 55, 60, 54, 29, 9, 69, 28, 43, 0, 44, 70, 8, 39, 38, 40, 41, 30, 42, 16, 53, 74, 7, 15, 51, 14, 71, 5, 80, 52, 72, 11, 0, 23, 65, 67, 3, 66, 24, 57, 68, 21, 46, 10, 0)
染色体 2: (0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0)

最佳解路线详情:
路线 1: [0, 45, 33, 4, 47, 64, 63, 62, 2, 48, 36, 35, 78, 31, 76, 50, 79, 6, 0]
  无人机任务:
    从节点 33 发射无人机访问: [49]
    从节点 4 发射无人机访问: [56, 20]
    从节点 63 发射无人机访问: [34, 19]
    从节点 62 发射无人机访问: [61, 18]
    从节点 35 发射无人机访问: [12, 13]
    从节点 31 发射无人机访问: [37, 32]
    从节点 76 发射无人机访问: [77, 73]
路线 2: [0, 22, 58, 25, 75, 55, 60, 9, 69, 28, 43, 0]
  无人机任务:
    从节点 58 发射无人机访问: [1, 26]
    从节点 25 发射无人机访问: [59, 17]
    从节点 75 发射无人机访问: [27]
    从节点 60 发射无人机访问: [54, 29]
路线 3: [0, 44, 70, 8, 40, 42, 16, 53, 74, 7, 15, 51, 5, 80, 11, 0]
  无人机任务:
    从节点 8 发射无人机访问: [39, 38]
    从节点 40 发射无人机访问: [41, 30]
    从节点 51 发射无人机访问: [14, 71]
    从节点 80 发射无人机访问: [52, 72]
路线 4: [0, 23, 65, 67, 24, 57, 68, 10, 0]
  无人机任务:
    从节点 67 发射无人机访问: [3, 66]
    从节点 68 发射无人机访问: [21, 46]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000555 | 1800.44 |    0.00
  10 | 0.000555 | 1800.44 |    0.00
  20 | 0.000555 | 1800.44 |    0.00
  30 | 0.000597 | 1674.65 |    0.00
  40 | 0.000614 | 1629.20 |    0.00
  50 | 0.000614 | 1629.20 |    0.00
  60 | 0.000632 | 1583.14 |    0.00
  70 | 0.000648 | 1544.14 |    0.00
  80 | 0.000660 | 1515.77 |    0.00
  90 | 0.000700 | 1428.26 |    0.00
 100 | 0.000718 | 1392.34 |    0.00
 110 | 0.000718 | 1392.34 |    0.00
 120 | 0.000718 | 1392.34 |    0.00
 130 | 0.000718 | 1392.34 |    0.00
 140 | 0.000718 | 1392.34 |    0.00
 150 | 0.000718 | 1392.34 |    0.00
 160 | 0.000723 | 1383.64 |    0.00
 170 | 0.000735 | 1359.68 |    0.00
 180 | 0.000752 | 1329.99 |    0.00
 190 | 0.000759 | 1317.20 |    0.00
 200 | 0.000939 | 1065.13 |    0.00
 210 | 0.000941 | 1062.30 |    0.00
 220 | 0.000944 | 1059.49 |    0.00
 230 | 0.000952 | 1050.39 |    0.00
 240 | 0.000994 | 1005.73 |    0.00
 250 | 0.001014 |  986.52 |    0.00
 260 | 0.001035 |  966.55 |    0.00
 270 | 0.001035 |  966.55 |    0.00
 280 | 0.001042 |  959.79 |    0.00
 290 | 0.001042 |  959.79 |    0.00
 300 | 0.001042 |  959.79 |    0.00
 310 | 0.001042 |  959.79 |    0.00
 320 | 0.001042 |  959.79 |    0.00
 330 | 0.001042 |  959.79 |    0.00
 340 | 0.001043 |  959.05 |    0.00
 350 | 0.001043 |  959.05 |    0.00
 360 | 0.001059 |  944.10 |    0.00
 370 | 0.001059 |  944.10 |    0.00
 380 | 0.001061 |  942.57 |    0.00
