import random
from typing import List, Dict, Tuple, Set, Optional, Union
from data_loader import Problem, Solution, TruckRoute, DroneTask, Customer, Individual
import utils

def calculate_route_distance(route, problem):
    """
    计算路径的总距离
    
    参数:
        route: 节点序列，例如 [0, 1, 2, 0]
        problem: 问题实例，包含节点坐标信息
    
    返回:
        路径的总距离
    """
    # 如果路径为空或只有一个节点，返回0
    if len(route) <= 1:
        return 0
    
    total_distance = 0
    
    # 遍历路径中的每对相邻节点
    for i in range(len(route) - 1):
        # 获取两个相邻节点
        from_node = route[i]
        to_node = route[i + 1]
        
        # 获取节点坐标
        x1, y1 = problem.locations[from_node]
        x2, y2 = problem.locations[to_node]
        
        # 计算欧几里得距离
        distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
        
        # 累加距离
        total_distance += distance
    
    return total_distance
def ensure_consistency(problem, individual):
    """确保个体染色体和解码结果的一致性"""
    # 先保存当前染色体
    chromosomes = individual.chromosomes

    # print(f"\n调用一致性函数中，此时染色体为：{chromosomes}")
    
    # 解码获取routes和drone_tasks
    decoded = Individual.decode(chromosomes, problem)

    # print(f"\n解码后的结果为：{decoded}")

    routes = [route for route, _ in decoded]

    # print(f"\n调用一致性函数中，此时卡车路径列表为：{routes}")
    
    # 构建无人机任务字典
    drone_tasks = {}
    for _, tasks in decoded:
        for launch_node, targets in tasks.items():
            if launch_node not in drone_tasks:
                drone_tasks[launch_node] = []
            drone_tasks[launch_node].extend(targets)

    # print(f"\n调用一致性函数中，此时无人机任务字典为：{drone_tasks}")
    
    # 使用routes和drone_tasks重新编码染色体
    new_chromosomes = Individual.encode(routes, drone_tasks, problem)
    
    # 更新个体的所有属性
    individual.chromosomes = new_chromosomes
    individual.routes = routes
    individual.drone_tasks = drone_tasks

# ---------------------------------------------------------------------
import random
def check_and_repair_drone_launch_point(problem: Problem, individual: Individual) -> bool:
    """
    检查并修复无人机客户点没有前置发射点的问题。
    
    问题描述：
    变异后，服务方式为1的客户点（无人机服务）可能没有前置的服务方式为0的点（发射点），
    例如路径 (0, 2, 4, 5, 3, 1, 0)，服务方式 (0, 1, 1, 1, 1, 0, 0) 中，客户点2之前无发射点。
    
    实现方法：
    1. 先将染色体分割成多个路径段
    2. 对每个路径段单独检查和修复无人机发射点问题
    3. 将修复后的路径段重新合并成染色体

    参数:
    - problem: 问题实例
    - individual: 个体实例
    
    返回:
    - 是否原本可行（True表示原本就可行，False表示进行了修复）
    """
    # 从个体中获取路径层和服务方式层
    path_layer, service_layer = individual.chromosomes
    
    # 设置标志变量
    is_feasible = True
    
    # 路径分割逻辑
    routes_segments = []  # 存储路径段列表，每段包含(节点,服务方式)元组
    remaining_path = list(zip(path_layer, service_layer))  # 将路径层和服务方式层打包
    
    # 分割染色体为多个路径段
    while remaining_path:
        if len(remaining_path) == 1 and remaining_path[0][0] == 0:
            break
        
        # 找到第一个0（配送中心）的位置
        start_idx = -1
        for i, (node, _) in enumerate(remaining_path):
            if node == 0:
                start_idx = i
                break
        
        if start_idx == -1:  # 没有找到配送中心，停止解码
            break
        
        # 找到下一个0（配送中心）的位置
        end_idx = -1
        for i in range(start_idx + 1, len(remaining_path)):
            if remaining_path[i][0] == 0:
                end_idx = i
                break
        
        if end_idx == -1:  # 没有找到下一个配送中心，取到末尾
            current_segment = remaining_path[start_idx:]
            # 添加终止配送中心（如果不是以0结尾）
            if current_segment[-1][0] != 0:
                current_segment.append((0, 0))
            routes_segments.append(current_segment)
            remaining_path = []  # 清空剩余路径，结束循环
        else:
            # 提取当前路径段（不包括第二个0）
            current_segment = remaining_path[start_idx:end_idx]
            # 添加终止配送中心（手动补充0）
            current_segment.append((0, 0))
            routes_segments.append(current_segment)
            # 更新剩余路径（保留第二个0，作为下一段的起点）
            remaining_path = remaining_path[end_idx:]
        # print(f'将路径层处理后得到的需要修复的路径段有：{routes_segments}')
    
    # 检查并修复每个路径段中的无人机发射点问题
    for segment_idx, segment in enumerate(routes_segments):
        # 提取当前路径段的节点和服务方式
        segment_nodes = [node for node, _ in segment]
        segment_services = [service for _, service in segment]
        
        # 找出所有无人机服务点
        drone_service_points = [i for i, (node, service) in enumerate(segment) 
                               if service == 1 and node != 0]
        
        # 对每个无人机服务点进行检查和修复
        for idx in drone_service_points:
            # 再次确认此点仍是无人机服务点
            if segment[idx][1] != 1 or segment[idx][0] == 0:
                continue
            
            # 向前查找最近的卡车服务点作为发射点
            launch_point_found = False
            
            # 从当前无人机服务点向前查找卡车服务点
            for j in range(idx - 1, -1, -1):
                # 检查是否找到卡车服务的客户点(不是配送中心)
                if segment[j][1] == 0 and segment[j][0] != 0:  # 卡车服务节点，非配送中心
                    launch_point_found = True
                    break
            
            # 如果没有找到前置发射点，需要修复
            if not launch_point_found:
                # 记录问题：这个路径段中的无人机服务点没有发射点
                is_feasible = False
                
                # 修复策略：将无人机服务点改为卡车服务
                node, _ = segment[idx]
                segment[idx] = (node, 0)
    
    # 将修复后的路径段合并回完整的染色体
    merged_path_layer = []
    merged_service_layer = []
    
    for segment in routes_segments:
        # 提取节点和服务方式（除了最后一个配送中心）
        segment_nodes = [node for node, _ in segment[:-1]]
        segment_services = [service for _, service in segment[:-1]]
        
        merged_path_layer.extend(segment_nodes)
        merged_service_layer.extend(segment_services)
    
    # 添加最后一个配送中心
    if routes_segments:
        last_node, last_service = routes_segments[-1][-1]
        merged_path_layer.append(last_node)
        merged_service_layer.append(last_service)
    
    # 更新个体的染色体
    individual.chromosomes = (tuple(merged_path_layer), tuple(merged_service_layer))
    
    # # 确保一致性
    # ensure_consistency(problem, individual)
    
    return is_feasible

from utils import calc_drone_energy
def check_and_repair_drone_task_limit(problem: Problem, individual: Individual) -> bool:
    """
    检查并修复发射点绑定的无人机任务数超过限制的问题。
    返回是否原本可行（True=原本未超限，False=已修复）。
    """
    # print("\n===== 开始修复无人机任务数约束 =====")
    # print(f"修复前染色体: {individual.chromosomes}")
    # 复制染色体为可变列表
    path_layer = list(individual.chromosomes[0])
    service_layer = list(individual.chromosomes[1])
    is_originally_feasible = True  # 标记是否原本可行

    # ------------------------------ 步骤 1: 解码染色体 ------------------------------
    decoded = individual.decode((path_layer, service_layer), problem)
    # print(f"解码结果: {decoded}")
    # print(decoded)=[([0, 1, 0], {1: [2, 3]}), ([0, 4, 5, 7, 9, 0], {5: [6], 7: [8], 9: [10]})]
    routes = [route for route, _ in decoded] 
    # print(f"路径: {routes}") 
    # print(routes)=[[0, 1, 0], [0, 4, 5, 7, 9, 0]]
    drone_tasks = {}  # 无人机任务字典

    # 构建无人机任务字典
    for _, tasks in decoded:
        for launch_node, drone_nodes in tasks.items():
            if launch_node not in drone_tasks:
                drone_tasks[launch_node] = []
            drone_tasks[launch_node].extend(drone_nodes)
    # print(f"无人机任务字典: {drone_tasks}")
    # print(drone_tasks)={1: [2, 3], 5: [6], 7: [8], 9: [10]}

    # ------------------------------ 步骤 2: 获取超限发射点字典 ------------------------------
    excess_launches = {}
    max_drones = problem.num_drones
    # 遍历所有发射点及其任务
    for launch, tasks in drone_tasks.items():
        if len(tasks) > max_drones:
            # 任务数超过限制，放入超限字典
            excess_launches[launch] = tasks.copy()
    # print(f"超限发射点及任务: {excess_launches}")
    # print(f"每个发射点可配备无人机数: {max_drones}")

    # print("超限发射点及任务:", excess_launches)
    # # 输出: 超限发射点及任务: {5: [6, 11, 12]}

    # ------------------------------ 步骤 3: 处理超限发射点：更新无人机任务字典、获得待分配无人机客户点列表 ------------------------------
    to_assign: List[int] = []  # 待分配的客户点列表

    # 遍历所有超限发射点，根据能耗保留任务
    for launch, tasks in excess_launches.items():
        # 标记原本不可行
        is_originally_feasible = False
        
        # 计算所有任务的能耗
        task_energies = []
        for task in tasks:
            # 计算无人机从launch发射点到task客户点的能耗
            energy = calc_drone_energy(problem, launch, task)
            task_energies.append((task, energy))
        
        # 按能耗从小到大排序
        task_energies.sort(key=lambda x: x[1])
        
        # 保留能耗最小的max_drones个任务
        kept_tasks = [task for task, _ in task_energies[:max_drones]]
        excess_tasks = [task for task, _ in task_energies[max_drones:]]
        
        # 更新无人机任务字典
        drone_tasks[launch] = kept_tasks
        # print(drone_tasks)
        # 将多余任务加入待分配列表
        to_assign.extend(excess_tasks)
        # print(to_assign)
        # print(f"处理发射点 {launch} 的超限任务:")
        # print(f"  原任务列表: {tasks}")
        # print(f"  保留任务: {kept_tasks}")
        # print(f"  超限任务: {excess_tasks}")
    # ------------------------------ 步骤 4: 分配待处理客户点（开始修复并返回将处理好的routes和drone_tasks编码成染色体） ------------------------------
    for customer in to_assign:
        # 尝试找到最佳可用发射点（能耗最小的发射点）
        best_launch = None
        min_energy = float('inf')
        
        # 遍历所有发射点，检查是否有剩余容量
        for launch, tasks in drone_tasks.items():
            if len(tasks) < max_drones:  # 还有剩余容量
                # print(len(tasks))
                # 计算客户点到该发射点的能耗
                energy = calc_drone_energy(problem, launch, customer)
                if energy < min_energy:
                    min_energy = energy
                    best_launch = launch
        
        if best_launch is not None:
            # 找到了可用发射点，将客户点分配给能耗最小的发射点
            drone_tasks[best_launch].append(customer)
            # # 打印分配信息（可选）
            # print(f"客户点{customer}分配给发射点{best_launch}，能耗为{min_energy:.2f}")
            # print(f"当前无人机任务字典: {drone_tasks}")
        else:
            # 没有找到可用发射点，将客户点转为卡车客户点
            # 寻找在所有路径中的最佳插入位置（距离增加最小）
            best_route_idx = -1
            best_pos_in_route = -1
            min_distance_increase = float('inf')

            # 遍历所有卡车路径
            for route_idx, route in enumerate(routes):
                # 尝试该路径中所有可能的插入位置
                for i in range(len(route) - 1):
                    # 获取当前相邻节点
                    from_node = route[i]
                    to_node = route[i+1]
                    
                    # 从problem.locations获取节点坐标
                    x1, y1 = problem.locations[from_node]
                    x2, y2 = problem.locations[to_node]
                    x_customer, y_customer = problem.locations[customer]
                    
                    # 计算插入前的原始距离（欧几里得距离）
                    original_dist = ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5
                    
                    # 计算插入后的新距离
                    dist1 = ((x1 - x_customer) ** 2 + (y1 - y_customer) ** 2) ** 0.5  # 前一点到客户点
                    dist2 = ((x_customer - x2) ** 2 + (y_customer - y2) ** 2) ** 0.5  # 客户点到后一点
                    new_dist = dist1 + dist2
                    
                    # 计算距离增加值
                    distance_increase = new_dist - original_dist
                    
                    # 记录距离增加最小的位置
                    if distance_increase < min_distance_increase:
                        min_distance_increase = distance_increase
                        best_route_idx = route_idx
                        best_pos_in_route = i + 1

            # 如果找到了最佳插入位置
            if best_route_idx != -1:
                # 在对应路径中插入客户点
                routes[best_route_idx].insert(best_pos_in_route, customer)
                
                # 添加到无人机任务字典作为潜在发射点
                drone_tasks[customer] = []
                
                # print(f"客户点{customer}转为卡车服务，插入到路径{best_route_idx+1}的位置{best_pos_in_route}，"
                #     f"在{routes[best_route_idx][best_pos_in_route-1]}和{routes[best_route_idx][best_pos_in_route+1]}之间，"
                #     f"距离增加{min_distance_increase:.2f}")
                
        # # 在编码前直接打印routes和drone_tasks
        # print("\n编码前的数据:")
        # print(f"routes: {routes}")
        # print(f"drone_tasks: {drone_tasks}")
        # 使用正确实现的encode函数重建染色体
        chromosomes = Individual.encode(routes, drone_tasks, problem)
        individual.chromosomes = chromosomes
    # print(f"修复后染色体: {individual.chromosomes}")
    # print("===== 无人机任务数约束修复完成 =====\n")

    # # 确保一致性
    # ensure_consistency(problem, individual)

    return is_originally_feasible

def check_and_repair_service_type(problem: Problem, individual: Individual) -> bool:
    """
    检查并修复服务方式分配不合理的问题。

    问题描述： 
    配送中心（0）被分配为服务方式1。
    
    参数:
    - problem: 问题实例
    - individual: 个体实例
    
    返回:
    - 是否修复成功
    """
    path_layer, service_layer = individual.chromosomes
    is_feasible = True
    
    for i, node in enumerate(path_layer):
        if node == 0:  # 配送中心
            if service_layer[i] != 0:
                is_feasible = False
                service_layer[i] = 0  # 强制配送中心服务方式为 0
    
    individual.chromosomes = (path_layer, service_layer)

    # # 确保一致性
    # ensure_consistency(problem, individual)

    return is_feasible

def check_and_repair_path_separation(problem: Problem, individual: Individual) -> bool:
    """
    检查并修复路径分隔不正确的问题（空路径）。
    
    问题描述：
    路径中存在空路径（如[0,0]），或者路径数量少于要求的车辆数。
    修复策略：通过平衡路径长度，将现有路径切分为所需的车辆数量。

    参数:
    - problem: 问题实例
    - individual: 个体实例
    
    返回:
    - 是否原本可行（True表示原本就可行，False表示进行了修复）
    """
    # 解码获取路径和无人机任务
    decoded = individual.decode(individual.chromosomes, problem)
    routes = [route for route, _ in decoded]
    # print(f'不可行解的全部路径为：{routes}')
    
    # 构建无人机任务字典
    drone_tasks = {}
    for _, tasks in decoded:
        for launch_node, drone_nodes in tasks.items():
            if launch_node not in drone_tasks:
                drone_tasks[launch_node] = []
            drone_tasks[launch_node].extend(drone_nodes)
    # print(f'不可行解的全部无人机任务字典为：{drone_tasks}')
    
    # 检查是否有效路径数量符合要求
    valid_routes = [r for r in routes if len(r) > 2]  # 非空路径（长度大于2）
    # print(f'不可行解的有效路径为：{valid_routes}')
    
    is_feasible = len(valid_routes) == problem.num_vehicles
    
    # 如果路径数量正确且都是有效路径，直接返回
    if is_feasible:
        return True
    
    # 修复路径数量问题
    if len(valid_routes) < problem.num_vehicles:
        # 收集卡车路径上的所有卡车客户点
        all_customers = []
        for route in valid_routes:
            all_customers.extend([node for node in route if node != 0])
            # extend() 是列表对象的一个方法，其作用是把一个可迭代对象（像列表、元组、字符串等）里的所有元素添加到当前列表的末尾，从而扩展当前列表。
        
        # 计算每条有效路径的总长度
        # 创建路径距离列表，元素为元组（路径索引，路径距离），例如[(0，120.5)，(1，85.2)，(2，145.3)]
        route_distances = [(i, calculate_route_distance(route, problem)) 
                          for i, route in enumerate(valid_routes)]
        
        # 按距离从大到小排序
        # [(2，145.3)，(0，120.5),(1，85.2)]
        route_distances.sort(key=lambda x: x[1], reverse=True)
        
        # 循环分割路径，直到达到所需的车辆数量
        while len(valid_routes) < problem.num_vehicles:
            # 如果没有有效路径，创建新路径
            if not valid_routes:
                # 采用整除，计算所有客户点平均分配到所需数量的路径
                per_route = max(1, len(all_customers) // problem.num_vehicles)
                for i in range(problem.num_vehicles):
                    start = i * per_route
                    end = start + per_route if i < problem.num_vehicles - 1 else len(all_customers)
                    if start < len(all_customers):
                        customers = all_customers[start:end] # 对all_customers列表进行切片，但得到的新列表不包含end索引对应的元素
                        valid_routes.append([0] + customers + [0]) # 创建有效卡车路径0-客户点-0
                break
            
            # 有有效路径则：
            # 找出最长的路径进行分割，longest_route_idx对应的是valid_routes的索引
            longest_route_idx = route_distances[0][0] # [(2，145.3)，(0，120.5),(1，85.2)]中的(2，145.3)中的2
            longest_route = valid_routes[longest_route_idx]
            
            # 如果路径太短，无法分割，尝试下一条
            if len(longest_route) <= 3:  # 只有起点、一个客户点和终点
                if len(route_distances) > 1:
                    route_distances.pop(0)  # 移除当前最长路径
                    continue
                else:
                    # 无法再分割，创建新路径
                    break
                        
            # 寻找最佳分割点
            best_cut_idx = 0 # 初始化最佳分割点索引
            min_diff = float('inf') # 初始化最小差值为无穷大
            
            for i in range(1, len(longest_route) - 1):
                # 计算分割后两段路径的距离
                route1 = longest_route[:i+1] + [0]  # 第一段 + 返回配送中心
                route2 = [0] + longest_route[i+1:]  # 从配送中心 + 第二段
                
                dist1 = calculate_route_distance(route1, problem)
                dist2 = calculate_route_distance(route2, problem)
                
                # 计算与目标的差异
                diff = abs(dist1 - dist2)
                if diff < min_diff:
                    min_diff = diff
                    best_cut_idx = i
            
            # 执行路径分割
            route1 = longest_route[:best_cut_idx+1] + [0]
            route2 = [0] + longest_route[best_cut_idx+1:]
            
            # 更新路径列表
            valid_routes[longest_route_idx] = route1
            valid_routes.append(route2)
            # print(valid_routes)
            
            # 更新距离列表
            route_distances[0] = (longest_route_idx, calculate_route_distance(route1, problem))
            route_distances.append((len(valid_routes) - 1, calculate_route_distance(route2, problem)))
            route_distances.sort(key=lambda x: x[1], reverse=True)



    # 检查并处理路径重叠（同一客户点出现在多条路径中）
    customer_routes = {}
    for i, route in enumerate(valid_routes):
        for node in route:
            if node != 0:  # 不考虑配送中心
                if node not in customer_routes:
                    customer_routes[node] = []
                customer_routes[node].append(i)
    
    # 处理重复客户点
    for node, route_indices in customer_routes.items():
        if len(route_indices) > 1:
            # 随机选择一条保留此客户点的路径
            keep_idx = random.choice(route_indices)
            
            # 从其他路径中移除此客户点
            for idx in route_indices:
                if idx != keep_idx:
                    route = valid_routes[idx]
                    if node in route and len(route) > 3:  # 确保移除后路径仍有客户点
                        route.remove(node)
    
    # 确保所有路径都是有效路径（至少有一个客户点）
    for i, route in enumerate(valid_routes):
        if len(route) <= 2:  # 路径不包含客户点
            available_nodes = [n for n in range(1, problem.dimension) 
                              if n not in [node for r_idx, r in enumerate(valid_routes) 
                                          for node in r if r_idx != i]]
            if available_nodes:
                # 添加一个未使用的客户点
                new_node = random.choice(available_nodes)
                valid_routes[i] = [0, new_node, 0]
    
    # 重新编码染色体
    individual.chromosomes = Individual.encode(valid_routes, drone_tasks, problem)

    # # 确保一致性
    # ensure_consistency(problem, individual)
    
    return is_feasible

def check_and_repair_drone_payload(problem: Problem, individual: Individual) -> bool:
    """
    检查并修复无人机客户点需求量大于无人机最大载重的问题。
    
    问题描述：
    当无人机客户点的需求量超过无人机最大载重时，无人机无法服务该客户点。
    修复策略：将该客户点的服务方式改为卡车服务。
    
    参数:
    - problem: 问题实例
    - individual: 个体
    
    返回:
    - 是否原本可行（True表示原本就可行，False表示进行了修复）
    """
    # 从染色体解码获取路径和服务方式
    path_layer, service_layer = individual.chromosomes
    path_layer = list(path_layer)
    service_layer = list(service_layer)
    
    # 无人机最大载重
    drone_max_payload = problem.drone_params['max_load']
    
    # 标记是否原本可行
    is_feasible = True
    
    # 检查并修复无人机客户点的需求量
    for idx, (node, service_type) in enumerate(zip(path_layer, service_layer)):
        # 只检查无人机服务的客户点
        if service_type == 1 and node != 0:
            # 获取客户点的需求量
            pickup, delivery = problem.demands.get(node, (0, 0))
            
            # 检查是否超过无人机最大载重
            if max(pickup, delivery) > drone_max_payload:
                # 需要修复：将无人机服务改为卡车服务
                service_layer[idx] = 0
                is_feasible = False  # 标记为不可行，已修复
                
                # 可以在这里添加日志
                # print(f"客户点{node}的需求量({max(pickup, delivery)}kg)超过无人机最大载重({drone_max_payload}kg)，已修复为卡车服务")
    
    # 如果进行了修复，更新染色体
    if not is_feasible:
        individual.chromosomes = (tuple(path_layer), tuple(service_layer))
        
        # 解码更新routes和drone_tasks
        decoded = individual.decode(individual.chromosomes, problem)
        individual.routes = [route for route, _ in decoded]
        
        # 合并无人机任务
        individual.drone_tasks = {}
        for _, tasks in decoded:
            for launch_node, targets in tasks.items():
                if launch_node in individual.drone_tasks:
                    individual.drone_tasks[launch_node].extend(targets)
                else:
                    individual.drone_tasks[launch_node] = targets
    
    # 返回是否原本可行
    return is_feasible
# ---------------------------------------------------------------------
