初始400多总成本，20代内改进速率快

========== 第20代最优解详情 ==========
适应度: 0.002861
总成本: 349.58
惩罚值: 0.00

========== 多次运行结果统计 ==========
运行次数: 1
平均总成本: 321.54
最小总成本: 321.54 (运行 1)
最大总成本: 321.54 (运行 1)
总成本标准差: 0.00

========== 算法精度与稳定性分析 ==========
最大偏差: 0.00 (0.00%)
平均偏差: 0.00 (0.00%)
平均求解时间: 58.96秒

各次运行结果详情:
+----------+------------+----------+----------+----------+------------+
|   运行ID |   随机种子 |   适应度 |   总成本 |   惩罚值 | 求解时间   |
+==========+============+==========+==========+==========+============+
|        1 |          1 |  0.00311 |   321.54 |        0 | 58.96秒    |
+----------+------------+----------+----------+----------+------------+

========== 最佳解详情 (运行 1) ==========
适应度: 0.003110
总成本: 321.54
惩罚值: 0.00

========== 染色体信息 ==========
  路径层: (0, 10, 3, 15, 28, 18, 9, 11, 4, 8, 2, 23, 17, 1, 0, 22, 29, 26, 13, 21, 19, 7, 6, 31, 16, 25, 5, 14, 12, 30, 24, 2
7, 20, 0)                                                                                                                      服务方式层: (0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0)

染色体解码结果:
  车辆 1 路径: [0, 10, 28, 11, 2, 1, 0]
    无人机任务:
      发射点 10 → 任务: [3, 15]
      发射点 28 → 任务: [18, 9]
      发射点 11 → 任务: [4, 8]
      发射点 2 → 任务: [23, 17]
  车辆 2 路径: [0, 22, 13, 7, 16, 14, 24, 0]
    无人机任务:
      发射点 22 → 任务: [29, 26]
      发射点 13 → 任务: [21, 19]
      发射点 7 → 任务: [6, 31]
      发射点 16 → 任务: [25, 5]
      发射点 14 → 任务: [12, 30]
      发射点 24 → 任务: [27, 20]

程序执行完毕。
