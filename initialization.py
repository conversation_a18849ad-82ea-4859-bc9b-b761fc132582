from typing import List, Dict, Tuple, Set
import random
import copy
from data_loader import Problem, Individual
from utils import euclidean_distance, calc_drone_energy

def initialize_population(problem: Problem, pop_size: int, use_extended_encoding: bool = False) -> List[Individual]:
    """
    初始化种群：改进的节约算法(40%) + 随机生成(60%)，并优化部分解以支持无人机任务
    适应度值未计算，修复算子未调用。

    参数:
        problem: 问题实例
        pop_size: 种群大小
        use_extended_encoding: 是否使用扩展编码模式（支持异步发射-回收）
    """
    population = []

    # 计算各方法生成的数量
    savings_count = int(pop_size * 0.6)  # 50%使用改进的节约算法
    random_count = pop_size - savings_count  # 50%使用随机生成

    # 1. 使用改进的节约算法生成60%的解决方案
    for _ in range(savings_count):
        # 生成基础解决方案
        routes = improved_savings_algorithm(problem)
        # 转换部分客户为无人机任务
        if use_extended_encoding:
            drone_tasks = assign_drone_tasks_extended(routes, problem)
        else:
            drone_tasks = assign_drone_tasks(routes, problem)
        # 添加到种群
        if use_extended_encoding:
            # 使用扩展编码创建个体
            chromosomes = Individual.encode(routes, drone_tasks, problem, use_extended_encoding=True)
            population.append(Individual(chromosomes=chromosomes))
        else:
            population.append(Individual(routes, drone_tasks))

    # 2. 使用随机方法生成40%的解决方案
    for _ in range(random_count):
        # 生成随机解决方案
        routes = random_solution_generator(problem)
        # 转换部分客户为无人机任务
        if use_extended_encoding:
            drone_tasks = assign_drone_tasks_extended(routes, problem)
        else:
            drone_tasks = assign_drone_tasks(routes, problem)
        # 添加到种群
        if use_extended_encoding:
            # 使用扩展编码创建个体
            chromosomes = Individual.encode(routes, drone_tasks, problem, use_extended_encoding=True)
            population.append(Individual(chromosomes=chromosomes))
        else:
            population.append(Individual(routes, drone_tasks))

    return population

def improved_savings_algorithm(problem: Problem) -> List[List[int]]:
    """
    改进的节约算法，添加随机因素以增加多样性
    返回: 路径列表，每个路径为一个节点序列（起点和终点都是仓库0）
    """
    # 获取客户节点编号（不包括仓库0）
    customers = list(problem.demands.keys())
    # print(customers)  # 输出结果：[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
    if 0 in customers:
        customers.remove(0)
    
    truck_capacity = problem.truck_params['max_load']
    num_vehicles = problem.truck_params['num_vehicles']
    
    # 初始化路径，每个客户一条独立路径
    routes = [[0, i, 0] for i in customers]
    # print(routes)  # [[0, 1, 0], [0, 2, 0],..., [0, 31, 0]]

    # 计算所有客户对之间的节约值
    savings = []
    for i in customers:
        for j in customers:
            if i >= j:  # 避免重复计算
                continue
            
            # 计算节约值: d(0,i) + d(0,j) - d(i,j)
            d_0i = euclidean_distance(problem.locations[0], problem.locations[i])
            d_0j = euclidean_distance(problem.locations[0], problem.locations[j])
            d_ij = euclidean_distance(problem.locations[i], problem.locations[j])
            saving = d_0i + d_0j - d_ij
            
            # 添加随机扰动(-20%到+20%)增加多样性
            saving = saving * (1 + random.uniform(-0.2, 0.2))
            savings.append((saving, i, j))
    
    # 按节约值从大到小排序
    savings.sort(reverse=True)
    
    # 随机打乱前30%的节约值，进一步增加多样性
    shuffle_count = int(len(savings) * 0.3)
    if shuffle_count > 1:
        top_part = savings[:shuffle_count]
        random.shuffle(top_part)
        savings[:shuffle_count] = top_part
    
    # 循环合并路径
    while len(routes) > num_vehicles and savings:
        # 有10%的概率随机选择一个节约值
        if random.random() < 0.1:
            index = random.randint(0, min(10, len(savings) - 1))
            s, i, j = savings.pop(index)
        else:
            s, i, j = savings.pop(0)  # 取节约值最大的
        
                # 查找包含这两个客户的路径
        route_i = None
        route_j = None
        
        for r in routes:
            if len(r) > 2:
                if r[-2] == i:
                    route_i = r
                if r[1] == j:
                    route_j = r
        
        # 如果找到两条符合条件的不同路径，尝试合并
        if route_i and route_j and route_i != route_j and random.random() < 0.9:
            # 创建合并路径
            new_route = route_i[:-1] + route_j[1:]
            
            # 检查合并后的路径是否满足载重约束
            total_demand = 0
            for node in new_route:
                if node != 0:  # 跳过仓库节点
                    delivery, pickup = problem.demands.get(node, (0, 0))
                    total_demand += delivery + pickup
            
            if total_demand <= truck_capacity:
                # 从路径列表中移除原路径
                routes.remove(route_i)
                routes.remove(route_j)
                # 添加合并后的路径
                routes.append(new_route)
        # # 查找包含这两个客户的路径
        # route_i = next((r for r in routes if len(r) > 2 and r[-2] == i), None)
        # route_j = next((r for r in routes if len(r) > 2 and r[1] == j), None)
        
        # # 如果找到两条符合条件的不同路径，尝试合并
        # if route_i and route_j and route_i != route_j and random.random() < 0.9:
        #     # 创建合并路径
        #     new_route = route_i[:-1] + route_j[1:]
            
        #     # 检查合并后的路径是否满足载重约束
        #     total_demand = 0
        #     for node in new_route:
        #         if node != 0:  # 跳过仓库节点
        #             delivery, pickup = problem.demands.get(node, (0, 0))
        #             total_demand += delivery + pickup
            
        #     if total_demand <= truck_capacity:
        #         # 从路径列表中移除原路径
        #         routes.remove(route_i)
        #         routes.remove(route_j)
        #         # 添加合并后的路径
        #         routes.append(new_route)
    
    # 确保选择的路径数量符合要求
    # 如果路径太多，选择最短的合并
    while len(routes) > num_vehicles:
        # 按路径长度排序
        routes.sort(key=lambda r: len(r))
        # 合并最短的两条路径
        if len(routes) >= 2:
            route1, route2 = routes[0], routes[1]
            merged_route = [0]
            merged_route.extend([n for n in route1[1:-1] if n != 0])
            merged_route.extend([n for n in route2[1:-1] if n != 0])
            merged_route.append(0)
            
            # 替换前两条路径
            routes = [merged_route] + routes[2:]
    
    # 如果路径太少，添加空路径
    while len(routes) < num_vehicles:
        routes.append([0, 0])
    
    # 收集所有已分配的客户
    assigned = set()
    for route in routes:
        for node in route:
            if node != 0:
                assigned.add(node)
    
    # 检查是否有未分配的客户
    unassigned = set(customers) - assigned
    
    # 将未分配的客户插入到现有路径中
    for node in unassigned:
        best_insertion = (float('inf'), -1, -1)  # (增加的成本, 路径索引, 插入位置)
        
        for r_idx, route in enumerate(routes):
            # 尝试每个可能的插入位置
            for pos in range(1, len(route)):
                # 计算插入带来的额外距离
                prev, next = route[pos-1], route[pos]
                d_prev_node = euclidean_distance(problem.locations[prev], problem.locations[node])
                d_node_next = euclidean_distance(problem.locations[node], problem.locations[next])
                d_prev_next = euclidean_distance(problem.locations[prev], problem.locations[next])
                additional_dist = d_prev_node + d_node_next - d_prev_next
                
                # 检查是否违反载重约束
                new_route = route.copy()
                new_route.insert(pos, node)
                
                # 计算路径的总需求
                total_demand = 0
                for n in new_route:
                    if n != 0:
                        delivery, pickup = problem.demands.get(n, (0, 0))
                        total_demand += delivery + pickup
                
                # 如果满足约束且成本更低，则更新最佳插入点
                if total_demand <= truck_capacity and additional_dist < best_insertion[0]:
                    best_insertion = (additional_dist, r_idx, pos)
        
        # 执行最佳插入
        if best_insertion[1] != -1:
            routes[best_insertion[1]].insert(best_insertion[2], node)

        else:
            # 如果没有可行的插入，创建新路径（应该不会发生，因为我们已经确保路径数量）
            # 但作为安全措施，替换最短的路径
            routes.sort(key=lambda r: len(r))
            routes[0] = [0, node, 0]
            
    # # 检查生成的基础解 
    # print("\n最终生成的路径 (improved_savings_algorithm):")      
    # for i, route in enumerate(routes):
    #     print(f"节约算法生成的基础解路径 {i+1}为: {route}")
    
    return routes

def random_solution_generator(problem: Problem) -> List[List[int]]:
    """
    随机生成解决方案
    返回: 路径列表，每个路径为一个节点序列
    """
    # 获取参数
    customers = list(problem.demands.keys())
    if 0 in customers:
        customers.remove(0)  # 排除仓库
    
    # 随机打乱客户顺序
    random.shuffle(customers)
    
    num_vehicles = problem.truck_params['num_vehicles']
    truck_capacity = problem.truck_params['max_load']
    
    # 初始化路径
    routes = [[] for _ in range(num_vehicles)]
    route_loads = [0] * num_vehicles
    
    # 确保每条路径至少有一个客户
    for route_idx in range(min(num_vehicles, len(customers))):
        if customers:
            customer = customers.pop(0)
            routes[route_idx] = [0, customer, 0]
            
            # 更新路径载重
            delivery, pickup = problem.demands.get(customer, (0, 0))
            route_loads[route_idx] = delivery + pickup
    
    # 分配剩余客户
    for customer in customers:
        # 计算该客户的需求
        delivery, pickup = problem.demands.get(customer, (0, 0))
        customer_demand = delivery + pickup
        
        # 收集能容纳该客户的路径
        feasible_routes = []
        for i, load in enumerate(route_loads):
            if load + customer_demand <= truck_capacity:
                feasible_routes.append(i)
        
        # 选择最合适的路径
        if feasible_routes:
            # 计算每条路径的载重比例
            load_ratios = [route_loads[i]/truck_capacity for i in feasible_routes]
            # 选择载重比例最小的路径
            chosen_idx = feasible_routes[load_ratios.index(min(load_ratios))]
        else:
            # 如果没有可行路径，选择载重最小的
            chosen_idx = route_loads.index(min(route_loads))
        
        # 更新路径
        # 获取原路径
        route = routes[chosen_idx]
        
        # 在终点前插入客户
        if route:
            route.insert(len(route)-1, customer)
        else:
            route.extend([0, customer, 0])
        
        # 更新路径载重
        route_loads[chosen_idx] += customer_demand
    
    # 确保所有路径都有正确的格式
    for i in range(len(routes)):
        if not routes[i]:
            routes[i] = [0, 0]
        elif routes[i][0] != 0:
            routes[i].insert(0, 0)
        if routes[i][-1] != 0:
            routes[i].append(0)
    # # 检查随机生成的基础解路径
    # print("\n最终生成的路径 (random_solution_generator):")
    # for i, route in enumerate(routes):
    #     print(f"随机生成算法生成的基础解路径 {i+1}为: {route}")
    return routes

def assign_drone_tasks(routes: List[List[int]], problem: Problem) -> Dict[int, List[int]]:
    """
    将部分卡车客户转换为无人机服务客户
    返回: 无人机任务字典 {发射点: [目标点列表]}
    """
    # 如果没有无人机参数，返回空字典
    # 实际获得了无人机参数
    # {'num_drones': 2, 'mass': 4.0, 'max_load': 5.0, 
    # 'speed': 50.0, 'battery': 200.0, 'energy_consumption_rate': 50.0, 
    # 'cost_per_energy': 5.0, 'cost_fixed': 3.0, 'service_time': 5.0}
    # print(problem.drone_params)
    if not problem.drone_params:
        return {}
    
    # 创建路径的深拷贝用于修改
    routes_copy = copy.deepcopy(routes)
    
    # 收集所有客户节点
    all_customers = set()
    for route in routes_copy:
        for node in route:
            if node != 0:
                all_customers.add(node)
    
    # 初始化无人机任务字典和已转换客户集合
    drone_tasks = {}
    converted = set()
    launch_points = set()  # 跟踪发射点
    
    # 为每条路径尝试分配无人机任务
    for route_idx, route in enumerate(routes_copy):
        # 遍历路径中的每个客户节点
        for i in range(1, len(route)-1):
            candidate = route[i]
            
            # 如果客户已被分配给无人机，或已经是发射点，跳过
            if candidate in converted or candidate in launch_points:  
                continue
            
            # 检查客户是否适合无人机服务（载重约束）
            delivery, pickup = problem.demands.get(candidate, (0, 0))
            if max(delivery, pickup) > problem.drone_params.get('max_load', 0):
                continue
            
            # 寻找可能的发射点
            for j in range(1, len(route)-1):
                if i == j or route[j] in converted:
                    continue
                
                launch_point = route[j]
                
                # 计算无人机能耗
                energy = calc_drone_energy(problem, launch_point, candidate)
                # 检查无人机能耗是否小于电池容量
                if energy < problem.drone_params.get('battery', 0):
                    # 如果发射点已在字典中，检查无人机数量约束
                    if launch_point in drone_tasks:
                        # print(drone_tasks)
                        if len(drone_tasks[launch_point]) >= problem.drone_params.get('max_drones', 1):
                            continue
                    else:
                        drone_tasks[launch_point] = []
                    
                    # 添加无人机任务
                    drone_tasks[launch_point].append(candidate)
                    converted.add(candidate)
                    launch_points.add(launch_point)

                    break
    
    # 如果有转换的客户，从路径中移除
    if converted:
        new_routes = []
        for route in routes:
            new_route = [node for node in route if node == 0 or node not in converted]
            # 确保路径至少包含起点和终点仓库
            if len(new_route) < 2:
                new_route = [0, 0]
            new_routes.append(new_route)
        
        # 更新原始路径列表
        routes.clear()
        routes.extend(new_routes)

    # # 检查最后返回得到的无人机任务字典
    # print("\n无人机任务字典:")
    # print(drone_tasks)

    # # 检查每条更新后的路径
    # print("\n更新后的路径(移除无人机服务的客户):")
    # for i, route in enumerate(routes):
    #     print(f"  路径 {i+1}: {route}")

    return drone_tasks


def assign_drone_tasks_extended(routes: List[List[int]], problem: Problem) -> Dict[int, Dict]:
    """
    为扩展编码模式分配无人机任务，支持异步发射-回收模式
    返回: 扩展格式的无人机任务字典 {发射点: {'drone_tasks': [DroneTask对象], 'recovery_point': int}}
    """
    from data_loader import DroneTask

    # 如果没有无人机参数，返回空字典
    if not problem.drone_params:
        return {}

    # 创建路径的深拷贝用于修改
    routes_copy = copy.deepcopy(routes)

    # 收集所有客户节点
    all_customers = set()
    for route in routes_copy:
        for node in route:
            if node != 0:
                all_customers.add(node)

    # 初始化扩展格式的无人机任务字典
    drone_tasks_extended = {}
    converted = set()
    launch_points = set()

    # 为每条路径尝试分配无人机任务
    for route_idx, route in enumerate(routes_copy):
        # 遍历路径中的每个客户节点作为潜在发射点
        for launch_idx in range(1, len(route)-1):
            launch_point = route[launch_idx]

            # 如果已经是发射点或被转换，跳过
            if launch_point in launch_points or launch_point in converted:
                continue

            # 寻找可以服务的客户点
            potential_customers = []
            for customer_idx in range(1, len(route)-1):
                if customer_idx == launch_idx:
                    continue

                candidate = route[customer_idx]
                if candidate in converted:
                    continue

                # 检查客户是否适合无人机服务（载重约束）
                delivery, pickup = problem.demands.get(candidate, (0, 0))
                if max(delivery, pickup) > problem.drone_params.get('max_load', 0):
                    continue

                # 计算无人机能耗
                energy = calc_drone_energy(problem, launch_point, candidate)
                if energy < problem.drone_params.get('battery', 0):
                    potential_customers.append((candidate, energy))

            # 如果有可服务的客户，创建无人机任务
            if potential_customers:
                # 按能耗排序，选择能耗最低的客户
                potential_customers.sort(key=lambda x: x[1])

                # 限制每个发射点的无人机数量
                max_drones = min(problem.drone_params.get('max_drones', 1), len(potential_customers))
                selected_customers = potential_customers[:max_drones]

                # 选择回收点（简化版：选择发射点后面的第一个节点作为回收点）
                recovery_point = launch_point  # 默认同步模式

                # 30%概率使用异步模式
                if random.random() < 0.3 and launch_idx < len(route) - 2:
                    recovery_point = route[launch_idx + 1]

                # 创建DroneTask对象
                drone_tasks = []
                for i, (customer, energy) in enumerate(selected_customers):
                    drone_task = DroneTask(
                        drone_id=i + 1,
                        customer_sequence=[customer],  # 简化版：每个无人机只服务一个客户
                        launch_point=launch_point,
                        recovery_point=recovery_point,
                        total_energy=energy
                    )
                    drone_tasks.append(drone_task)
                    converted.add(customer)

                # 添加到扩展任务字典
                drone_tasks_extended[launch_point] = {
                    'drone_tasks': drone_tasks,
                    'recovery_point': recovery_point
                }
                launch_points.add(launch_point)

                # 限制总的发射点数量，避免过多无人机任务
                if len(drone_tasks_extended) >= len(routes_copy) * 2:
                    break

    return drone_tasks_extended