import math
from typing import List, Dict, Tuple, Any
from data_loader import Problem, Individual
from functools import lru_cache

@lru_cache(maxsize=10000)
def euclidean_distance(a: <PERSON><PERSON>[float, float], b: <PERSON><PERSON>[float, float]) -> float:
    """计算欧氏距离"""
    return math.sqrt((a[0]-b[0])**2 + (a[1]-b[1])**2)

def calculate_truck_load(problem: Problem, route: List[int], drone_tasks: Dict[int, List[int]]) -> List[float]:
    """
    计算卡车在路径上的动态载重
    
    参数:
    - problem: 问题实例
    - route: 卡车路径
    - drone_tasks: 无人机任务字典 {发射点: [目标点列表]}
    
    返回:
    - 路径上每个节点后卡车的载重列表
    """
    # 收集所有客户点（卡车直接服务和无人机服务的）
    all_customers = set()
    # 添加卡车直接服务的客户
    for node in route:
        if node != 0:  # 排除仓库
            all_customers.add(node)
    
    # 添加无人机服务的客户
    drone_customers = set()
    for launch_node, targets in drone_tasks.items():
        drone_customers.update(targets)
    all_customers.update(drone_customers)
    
    # 计算初始载重 = 所有客户的送货需求 + 无人机自重
    initial_load = 0
    for customer in all_customers:
        delivery, _ = problem.demands.get(customer, (0, 0))
        initial_load += delivery
    
    # 添加无人机自重
    max_drones = problem.drone_params.get('max_drones', 0)  # 卡车上固定搭载的无人机数量
    drone_mass = problem.drone_params.get('mass', 0)  # 每架无人机的质量
    initial_load += max_drones * drone_mass  # 添加固定无人机自重
    
    # 跟踪卡车在各节点的载重
    loads = []
    current_load = initial_load
    
    # 仓库起点载重
    loads.append(current_load)
    
    # 计算路径上每个节点后的载重
    for i in range(1, len(route)):
        node = route[i]
        
        # 获取当前节点的需求
        delivery, pickup = problem.demands.get(node, (0, 0))
        
        # 减去当前节点的送货需求，加上取货需求
        current_load = current_load - delivery + pickup
        
        # 处理无人机任务
        if node in drone_tasks:
            # 无人机发射节点与回收节点设定为必须为同一个。
            for target in drone_tasks[node]:
                target_delivery, target_pickup = problem.demands.get(target, (0, 0))
                # 减去无人机配送的货物重量
                current_load -= target_delivery
                # 加上无人机收集的货物重量
                current_load += target_pickup
        
        loads.append(current_load)
    
    return loads

def calc_truck_cost(problem: Problem, route: List[int]) -> float:
    """
    计算单条卡车路径成本
    
    参数:
    - problem: 问题实例
    - route: 卡车路径
    
    返回:
    - 路径成本
    """
    distance = 0.0
    for i in range(len(route)-1):
        from_node, to_node = route[i], route[i+1]
        distance += euclidean_distance(problem.locations[from_node], problem.locations[to_node])
    route_cost = distance * problem.truck_params['cost_per_km']
    return route_cost

def calc_total_truck_cost(problem: Problem, routes: List[List[int]]) -> float:
    """
    计算卡车总运输成本
    
    参数:
    - problem: 问题实例
    - routes: 所有卡车路径列表
    
    返回:
    - 总卡车运输成本
    """
    return sum(calc_truck_cost(problem, route) for route in routes)

@lru_cache(maxsize=1000)
def calc_drone_energy(problem: Problem, launch_node: int, target_node: int) -> float:
    """
    计算无人机单次航程能耗
    
    参数:
    - problem: 问题实例
    - launch_node: 发射节点
    - target_node: 目标节点
    
    返回:
    - 无人机能耗
    """
    # 计算飞行距离
    dist = euclidean_distance(problem.locations[launch_node], problem.locations[target_node])
    
    # 获取无人机参数
    drone_mass = problem.drone_params['mass']
    alpha = problem.drone_params['energy_consumption_rate']
    speed = problem.drone_params['speed']
    
    # 获取目标节点的需求
    delivery, pickup = problem.demands.get(target_node, (0, 0))
    
    # 计算飞行能耗 (去程 + 返程)
    # 去程: 无人机自重 + 配送货物
    # 返程: 无人机自重 + 取回货物
    flight_energy_delivery = alpha * (drone_mass + delivery) * dist / speed
    flight_energy_return = alpha * (drone_mass + pickup) * dist / speed
    
    # 计算服务能耗 (假设服务时间为1单位)
    service_time = problem.drone_params.get('service_time', 3)
    service_energy = alpha * drone_mass * service_time / 60
    
    return flight_energy_delivery + flight_energy_return + service_energy

def calc_drone_cost(problem: Problem, launch_node: int, target_node: int) -> float:
    """
    计算无人机单次任务成本
    
    参数:
    - problem: 问题实例
    - launch_node: 发射节点
    - target_node: 目标节点
    
    返回:
    - 无人机任务成本
    """
    energy = calc_drone_energy(problem, launch_node, target_node)  # 单位: Wh
    # 将Wh转换为kWh后再乘以单位成本
    return (energy / 1000) * problem.drone_params['cost_per_energy']

def calc_total_drone_cost(problem: Problem, drone_tasks: Dict[int, List[int]]) -> float:
    """
    计算所有无人机任务的总成本
    
    参数:
    - problem: 问题实例
    - drone_tasks: 无人机任务字典 {发射点: [目标点列表]}
    
    返回:
    - 所有无人机任务的总成本
    """
    total_cost = 0.0
    for launch_node, targets in drone_tasks.items():
        for target_node in targets:
            total_cost += calc_drone_cost(problem, launch_node, target_node)
    return total_cost

@lru_cache(maxsize=1000)
def calc_drone_flight_time(problem: Problem, launch_node: int, target_node: int) -> float:
    """
    计算无人机单次任务飞行时间
    
    参数:
    - problem: 问题实例
    - launch_node: 发射节点
    - target_node: 目标节点
    
    返回:
    - 无人机往返飞行时间
    """
    dist = euclidean_distance(problem.locations[launch_node], problem.locations[target_node])
    speed = problem.drone_params['speed']
    
    # 去程 + 返程 (小时) + 服务时间(分钟转小时)
    service_time = problem.service_time / 60  # 分钟转小时
    return 2 * dist / speed + service_time

def calc_waiting_cost(problem: Problem, route: List[int], drone_tasks: Dict[int, List[int]]) -> float:
    waiting_cost = 0.0
    
    for node in route:
        if node in drone_tasks:
            max_flight_time = 0
            for target in drone_tasks[node]:
                flight_time = calc_drone_flight_time(problem, node, target)
                max_flight_time = max(max_flight_time, flight_time)
            
            # 卡车服务时间(小时)
            truck_service_time = problem.service_time / 60
            
            # 实际等待时间 = 无人机飞行时间 - 卡车服务时间 (如果是正值)
            actual_waiting_time = max(0, max_flight_time - truck_service_time)
            
            # 等待成本 (确保单位是元/小时)
            waiting_cost_per_hour = problem.wait_time_per_cost * 60  # 元/分钟 -> 元/小时
            waiting_cost += actual_waiting_time * waiting_cost_per_hour
    
    return waiting_cost

def calc_total_waiting_cost(problem: Problem, routes: List[List[int]], drone_tasks: Dict[int, List[int]]) -> float:
    """
    计算所有卡车的等待成本
    
    参数:
    - problem: 问题实例
    - routes: 所有卡车路径
    - drone_tasks: 无人机任务字典
    
    返回:
    - 总等待成本
    """
    return sum(calc_waiting_cost(problem, route, drone_tasks) for route in routes)

def calculate_total_cost(problem: Problem, routes: List[List[int]], drone_tasks: Dict[int, List[int]], return_breakdown=False):
    """
    计算解决方案的总成本
    
    参数:
    - problem: 问题实例
    - routes: 所有卡车路径
    - drone_tasks: 无人机任务字典
    - return_breakdown: 是否返回成本分解详情
    
    返回:
    - 如果return_breakdown=False: 返回总成本
    - 如果return_breakdown=True: 返回包含成本分解的字典
    """
    # 卡车运输成本
    truck_cost = calc_total_truck_cost(problem, routes)
    
    # 无人机能耗成本
    drone_cost = calc_total_drone_cost(problem, drone_tasks)
    
    # 卡车等待成本
    waiting_cost = calc_total_waiting_cost(problem, routes, drone_tasks)
    
    # 系统运行时间成本
    system_cost = calc_system_operation_cost(problem, routes, drone_tasks)
    
    # 卡车固定成本 = 使用的卡车数量 × 每辆卡车的固定成本
    truck_fixed_cost = problem.num_vehicles * problem.vehicle_fixed_cost
    
    # 无人机固定成本 = 使用的无人机数量 × 每架无人机的固定成本
    drone_fixed_cost = problem.num_drones * problem.drone_fixed_cost
    
    # 总成本 = 所有成本项之和
    total_cost = truck_cost + drone_cost + waiting_cost + system_cost + truck_fixed_cost + drone_fixed_cost
    
    if return_breakdown:
        # 计算系统运营时间（小时）
        system_operation_time = calculate_system_operation_time(problem, routes, drone_tasks)
        
        return {
            'total_cost': total_cost,
            'vehicle_cost': truck_cost,
            'drone_cost': drone_cost,
            'waiting_cost': waiting_cost,
            'time_cost': system_cost,
            'truck_fixed_cost': truck_fixed_cost,
            'drone_fixed_cost': drone_fixed_cost,
            'cost_without_time': truck_cost + drone_cost + waiting_cost + truck_fixed_cost + drone_fixed_cost,
            'operation_time_hours': system_operation_time
        }
    else:
        return total_cost


def calculate_truck_overload_penalty(problem: Problem, route: List[int], drone_tasks: Dict[int, List[int]], current_gen: int, max_gen: int) -> float:
    """
    计算卡车载重超限惩罚
    
    参数:
    - problem: 问题实例
    - route: 卡车路径
    - drone_tasks: 无人机任务字典
    - current_gen: 当前代数
    - max_gen: 最大代数
    
    返回:
    - 载重超限惩罚值
    """
    # 基础惩罚系数
    namuda1 = 100  # 元/kg
    k = 2.5
    
    # 计算动态惩罚系数
    namuda_t = namuda1 * math.exp(k * (current_gen / max_gen))
    
    # 计算各节点载重
    loads = calculate_truck_load(problem, route, drone_tasks)
    max_load = problem.truck_params['max_load']
    
    # 累计超重惩罚
    total_overload_penalty = 0.0
    for load in loads:
        overload = max(0, load - max_load)
        total_overload_penalty += overload
    
    return namuda_t * total_overload_penalty

def calculate_drone_energy_penalty(problem: Problem, drone_tasks: Dict[int, List[int]], current_gen: int, max_gen: int) -> float:
    """
    计算无人机能耗超限惩罚
    
    参数:
    - problem: 问题实例
    - drone_tasks: 无人机任务字典
    - current_gen: 当前代数
    - max_gen: 最大代数
    
    返回:
    - 能耗超限惩罚值
    """
    # 基础惩罚系数和参数
    namuda2 = 5.0  # 元/Wh
    gamma = 3.0
    alpha = 1.5
    
    # 计算动态惩罚系数
    namuda_d = namuda2 * (1 + gamma * (current_gen / max_gen))
    
    # 累计所有无人机任务的能耗超限惩罚
    total_energy_penalty = 0.0
    battery_capacity = problem.drone_params['battery']
    
    for launch_node, targets in drone_tasks.items():
        for target in targets:
            energy = calc_drone_energy(problem, launch_node, target)
            energy_exceed = max(0, energy - battery_capacity)
            
            # 应用非线性惩罚
            if energy_exceed > 0:
                total_energy_penalty += namuda_d * (energy_exceed ** alpha)
    
    return total_energy_penalty

def calculate_total_penalty(problem: Problem, individual: Individual, current_gen: int, max_gen: int) -> Tuple[float, float]:
    """
    计算个体的总惩罚值
    
    参数:
    - problem: 问题实例
    - individual: 解决方案个体
    - current_gen: 当前代数
    - max_gen: 最大代数
    
    返回:
    - 卡车载重惩罚和无人机能耗惩罚的元组
    """
    # 计算卡车载重超限惩罚
    truck_penalty = 0.0
    for route in individual.routes:
        truck_penalty += calculate_truck_overload_penalty(problem, route, individual.drone_tasks, current_gen, max_gen)
    
    # 计算无人机能耗超限惩罚
    drone_penalty = calculate_drone_energy_penalty(problem, individual.drone_tasks, current_gen, max_gen)
    
    return truck_penalty, drone_penalty

def evaluate_individual_with_penalty(problem: Problem, individual: Individual, current_gen: int, max_gen: int) -> float:
    """
    评估个体的适应度（包含惩罚的倒数）
    
    参数:
    - problem: 问题实例
    - individual: 解决方案个体
    - current_gen: 当前代数
    - max_gen: 最大代数
    
    返回:
    - 适应度值（总成本和惩罚的倒数）
    """
    # 计算总成本
    total_cost = calculate_total_cost(problem, individual.routes, individual.drone_tasks)

    # 保存总成本到个体
    individual.total_cost = total_cost
    
    # 计算惩罚值
    truck_penalty, drone_penalty = calculate_total_penalty(problem, individual, current_gen, max_gen)
    
    # 总成本加上惩罚值
    penalized_cost = total_cost + truck_penalty + drone_penalty
    
    # 保存惩罚值，方便后续分析
    individual.penalty = truck_penalty + drone_penalty
    
    # 对于异常大的成本，避免适应度接近0
    if penalized_cost > 1e10:
        return 1e-10
    
    # 返回适应度值（成本的倒数，成本越小适应度越高）
    fitness = 1.0 / penalized_cost
    individual.fitness = fitness
    
    return fitness

def update_population_fitness(problem: Problem, population: List[Individual], current_gen: int, max_gen: int) -> None:
    """
    更新种群中所有个体的适应度
    
    参数:
    - problem: 问题实例
    - population: 个体种群
    - current_gen: 当前代数
    - max_gen: 最大代数
    """
    for individual in population:
        evaluate_individual_with_penalty(problem, individual, current_gen, max_gen)


# def check_truck_capacity(problem: Problem, route: List[int]) -> bool:
#     """
#     检查卡车路径是否满足载重约束
    
#     参数:
#     - problem: 问题实例
#     - route: 卡车路径
    
#     返回:
#     - 是否满足载重约束
#     """
#     loads = calculate_truck_load(problem, route)
#     return all(load <= problem.truck_params['max_load'] for load in loads)

# def check_drone_constraints(problem: Problem, launch_node: int, target_node: int) -> bool:
#     """
#     检查无人机任务是否满足约束
    
#     参数:
#     - problem: 问题实例
#     - launch_node: 发射节点
#     - target_node: 目标节点
    
#     返回:
#     - 是否满足约束
#     """
#     # 检查载重约束
#     delivery, pickup = problem.demands.get(target_node, (0, 0))
#     if max(delivery, pickup) > problem.drone_params['max_load']:
#         return False
    
#     # 检查飞行距离约束
#     dist = euclidean_distance(problem.locations[launch_node], problem.locations[target_node])
#     if 2 * dist > problem.drone_params['max_distance']:
#         return False
    
#     # 检查能耗约束
#     energy = calc_drone_energy(problem, launch_node, target_node)
#     if energy > problem.drone_params['max_energy']:
#         return False
    
#     return True

# def is_solution_feasible(problem: Problem, individual: Individual) -> bool:
#     """
#     检查解决方案是否可行
    
#     参数:
#     - problem: 问题实例
#     - individual: 解决方案个体
    
#     返回:
#     - 是否可行
#     """
#     routes = individual.routes
#     drone_tasks = individual.drone_tasks
    
#     # 检查路径数量
#     if len(routes) > problem.truck_params['num_vehicles']:
#         return False
    
#     # 检查每条卡车路径
#     for route in routes:
#         # 检查路径是否以仓库开始和结束
#         if len(route) < 2 or route[0] != 0 or route[-1] != 0:
#             return False
        
#         # 检查载重约束
#         if not check_truck_capacity(problem, route):
#             return False
    
#     # 检查无人机任务
#     for launch_node, targets in drone_tasks.items():
#         # 检查发射点是否在卡车路径上
#         if not any(launch_node in route for route in routes):
#             return False
        
#         # 检查每个发射点的无人机数量
#         if len(targets) > problem.drone_params.get('max_drones', 1):
#             return False
        
#         # 检查每个无人机任务
#         for target in targets:
#             if not check_drone_constraints(problem, launch_node, target):
#                 return False
    
#     # 检查所有客户是否都被服务
#     served_customers = set()
#     # 添加卡车服务的客户
#     for route in routes:
#         for node in route:
#             if node != 0:  # 排除仓库
#                 served_customers.add(node)
    
#     # 添加无人机服务的客户
#     for targets in drone_tasks.values():
#         served_customers.update(targets)
    
#     # 检查是否所有客户都被服务
#     all_customers = set(problem.demands.keys())
#     all_customers.discard(0)  # 排除仓库
    
#     return served_customers == all_customers

# def evaluate_individual(problem: Problem, individual: Individual) -> float:
#     """
#     评估个体的适应度（总成本）
    
#     参数:
#     - problem: 问题实例
#     - individual: 解决方案个体
    
#     返回:
#     - 总成本（如果解决方案不可行，则返回很大的惩罚值）
#     """
#     if not is_solution_feasible(problem, individual):
#         return float('inf')  # 不可行解返回无穷大成本
    
#     return calculate_total_cost(problem, individual.routes, individual.drone_tasks)

def calc_system_operation_cost(problem: Problem, routes: List[List[int]], drone_tasks: Dict[int, List[int]]) -> float:
    """
    计算系统运行时间成本
    
    参数:
    - problem: 问题实例
    - routes: 所有卡车路径
    - drone_tasks: 无人机任务字典
    
    返回:
    - 系统运行时间成本
    """
    # 计算每条路径的完成时间
    route_completion_times = []
    
    for route in routes:
        # 计算卡车行驶时间
        truck_travel_time = 0.0
        for i in range(len(route)-1):
            from_node, to_node = route[i], route[i+1]
            distance = euclidean_distance(problem.locations[from_node], problem.locations[to_node])
            truck_travel_time += distance / problem.truck_params['speed']
        
        # 计算卡车服务时间
        truck_service_time = (len(route) - 2) * problem.service_time / 60  # 减去起点和终点，转换为小时
        
        # 计算无人机任务导致的额外等待时间
        drone_waiting_time = 0.0
        for node in route:
            if node in drone_tasks:
                max_flight_time = 0
                for target in drone_tasks[node]:
                    flight_time = calc_drone_flight_time(problem, node, target)
                    max_flight_time = max(max_flight_time, flight_time)
                
                # 实际等待时间 = 无人机飞行时间 - 卡车服务时间 (如果是正值)
                truck_service_time_at_node = problem.service_time / 60
                actual_waiting_time = max(0, max_flight_time - truck_service_time_at_node)
                drone_waiting_time += actual_waiting_time
        
        # 路径总完成时间 = 行驶时间 + 服务时间 + 等待时间
        total_time = truck_travel_time + truck_service_time + drone_waiting_time
        route_completion_times.append(total_time)
    
    # 系统运行时间 = 最长路径完成时间
    system_operation_time = max(route_completion_times) if route_completion_times else 0
    
    # 系统运行成本 = 运行时间 * 单位时间成本
    operation_cost_per_hour = problem.system_operation_cost_per_hour if hasattr(problem, 'system_operation_cost_per_hour') else 0
    return system_operation_time * operation_cost_per_hour


def calculate_system_operation_time(problem, routes, drone_tasks):
    """
    仅计算系统运行时间（小时）
    
    参数:
    - problem: 问题实例
    - routes: 所有卡车路径
    - drone_tasks: 无人机任务字典
    
    返回:
    - 系统运行时间（小时）
    """
    from utils import euclidean_distance, calc_drone_flight_time
    
    # 计算每条路径的完成时间
    route_completion_times = []
    
    for route in routes:
        # 计算卡车行驶时间
        truck_travel_time = 0.0
        for i in range(len(route)-1):
            from_node, to_node = route[i], route[i+1]
            distance = euclidean_distance(problem.locations[from_node], problem.locations[to_node])
            truck_travel_time += distance / problem.truck_params['speed']
        
        # 计算卡车服务时间
        truck_service_time = (len(route) - 2) * problem.service_time / 60  # 减去起点和终点，转换为小时
        
        # 计算无人机任务导致的额外等待时间
        drone_waiting_time = 0.0
        for node in route:
            if node in drone_tasks:
                max_flight_time = 0
                for target in drone_tasks[node]:
                    flight_time = calc_drone_flight_time(problem, node, target)
                    max_flight_time = max(max_flight_time, flight_time)
                
                # 实际等待时间 = 无人机飞行时间 - 卡车服务时间 (如果是正值)
                truck_service_time_at_node = problem.service_time / 60
                actual_waiting_time = max(0, max_flight_time - truck_service_time_at_node)
                drone_waiting_time += actual_waiting_time
        
        # 路径总完成时间 = 行驶时间 + 服务时间 + 等待时间
        total_time = truck_travel_time + truck_service_time + drone_waiting_time
        route_completion_times.append(total_time)
    
    # 系统运行时间 = 最长路径完成时间
    return max(route_completion_times) if route_completion_times else 0
