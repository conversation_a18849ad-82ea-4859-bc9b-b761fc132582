if progress_ratio <= 0.3:
ls_frequency = 30  # 前期
elif progress_ratio <= 0.5:
ls_frequency = 25  # 中期
else:
ls_frequency = 20  # 后期


# 基础惩罚系数和参数
namuda2 = 5.0  # 元/Wh
gamma = 3.0
alpha = 1.5


# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)


========== C101-81-1 数据集求解结果 ==========

算法配置:
数据集: C_聚类_数据集/C101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.1
初始随机种子: 40
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1033.84
最小总成本: 950.60 (运行 5)
最大总成本: 1160.11 (运行 3)
总成本标准差: 66.84

========== 算法精度与稳定性分析 ==========
最大偏差: 209.50 (22.04%)
平均偏差: 83.23 (8.76%)
平均求解时间: 303.27秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |       41 | 0.000987 | 1012.78 |    0.00 | 303.41
     2 |       42 | 0.000955 | 1047.29 |    0.00 | 302.10
     3 |       43 | 0.000862 | 1160.11 |    0.00 | 307.89
     4 |       44 | 0.001049 |  953.51 |    0.00 | 302.31
     5 |       45 | 0.001052 |  950.60 |    0.00 | 302.63
     6 |       46 | 0.001029 |  971.44 |    0.00 | 304.42
     7 |       47 | 0.000985 | 1014.80 |    0.00 | 302.10
     8 |       48 | 0.000887 | 1126.83 |    0.00 | 302.34
     9 |       49 | 0.000975 | 1025.37 |    0.00 | 303.18
    10 |       50 | 0.000930 | 1075.66 |    0.00 | 302.32

最佳解详细信息:
运行ID: 5
适应度: 0.001052
总成本: 950.60
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 41, 39, 37, 40, 34, 33, 32, 44, 46, 38, 43, 47, 35, 45, 42, 54, 48, 51, 55, 53, 36, 0, 16, 18, 20, 52, 50, 49, 59, 64, 62, 63, 61, 56, 58, 57, 65, 66, 69, 68, 67, 70, 71, 72, 0, 60, 1, 3, 4, 2, 79, 80, 77, 75, 73, 74, 76, 78, 0, 6, 7, 8, 9, 5, 10, 12, 13, 14, 15, 11, 29, 31, 30, 26, 27, 28, 25, 23, 21, 24, 22, 19, 17, 0)
染色体 2: (0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 41, 39, 34, 33, 32, 44, 43, 45, 42, 54, 55, 0]
  无人机任务:
    从节点 39 发射无人机访问: [37, 40]
    从节点 44 发射无人机访问: [46, 38]
    从节点 43 发射无人机访问: [47, 35]
    从节点 54 发射无人机访问: [48, 51]
    从节点 55 发射无人机访问: [53, 36]
路线 2: [0, 16, 18, 20, 52, 50, 49, 59, 64, 63, 61, 56, 65, 66, 69, 70, 71, 72, 0]
  无人机任务:
    从节点 64 发射无人机访问: [62]
    从节点 56 发射无人机访问: [58, 57]
    从节点 69 发射无人机访问: [68, 67]
路线 3: [0, 60, 1, 2, 79, 77, 75, 76, 78, 0]
  无人机任务:
    从节点 1 发射无人机访问: [3, 4]
    从节点 79 发射无人机访问: [80]
    从节点 75 发射无人机访问: [73, 74]
路线 4: [0, 6, 7, 8, 10, 12, 14, 29, 26, 25, 23, 21, 19, 17, 0]
  无人机任务:
    从节点 8 发射无人机访问: [9, 5]
    从节点 12 发射无人机访问: [13]
    从节点 14 发射无人机访问: [15, 11]
    从节点 29 发射无人机访问: [31, 30]
    从节点 26 发射无人机访问: [27, 28]
    从节点 21 发射无人机访问: [24, 22]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000532 | 1880.28 |    0.00
  10 | 0.000532 | 1880.28 |    0.00
  20 | 0.000532 | 1880.28 |    0.00
  30 | 0.000588 | 1701.85 |    0.00
  40 | 0.000588 | 1701.85 |    0.00
  50 | 0.000601 | 1664.87 |    0.00
  60 | 0.000601 | 1664.87 |    0.00
  70 | 0.000601 | 1664.87 |    0.00
  80 | 0.000603 | 1658.07 |    0.00
  90 | 0.000607 | 1646.62 |    0.00
 100 | 0.000631 | 1585.99 |    0.00
 110 | 0.000638 | 1566.41 |    0.00
 120 | 0.000686 | 1457.22 |    0.00
 130 | 0.000687 | 1456.55 |    0.00
 140 | 0.000701 | 1425.71 |    0.00
 150 | 0.000707 | 1414.90 |    0.00
 160 | 0.000707 | 1414.90 |    0.00
 170 | 0.000707 | 1414.90 |    0.00
 180 | 0.000713 | 1403.30 |    0.00
 190 | 0.000724 | 1382.10 |    0.00
 200 | 0.000724 | 1382.10 |    0.00
 210 | 0.000730 | 1369.96 |    0.00
 220 | 0.000730 | 1369.96 |    0.00
 230 | 0.000730 | 1369.96 |    0.00
 240 | 0.000730 | 1369.96 |    0.00
 250 | 0.000730 | 1369.96 |    0.00
 260 | 0.000730 | 1369.96 |    0.00
 270 | 0.000730 | 1369.96 |    0.00
 280 | 0.000766 | 1304.84 |    0.00
 290 | 0.000792 | 1263.00 |    0.00
 300 | 0.000846 | 1181.36 |    0.00
 310 | 0.000856 | 1168.84 |    0.00
 320 | 0.000871 | 1147.73 |    0.00
 330 | 0.000873 | 1145.46 |    0.00
 340 | 0.000927 | 1078.49 |    0.00
 350 | 0.000927 | 1078.49 |    0.00
 360 | 0.000955 | 1047.22 |    0.00
 370 | 0.000955 | 1047.22 |    0.00
 380 | 0.001029 |  971.55 |    0.00
 390 | 0.001039 |  962.73 |    0.00
 400 | 0.001040 |  961.96 |    0.00
 410 | 0.001045 |  957.20 |    0.00
 420 | 0.001049 |  952.86 |    0.00
 430 | 0.001052 |  950.60 |    0.00
 440 | 0.001052 |  950.60 |    0.00
 450 | 0.001052 |  950.60 |    0.00
 460 | 0.001052 |  950.60 |    0.00
 470 | 0.001052 |  950.60 |    0.00
 480 | 0.001052 |  950.60 |    0.00
