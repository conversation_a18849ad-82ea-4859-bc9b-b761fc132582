import random
import os
import re

# 设置无人机最大载重参数（kg）
DRONE_MAX_PAYLOAD = 10.0

def convert_solomon_file(input_file, output_file=None):
    """将Solomon数据集转换为新格式"""
    
    if output_file is None:
        # 如果没有指定输出文件名，则使用输入文件名加上"-converted"
        file_name, ext = os.path.splitext(input_file)
        output_file = f"{file_name}-converted{ext}"
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取客户点数据
    customer_data = []
    reading_customers = False
    depot_line = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        if 'CUST NO.' in line:
            reading_customers = True
            continue
        
        if reading_customers:
            parts = re.split(r'\s+', line.strip())
            if len(parts) >= 4:  # 至少需要ID、X坐标、Y坐标和原需求量
                try:
                    customer_id = int(parts[0])
                    x_coord = float(parts[1])
                    y_coord = float(parts[2])
                    
                    # 保存配送中心
                    if customer_id == 0:
                        depot_line = [customer_id, x_coord, y_coord]
                    else:
                        customer_data.append([customer_id, x_coord, y_coord])
                except ValueError:
                    continue
    
    # 确保我们有客户数据
    if not customer_data or not depot_line:
        raise ValueError(f"无法从 {input_file} 提取客户点数据")
    
    # 随机分配送货和取货需求
    num_customers = len(customer_data)
    
    # 确定各类客户数量
    num_both = int(num_customers * 0.2)  # 20%的客户有送货和取货需求
    num_delivery = int(num_customers * 0.5)  # 50%的客户仅有送货需求
    num_pickup = num_customers - num_both - num_delivery  # 剩余客户仅有取货需求
    
    # 随机打乱客户数据以进行分配
    random.shuffle(customer_data)
    
    # 分配需求
    for i, customer in enumerate(customer_data):
        delivery = 0
        pickup = 0
        
        # 确定需求量范围
        if i < int(num_customers * 0.1):  # 10%的客户需求量在(10,20]
            demand_range = (11, 20)  # 大于10且小于等于20的整数
        else:  # 90%的客户需求量在(0,10]
            demand_range = (1, 10)  # 大于0且小于等于10的整数
        
        # 根据客户类型分配需求
        if i < num_both:  # 20%的客户：送货和取货
            delivery = random.randint(*demand_range)
            pickup = random.randint(*demand_range)
        elif i < num_both + num_delivery:  # 50%的客户：仅送货
            delivery = random.randint(*demand_range)
            pickup = 0
        else:  # 30%的客户：仅取货
            delivery = 0
            pickup = random.randint(*demand_range)
        
        # 添加需求量到客户数据
        customer.append(delivery)
        customer.append(pickup)
    
    # 为配送中心添加0需求
    depot_line.append(0)
    depot_line.append(0)
    
    # 获取原始问题的名称
    name = ""
    for line in lines:
        if 'VEHICLE' in line or '//' in line or 'Vrp-Set' in line:
            name = line.strip()
            break
      # 提取文件名不带路径
    basename = os.path.basename(input_file)
    
    # 构建新文件内容
    new_content = ["NAME : " + basename.replace(".vrp", "-converted"),
                  "COMMENT : (Solomon dataset, Modified for VRPD)",
                  "TYPE : VRPD",
                  "DIMENSION : " + str(len(customer_data) + 1),  # +1 for depot
                  "EDGE_WEIGHT_TYPE : EUC_2D",
                  "NUM_CUSTOMERS : " + str(len(customer_data)),
                  "NUM_VEHICLES : 2",
                  "NUM_DRONES_PER_VEHICLE : 2",
                  f"VEHICLE_CAPACITY: 200kg",
                  "VEHICLE_SPEED : 40km/h",
                  "VEHICLE_PER_COST : 5 YUAN/km",
                  "VEHICLE_FIXED_COST : 30 YUAN/辆",
                  f"DRONE_MAX_PAYLOAD : {DRONE_MAX_PAYLOAD}kg",
                  "DRONE_MASS : 4kg",
                  "BATTERY_ENERGY : 200Wh",
                  "DRONE_SPEED : 50km/h",
                  "DRONE_ENERGY_RATE : 50W/kg",
                  "DRONE_PER_COST : 5 YUAN/kWh",
                  "DRONE_FIXED_COST : 3 YUAN/架",
                  "SERVICE_TIME : 5 min",
                  "WAIT_TIME_PER_COST : 1 YUAN/min",
                  "",
                  "NODE_COORD_SECTION"]
      # 准备所有节点数据
    all_nodes = [depot_line] + customer_data
    all_nodes.sort(key=lambda x: x[0])  # 按ID排序
    
    # 添加坐标部分
    for node in all_nodes:
        new_content.append(f" {node[0]} {node[1]:.1f} {node[2]:.1f}")
    
    # 添加需求部分
    new_content.append("")
    new_content.append("DEMAND_SECTION")
    
    for node in all_nodes:
        customer_id, x, y, delivery, pickup = node
        comment = ""
          # 添加注释标识客户类型
        if customer_id == 0:
            comment = "// 配送中心"
        elif delivery > 0 and pickup > 0:
            # 判断取送货是否都是超重点
            if delivery > DRONE_MAX_PAYLOAD and pickup > DRONE_MAX_PAYLOAD:
                comment = "// 取送货，超重点"
            elif delivery > DRONE_MAX_PAYLOAD:
                comment = "// 取送货，送货超重"
            elif pickup > DRONE_MAX_PAYLOAD:
                comment = "// 取送货，取货超重"
            else:
                comment = "// 取送货"
        elif delivery > 0:
            if delivery > DRONE_MAX_PAYLOAD:
                comment = "// 仅送货，超重点"
            else:
                comment = "// 仅送货"
        elif pickup > 0:
            if pickup > DRONE_MAX_PAYLOAD:
                comment = "// 仅取货，超重点"
            else:
                comment = "// 仅取货"
        
        new_content.append(f" {customer_id} {delivery} {pickup}      {comment}")
    
    # 添加depot部分和文件结束标记
    new_content.append("")
    new_content.append("DEPOT_SECTION")
    new_content.append(" 0")
    new_content.append(" 0")
    new_content.append("EOF")
    
    # 写入新文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("\n".join(new_content))
    
    print(f"转换完成: {input_file} -> {output_file}")
    
    return output_file

# 设置随机种子以保证可重复结果
random.seed(42)

# 转换三个数据集
files_to_convert = [
    "1_Solomon_R101.vrp",
    "3_Solomon_C101.vrp", 
    "5_Solomom_RC101.vrp"
]

for file in files_to_convert:
    convert_solomon_file(file, file.replace(".vrp", "-converted.vrp"))
