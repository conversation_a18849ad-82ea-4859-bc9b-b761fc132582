NAME : R101-61-1
COMMENT : (<PERSON> dataset, Modified for VRPD)
TYPE : VRPD
DIMENSION : 61
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 60

NODE_COORD_SECTION
 0 8.75 8.75
 1 10.25 12.25
 2 8.75 4.25
 3 13.75 5.00
 4 3.75 7.50
 5 6.25 7.50
 6 5.00 12.50
 7 7.50 15.00
 8 5.00 16.25
 9 12.50 8.75
 10 3.75 2.50
 11 7.50 1.25
 12 2.50 5.00
 13 11.25 16.25
 14 11.25 2.50
 15 16.25 8.75
 16 16.25 5.00
 17 11.25 7.50
 18 8.75 10.00
 19 10.25 9.25
 20 16.00 10.50
 21 10.00 15.00
 22 16.25 13.75
 23 15.75 16.25
 24 0.50 15.00
 25 5.00 5.00
 26 10.00 6.25
 27 10.50 1.75
 28 5.75 0.75
 29 2.75 3.50
 30 1.50 9.50
 31 0.50 12.00
 32 3.25 13.00
 33 9.25 7.75
 34 14.25 7.25
 35 15.75 5.75
 36 13.25 3.00
 37 4.25 8.50
 38 6.00 14.50
 39 15.50 19.25
 40 14.00 9.75
 41 9.25 14.00
 42 14.25 17.00
 43 11.75 4.00
 44 11.50 3.25
 45 13.25 10.75
 46 14.25 12.00
 47 14.00 9.25
 48 13.75 13.50
 49 3.50 9.25
 50 1.00 4.50
 51 7.00 4.50
 52 6.50 8.75
 53 7.75 16.75
 54 3.75 4.75
 55 4.50 6.00
 56 6.50 6.75
 57 6.25 6.00
 58 4.75 5.25
 59 5.00 6.50
 60 4.50 4.50

DEMAND_SECTION
 0 0 0      
 1 0.0 1.9      // 仅取货需求客户 - light包裹
 2 0.0 3.7      // 仅取货需求客户 - light包裹
 3 2.6 0.0      // 仅送货需求客户 - light包裹
 4 2.7 1.8      // 送货取货双需求客户 - light包裹
 5 2.3 0.0      // 仅送货需求客户 - light包裹
 6 2.6 0.0      // 仅送货需求客户 - light包裹
 7 2.2 1.5      // 送货取货双需求客户 - light包裹
 8 11.8 0.0      // 仅送货需求客户 - heavy包裹
 9 7.4 0.0      // 仅送货需求客户 - medium包裹
 10 0.0 2.4      // 仅取货需求客户 - light包裹
 11 1.4 1.2      // 送货取货双需求客户 - light包裹
 12 1.3 1.3      // 送货取货双需求客户 - light包裹
 13 1.9 1.7      // 送货取货双需求客户 - light包裹
 14 0.0 3.5      // 仅取货需求客户 - light包裹
 15 0.0 1.8      // 仅取货需求客户 - light包裹
 16 2.4 0.0      // 仅送货需求客户 - light包裹
 17 16.5 3.3      // 送货取货双需求客户 - heavy包裹
 18 2.0 1.2      // 送货取货双需求客户 - light包裹
 19 4.0 0.0      // 仅送货需求客户 - light包裹
 20 4.8 0.0      // 仅送货需求客户 - light包裹
 21 2.9 2.1      // 送货取货双需求客户 - light包裹
 22 3.7 0.0      // 仅送货需求客户 - light包裹
 23 8.3 8.2      // 送货取货双需求客户 - medium包裹
 24 4.9 0.0      // 仅送货需求客户 - light包裹
 25 0.0 2.8      // 仅取货需求客户 - light包裹
 26 3.5 0.0      // 仅送货需求客户 - light包裹
 27 3.4 0.0      // 仅送货需求客户 - light包裹
 28 1.3 0.0      // 仅送货需求客户 - light包裹
 29 1.2 0.0      // 仅送货需求客户 - light包裹
 30 2.1 0.0      // 仅送货需求客户 - light包裹
 31 0.0 4.9      // 仅取货需求客户 - light包裹
 32 3.4 0.0      // 仅送货需求客户 - light包裹
 33 3.3 0.0      // 仅送货需求客户 - light包裹
 34 17.4 0.0      // 仅送货需求客户 - heavy包裹
 35 4.6 3.1      // 送货取货双需求客户 - light包裹
 36 3.4 0.0      // 仅送货需求客户 - light包裹
 37 4.8 2.7      // 送货取货双需求客户 - light包裹
 38 9.2 0.0      // 仅送货需求客户 - medium包裹
 39 10.9 0.0      // 仅送货需求客户 - heavy包裹
 40 1.2 0.0      // 仅送货需求客户 - light包裹
 41 0.0 5.0      // 仅取货需求客户 - light包裹
 42 3.4 0.0      // 仅送货需求客户 - light包裹
 43 4.7 0.0      // 仅送货需求客户 - light包裹
 44 4.1 0.0      // 仅送货需求客户 - light包裹
 45 1.2 0.0      // 仅送货需求客户 - light包裹
 46 2.3 0.0      // 仅送货需求客户 - light包裹
 47 0.0 3.2      // 仅取货需求客户 - light包裹
 48 2.0 0.0      // 仅送货需求客户 - light包裹
 49 0.0 9.8      // 仅取货需求客户 - medium包裹
 50 1.6 0.0      // 仅送货需求客户 - light包裹
 51 0.0 3.7      // 仅取货需求客户 - light包裹
 52 6.6 4.7      // 送货取货双需求客户 - medium包裹
 53 1.5 1.4      // 送货取货双需求客户 - light包裹
 54 3.2 2.4      // 送货取货双需求客户 - light包裹
 55 1.5 1.4      // 送货取货双需求客户 - light包裹
 56 10.0 0.0      // 仅送货需求客户 - medium包裹
 57 5.6 0.0      // 仅送货需求客户 - medium包裹
 58 0.0 3.0      // 仅取货需求客户 - light包裹
 59 2.2 0.0      // 仅送货需求客户 - light包裹
 60 13.5 0.0      // 仅送货需求客户 - heavy包裹

DEPOT_SECTION
 0
 -1
EOF