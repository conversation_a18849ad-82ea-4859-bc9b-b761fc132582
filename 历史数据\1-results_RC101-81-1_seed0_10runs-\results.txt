# 自适应变异因子 - 随着迭代进行而减小
self.mutation_factor = self.mutation_factor * (1 - 0.8 * elapsed_time / self.max_runtime)

# 计算各方法生成的数量
savings_count = int(pop_size * 0.7)  # 50%使用改进的节约算法
random_count = pop_size - savings_count  # 50%使用随机生成

if progress_ratio <= 0.2:
    ls_frequency = 30  # 前期
elif progress_ratio <= 0.6:
    ls_frequency = 25  # 中期
else:
    ls_frequency = 20  # 后期

========== RC101-81-1 数据集求解结果 ==========

算法配置:
数据集: RC_随机+聚类_数据集/RC101-81-1.vrp
种群大小: 100
最大迭代次数: 500
最大运行时间: 300秒
变异因子: 0.8
交叉率: 0.8
精英比例: 0.04
初始随机种子: 0
运行次数: 10

========== 多次运行结果统计 ==========
运行次数: 10
平均总成本: 1231.26
最小总成本: 1079.46 (运行 9)
最大总成本: 1385.53 (运行 3)
总成本标准差: 79.94

========== 算法精度与稳定性分析 ==========
最大偏差: 306.07 (28.35%)
平均偏差: 151.80 (14.06%)
平均求解时间: 304.38秒

所有运行结果:
运行ID | 随机种子 | 适应度 | 总成本 | 惩罚值 | 求解时间(秒)
----------------------------------------------------------------------
     1 |        1 | 0.000814 | 1229.00 |    0.00 | 304.29
     2 |        2 | 0.000746 | 1340.45 |    0.00 | 309.78
     3 |        3 | 0.000722 | 1385.53 |    0.00 | 303.18
     4 |        4 | 0.000815 | 1226.85 |    0.00 | 303.14
     5 |        5 | 0.000846 | 1181.60 |    0.00 | 305.73
     6 |        6 | 0.000845 | 1183.57 |    0.00 | 303.00
     7 |        7 | 0.000818 | 1222.55 |    0.00 | 303.04
     8 |        8 | 0.000820 | 1219.75 |    0.00 | 302.78
     9 |        9 | 0.000926 | 1079.46 |    0.00 | 302.99
    10 |       10 | 0.000804 | 1243.83 |    0.00 | 305.89

最佳解详细信息:
运行ID: 9
适应度: 0.000926
总成本: 1079.46
惩罚值: 0.00

最佳解染色体:
染色体 1: (0, 79, 48, 43, 41, 10, 38, 13, 70, 78, 46, 60, 47, 59, 69, 15, 18, 22, 52, 66, 20, 54, 0, 64, 73, 74, 76, 44, 50, 67, 68, 51, 61, 71, 40, 28, 24, 27, 25, 23, 26, 21, 75, 77, 56, 0, 3, 7, 37, 5, 2, 1, 6, 4, 63, 58, 62, 11, 8, 9, 12, 14, 17, 19, 39, 16, 45, 53, 0, 72, 55, 80, 49, 34, 35, 36, 32, 33, 31, 29, 30, 57, 42, 65, 0)
染色体 2: (0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 0, 0)

最佳解路线详情:
路线 1: [0, 79, 41, 10, 38, 13, 70, 78, 46, 59, 15, 52, 54, 0]
  无人机任务:
    从节点 79 发射无人机访问: [48, 43]
    从节点 46 发射无人机访问: [60, 47]
    从节点 59 发射无人机访问: [69]
    从节点 15 发射无人机访问: [18, 22]
    从节点 52 发射无人机访问: [66, 20]
路线 2: [0, 64, 73, 74, 76, 67, 68, 51, 40, 28, 25, 23, 75, 0]
  无人机任务:
    从节点 76 发射无人机访问: [44, 50]
    从节点 51 发射无人机访问: [61, 71]
    从节点 28 发射无人机访问: [24, 27]
    从节点 23 发射无人机访问: [26, 21]
    从节点 75 发射无人机访问: [77, 56]
路线 3: [0, 3, 5, 6, 63, 58, 11, 12, 14, 39, 16, 45, 53, 0]
  无人机任务:
    从节点 3 发射无人机访问: [7, 37]
    从节点 5 发射无人机访问: [2, 1]
    从节点 6 发射无人机访问: [4]
    从节点 58 发射无人机访问: [62]
    从节点 11 发射无人机访问: [8, 9]
    从节点 14 发射无人机访问: [17, 19]
路线 4: [0, 72, 55, 80, 49, 34, 32, 31, 57, 42, 65, 0]
  无人机任务:
    从节点 34 发射无人机访问: [35, 36]
    从节点 32 发射无人机访问: [33]
    从节点 31 发射无人机访问: [29, 30]

收敛历史数据 (每10代):
代数 | 最佳适应度 | 最佳成本 | 惩罚值
--------------------------------------------------
   0 | 0.000482 | 2074.41 |    0.00
  10 | 0.000482 | 2074.41 |    0.00
  20 | 0.000482 | 2074.41 |    0.00
  30 | 0.000482 | 2074.41 |    0.00
  40 | 0.000482 | 2074.41 |    0.00
  50 | 0.000489 | 2045.14 |    0.00
  60 | 0.000508 | 1969.99 |    0.00
  70 | 0.000508 | 1969.99 |    0.00
  80 | 0.000520 | 1924.72 |    0.00
  90 | 0.000520 | 1924.72 |    0.00
 100 | 0.000629 | 1589.58 |    0.00
 110 | 0.000629 | 1589.58 |    0.00
 120 | 0.000633 | 1580.88 |    0.00
 130 | 0.000720 | 1389.05 |    0.00
 140 | 0.000720 | 1389.05 |    0.00
 150 | 0.000727 | 1375.89 |    0.00
 160 | 0.000727 | 1375.89 |    0.00
 170 | 0.000728 | 1372.90 |    0.00
 180 | 0.000786 | 1273.05 |    0.00
 190 | 0.000791 | 1263.51 |    0.00
 200 | 0.000868 | 1151.46 |    0.00
 210 | 0.000869 | 1150.54 |    0.00
 220 | 0.000876 | 1141.95 |    0.00
 230 | 0.000877 | 1139.91 |    0.00
 240 | 0.000877 | 1139.91 |    0.00
 250 | 0.000877 | 1139.91 |    0.00
 260 | 0.000877 | 1139.91 |    0.00
 270 | 0.000877 | 1139.91 |    0.00
 280 | 0.000877 | 1139.91 |    0.00
 290 | 0.000877 | 1139.91 |    0.00
 300 | 0.000877 | 1139.91 |    0.00
 310 | 0.000877 | 1139.91 |    0.00
 320 | 0.000877 | 1139.91 |    0.00
 330 | 0.000877 | 1139.91 |    0.00
 340 | 0.000877 | 1139.91 |    0.00
 350 | 0.000877 | 1139.91 |    0.00
 360 | 0.000877 | 1139.91 |    0.00
 370 | 0.000877 | 1139.91 |    0.00
 380 | 0.000890 | 1123.57 |    0.00
 390 | 0.000894 | 1118.54 |    0.00
 400 | 0.000900 | 1111.52 |    0.00
 410 | 0.000920 | 1086.91 |    0.00
 420 | 0.000925 | 1081.03 |    0.00
 430 | 0.000925 | 1081.03 |    0.00
 440 | 0.000925 | 1081.03 |    0.00
 450 | 0.000925 | 1081.03 |    0.00
 460 | 0.000926 | 1079.46 |    0.00
