from data_loader import Individual, Problem
from repair import check_and_repair_drone_task_limit
import utils

def simple_test():
    """简化版测试，确保修复函数正常工作"""
    print("开始简化版测试...")
    
    # 创建简单的模拟问题实例
    mock_problem = type('MockProblem', (), {
        'num_drones': 2,  # 每个发射点最多2个无人机
        'drone_params': {
            'max_load': 5.0,
            'mass': 4.0,
            'speed': 50.0,
            'energy_consumption_rate': 50.0,
            'battery': 200.0
        },
        'locations': {
            0: (0, 0),    # 配送中心
            1: (1, 1),    # 客户点1
            2: (10, 1),   # 客户点2
            3: (2, 1),    # 客户点3
            4: (1, 2),    # 客户点4
            5: (8, 1),    # 客户点5
            6: (3, 3),    # 客户点6
            7: (5, 5),    # 客户点7
            8: (6, 6),    # 客户点8
            9: (7, 7),    # 客户点9
            10: (8, 8),   # 客户点10
            11: (9, 9),   # 客户点11
            12: (10, 10), # 客户点12
            13: (11, 11), # 客户点13
        },
        'demands': {i: (1.0, 1.0) for i in range(14)}
    })()
    
    # 简单计算能耗函数
    def calc_energy(problem, launch, target):
        x1, y1 = problem.locations[launch]
        x2, y2 = problem.locations[target]
        dist = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
        return dist * 10  # 简单的能耗计算
    
    # 替换能耗计算函数
    utils.calc_drone_energy = lambda p, l, t: calc_energy(mock_problem, l, t)
    
    # 创建一个简单的不可行解
    # 旧的测试染色体(保留为注释)
    # path_layer    = [0, 5, 10, 9, 1, 12, 0, 7, 8, 3, 2, 11, 4, 6, 13, 0]
    # service_layer = [0, 0, 1,  1, 1, 1,  0, 0, 1, 0, 1, 1,  0, 1, 1,  0]  
    
    # 新的测试染色体
    path_layer    = [0, 0, 2, 8, 7, 5, 1, 3, 9, 6, 10, 4, 0]
    service_layer = [0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0]
    
    individual = Individual(chromosomes=(path_layer, service_layer))
    
    # 打印初始状态
    print("\n修复前:")
    print("路径层:", path_layer)
    print("服务方式层:", service_layer)
    print("预期：客户点2、8由无人机服务，但无前置发射点；客户点9、10由无人机服务，超过限制")
    
    # 计算初始能耗
    print("\n客户点能耗:")
    for node in [2, 8, 9, 10]:
        # 对于无人机客户点2、8，找不到前置发射点，因此按推测使用路径中一个可能的卡车服务点计算
        possible_launch = 7 if node in [2, 8] else 6
        energy = calc_energy(mock_problem, possible_launch, node)
        print(f"假设发射点{possible_launch}到客户点{node}的能耗: {energy}")
    
    # 调用修复函数
    print("\n调用修复函数...")
    result = check_and_repair_drone_task_limit(mock_problem, individual)
    
    # 打印修复结果
    path_after, service_after = individual.chromosomes
    print("\n修复后:")
    print("路径层:", path_after)
    print("服务方式层:", service_after)
    print("修复结果:", "可行" if result else "已修复不可行解")
    
    # 修正的任务分配分析逻辑
    drone_service_nodes = [(i, node) for i, (node, service) in enumerate(zip(path_after, service_after)) 
                          if service == 1 and node != 0]
    
    launch_tasks = {}
    for i, node in drone_service_nodes:
        # 向前查找最近的卡车服务点作为发射点
        launch_node = None
        for j in range(i-1, -1, -1):
            if path_after[j] != 0 and service_after[j] == 0:
                launch_node = path_after[j]
                break
        
        if launch_node:
            if launch_node not in launch_tasks:
                launch_tasks[launch_node] = []
            launch_tasks[launch_node].append(node)
    
    print("\n分析修复后的任务分配:")
    for launch, tasks in launch_tasks.items():
        print(f"发射点{launch}控制的客户点: {tasks} (共{len(tasks)}个)")
    
    # 验证是否符合限制
    all_valid = all(len(tasks) <= mock_problem.num_drones for tasks in launch_tasks.values())
    print("\n最终验证:", "符合限制" if all_valid else "仍然不符合限制")

if __name__ == "__main__":
    simple_test()
