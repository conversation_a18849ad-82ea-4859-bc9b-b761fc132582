NAME : C101-61-3
COMMENT : (<PERSON> dataset, Modified for VRPD with scaled coordinates)
TYPE : VRPD
DIMENSION : 61
EDGE_WEIGHT_TYPE : EUC_2D
NUM_CUSTOMERS : 60

NODE_COORD_SECTION
 0 10.0 12.5
 1 11.2 17.0
 2 11.2 17.5
 3 10.5 16.5
 4 10.5 17.0
 5 10.5 16.2
 6 10.0 17.2
 7 8.8 16.5
 8 5.5 18.8
 9 4.5 18.8
 10 3.8 18.8
 11 7.5 12.5
 12 7.5 13.0
 13 7.0 13.0
 14 7.0 13.8
 15 6.2 12.5
 16 6.2 13.0
 17 6.2 13.8
 18 5.8 13.0
 19 5.8 13.8
 20 5.0 12.5
 21 5.0 13.8
 22 2.5 10.0
 23 2.0 11.2
 24 1.2 11.2
 25 8.8 7.5
 26 8.8 8.0
 27 8.2 8.0
 28 8.2 8.8
 29 8.0 7.5
 30 7.5 7.5
 31 7.5 8.0
 32 7.5 8.8
 33 7.0 7.5
 34 7.0 8.8
 35 10.5 3.8
 36 10.0 3.8
 37 9.5 3.8
 38 12.5 7.5
 39 12.5 8.8
 40 12.0 7.5
 41 11.8 8.8
 42 11.2 7.5
 43 11.2 8.8
 44 23.0 7.5
 45 11.2 16.2
 46 22.5 8.8
 47 22.0 7.5
 48 22.0 8.8
 49 21.8 7.5
 50 21.2 8.8
 51 17.0 15.0
 52 16.2 13.8
 53 16.2 15.0
 54 15.8 14.5
 55 15.0 13.8
 56 15.0 15.0
 57 15.5 20.0
 58 15.0 20.0
 59 14.5 18.8
 60 13.8 20.0

DEMAND_SECTION
 0 0 0      
 1 4.5 0.0      // 仅送货需求客户 - light包裹
 2 3.4 0.0      // 仅送货需求客户 - light包裹
 3 0.0 1.7      // 仅取货需求客户 - light包裹
 4 3.5 0.0      // 仅送货需求客户 - light包裹
 5 8.6 0.0      // 仅送货需求客户 - medium包裹
 6 2.5 0.0      // 仅送货需求客户 - light包裹
 7 4.9 6.5      // 送货取货双需求客户 - light包裹
 8 4.1 0.0      // 仅送货需求客户 - light包裹
 9 0.0 2.0      // 仅取货需求客户 - light包裹
 10 17.9 9.1      // 送货取货双需求客户 - heavy包裹
 11 3.7 6.1      // 送货取货双需求客户 - light包裹
 12 0.0 4.1      // 仅取货需求客户 - light包裹
 13 3.0 0.0      // 仅送货需求客户 - light包裹
 14 1.6 0.0      // 仅送货需求客户 - light包裹
 15 3.4 0.0      // 仅送货需求客户 - light包裹
 16 5.4 5.3      // 送货取货双需求客户 - medium包裹
 17 4.7 7.4      // 送货取货双需求客户 - light包裹
 18 1.5 0.0      // 仅送货需求客户 - light包裹
 19 1.6 5.0      // 送货取货双需求客户 - light包裹
 20 4.3 0.0      // 仅送货需求客户 - light包裹
 21 0.0 4.6      // 仅取货需求客户 - light包裹
 22 9.4 0.0      // 仅送货需求客户 - medium包裹
 23 7.1 0.0      // 仅送货需求客户 - medium包裹
 24 2.5 4.5      // 送货取货双需求客户 - light包裹
 25 1.9 0.0      // 仅送货需求客户 - light包裹
 26 1.4 7.4      // 送货取货双需求客户 - light包裹
 27 1.6 0.0      // 仅送货需求客户 - light包裹
 28 0.0 4.7      // 仅取货需求客户 - light包裹
 29 4.3 7.8      // 送货取货双需求客户 - light包裹
 30 3.5 0.0      // 仅送货需求客户 - light包裹
 31 5.2 0.0      // 仅送货需求客户 - medium包裹
 32 1.6 9.5      // 送货取货双需求客户 - light包裹
 33 2.7 0.0      // 仅送货需求客户 - light包裹
 34 2.0 0.0      // 仅送货需求客户 - light包裹
 35 2.0 0.0      // 仅送货需求客户 - light包裹
 36 3.5 0.0      // 仅送货需求客户 - light包裹
 37 4.3 5.5      // 送货取货双需求客户 - light包裹
 38 3.5 0.0      // 仅送货需求客户 - light包裹
 39 3.9 3.6      // 送货取货双需求客户 - light包裹
 40 0.0 2.4      // 仅取货需求客户 - light包裹
 41 1.7 0.0      // 仅送货需求客户 - light包裹
 42 0.0 3.1      // 仅取货需求客户 - light包裹
 43 0.0 4.8      // 仅取货需求客户 - light包裹
 44 4.1 0.0      // 仅送货需求客户 - light包裹
 45 1.1 8.9      // 送货取货双需求客户 - light包裹
 46 2.5 0.0      // 仅送货需求客户 - light包裹
 47 4.0 4.2      // 送货取货双需求客户 - light包裹
 48 0.0 2.0      // 仅取货需求客户 - light包裹
 49 1.4 0.0      // 仅送货需求客户 - light包裹
 50 4.0 0.0      // 仅送货需求客户 - light包裹
 51 0.0 2.4      // 仅取货需求客户 - light包裹
 52 3.6 8.5      // 送货取货双需求客户 - light包裹
 53 4.9 0.0      // 仅送货需求客户 - light包裹
 54 1.7 0.0      // 仅送货需求客户 - light包裹
 55 3.9 0.0      // 仅送货需求客户 - light包裹
 56 0.0 5.3      // 仅取货需求客户 - medium包裹
 57 4.2 0.0      // 仅送货需求客户 - light包裹
 58 6.6 0.0      // 仅送货需求客户 - medium包裹
 59 2.6 0.0      // 仅送货需求客户 - light包裹
 60 0.0 7.4      // 仅取货需求客户 - medium包裹
 1 10 0      
 2 30 0      
 3 10 0      
 4 10 0      
 5 10 0      
 6 20 0      
 7 10 0      
 8 30 0      
 9 20 0      
 10 20 0      
 11 10 0      
 12 20 0      
 13 20 0      
 14 10 0      
 15 10 0      
 16 40 0      
 17 10 0      
 18 10 0      
 19 20 0      
 20 10 0      
 21 10 0      
 22 30 0      
 23 20 0      
 24 10 0      
 25 10 0      
 26 10 0      
 27 20 0      
 28 10 0      
 29 10 0      
 30 10 0      
 31 30 0      
 32 10 0      
 33 10 0      
 34 10 0      
 35 10 0      
 36 40 0      
 37 10 0      
 38 10 0      
 39 20 0      
 40 10 0      
 41 10 0      
 42 10 0      
 43 10 0      
 44 10 0      
 45 20 0      
 46 10 0      
 47 10 0      
 48 20 0      
 49 10 0      
 50 30 0      
 51 30 0      
 52 20 0      
 53 30 0      
 54 10 0      
 55 10 0      
 56 10 0      
 57 30 0      
 58 10 0      
 59 20 0      
 60 10 0      

DEPOT_SECTION
 0
-1
EOF