import main
import time
import os

def run_test():
    print("优化后的差分进化算法性能测试")
    print("-" * 50)
    
    # 设置测试问题
    problem_file = 'A-n32-k2-d4.vrp'
    print(f"测试问题: {problem_file}")
    
    # 加载问题
    start_time = time.time()
    problem = main.Problem(problem_file)
    print(f"问题规模: {len(problem.customers)} 客户点")
    
    # 运行算法
    print("\n开始求解...")
    best_solution, history = main.solve_vrpd(
        problem, 
        random_seed=42, 
        pop_size=50, 
        max_generations=50
    )
    
    # 计算总耗时
    total_time = time.time() - start_time
    print(f"\n求解完成, 总耗时: {total_time:.2f}s")
    
    # 输出结果
    print(f"最佳适应度: {best_solution.fitness:.6f}")
    print(f"最佳总成本: {best_solution.total_cost:.2f}")
    print(f"惩罚值: {best_solution.penalty:.2f}")
    
    # 输出简要路径信息
    print("\n最优解路径信息:")
    decoded_solution = main.Individual.decode(best_solution.chromosomes, problem)
    for i, (route, drone_tasks) in enumerate(decoded_solution):
        print(f"  车辆 {i+1} 路径: {route}")
        if drone_tasks:
            print(f"    无人机任务:")
            for launch, targets in drone_tasks.items():
                print(f"      发射点 {launch} → 任务: {targets}")
        else:
            print(f"    无无人机任务")

if __name__ == "__main__":
    run_test()
